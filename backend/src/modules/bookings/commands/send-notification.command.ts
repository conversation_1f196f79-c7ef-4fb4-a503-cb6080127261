import { Command } from '../../../common/commands/base.command';

export enum NotificationType {
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  PUSH = 'PUSH'
}

export class SendNotificationCommand extends Command {
  constructor(
    public readonly recipient: string,
    public readonly type: NotificationType,
    public readonly subject: string,
    public readonly message: string,
    public readonly templateData?: any
  ) {
    super();
  }
}
