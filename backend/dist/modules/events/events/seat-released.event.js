"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeatReleasedEvent = void 0;
const base_event_1 = require("../../../common/events/base.event");
class SeatReleasedEvent extends base_event_1.DomainEvent {
    constructor(aggregateId, seatIds, version = 1) {
        super(aggregateId, version);
        this.aggregateId = aggregateId;
        this.seatIds = seatIds;
    }
}
exports.SeatReleasedEvent = SeatReleasedEvent;
//# sourceMappingURL=seat-released.event.js.map