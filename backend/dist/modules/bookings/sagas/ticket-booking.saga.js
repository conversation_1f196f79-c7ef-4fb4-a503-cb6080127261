"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TicketBookingSaga = void 0;
const common_1 = require("@nestjs/common");
const cqrs_1 = require("@nestjs/cqrs");
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
const base_saga_1 = require("../../../common/sagas/base.saga");
const booking_initiated_event_1 = require("../events/booking-initiated.event");
const seats_reserved_event_1 = require("../events/seats-reserved.event");
const payment_processed_event_1 = require("../events/payment-processed.event");
const booking_confirmed_event_1 = require("../events/booking-confirmed.event");
const booking_cancelled_event_1 = require("../events/booking-cancelled.event");
const booking_expired_event_1 = require("../events/booking-expired.event");
const reserve_seats_command_1 = require("../commands/reserve-seats.command");
const confirm_booking_command_1 = require("../commands/confirm-booking.command");
const cancel_booking_command_1 = require("../commands/cancel-booking.command");
const send_notification_command_1 = require("../commands/send-notification.command");
let TicketBookingSaga = class TicketBookingSaga extends base_saga_1.BaseSaga {
    constructor() {
        super(...arguments);
        this.bookingInitiated = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)(booking_initiated_event_1.BookingInitiatedEvent), (0, operators_1.map)((event) => {
                console.log(`🎫 Booking Saga: Booking initiated for ${event.aggregateId}`);
                this.addStep({
                    stepId: 'reserve-seats',
                    command: new reserve_seats_command_1.ReserveSeatsCommand(event.aggregateId, event.bookingData.eventId, event.bookingData.seatIds, 15),
                    compensationCommand: new cancel_booking_command_1.CancelBookingCommand(event.aggregateId, 'Seat reservation failed'),
                    maxRetries: 3
                });
                this.startSaga();
                return this.steps[0].command;
            }));
        };
        this.seatsReserved = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)(seats_reserved_event_1.SeatsReservedEvent), (0, operators_1.map)((event) => {
                console.log(`🪑 Booking Saga: Seats reserved for booking ${event.aggregateId}`);
                this.addStep({
                    stepId: 'send-reservation-notification',
                    command: new send_notification_command_1.SendNotificationCommand(event.aggregateId, send_notification_command_1.NotificationType.EMAIL, 'Seats Reserved - Complete Your Booking', 'Your seats have been reserved. Please complete payment within 15 minutes.', {
                        bookingId: event.aggregateId,
                        eventId: event.eventId,
                        seatIds: event.seatIds,
                        expiresAt: event.expiresAt
                    })
                });
                return this.steps[this.steps.length - 1].command;
            }));
        };
        this.paymentProcessed = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)(payment_processed_event_1.PaymentProcessedEvent), (0, operators_1.map)((event) => {
                console.log(`💳 Booking Saga: Payment processed for booking ${event.aggregateId}`);
                this.addStep({
                    stepId: 'confirm-booking',
                    command: new confirm_booking_command_1.ConfirmBookingCommand(event.aggregateId),
                    compensationCommand: new cancel_booking_command_1.CancelBookingCommand(event.aggregateId, 'Booking confirmation failed'),
                    maxRetries: 3
                });
                return this.steps[this.steps.length - 1].command;
            }));
        };
        this.bookingConfirmed = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)(booking_confirmed_event_1.BookingConfirmedEvent), (0, operators_1.map)((event) => {
                console.log(`✅ Booking Saga: Booking confirmed ${event.aggregateId}`);
                this.addStep({
                    stepId: 'send-confirmation-notification',
                    command: new send_notification_command_1.SendNotificationCommand(event.aggregateId, send_notification_command_1.NotificationType.EMAIL, 'Booking Confirmed - Your Tickets Are Ready!', 'Your booking has been confirmed. Your tickets are attached.', {
                        bookingId: event.aggregateId,
                        confirmedAt: event.confirmedAt
                    })
                });
                this.addStep({
                    stepId: 'generate-tickets',
                    command: new send_notification_command_1.SendNotificationCommand(event.aggregateId, send_notification_command_1.NotificationType.EMAIL, 'Your Digital Tickets', 'Please find your digital tickets attached.', {
                        bookingId: event.aggregateId,
                        generateTickets: true
                    })
                });
                console.log(`🎉 Booking Saga: Completed successfully for ${event.aggregateId}`);
                return this.steps[this.steps.length - 2].command;
            }));
        };
        this.bookingCancelled = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)(booking_cancelled_event_1.BookingCancelledEvent), (0, operators_1.map)((event) => {
                console.log(`❌ Booking Saga: Booking cancelled ${event.aggregateId}`);
                return new send_notification_command_1.SendNotificationCommand(event.aggregateId, send_notification_command_1.NotificationType.EMAIL, 'Booking Cancelled', `Your booking has been cancelled. ${event.reason || ''}`, {
                    bookingId: event.aggregateId,
                    reason: event.reason,
                    cancelledAt: event.cancelledAt
                });
            }));
        };
        this.bookingExpired = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)(booking_expired_event_1.BookingExpiredEvent), (0, operators_1.delay)(1000), (0, operators_1.map)((event) => {
                console.log(`⏰ Booking Saga: Booking expired ${event.aggregateId}`);
                return new send_notification_command_1.SendNotificationCommand(event.aggregateId, send_notification_command_1.NotificationType.EMAIL, 'Booking Expired', 'Your seat reservation has expired. Please try booking again.', {
                    bookingId: event.aggregateId,
                    expiredAt: event.expiredAt
                });
            }));
        };
    }
    handle(event) {
        switch (event.constructor.name) {
            case booking_initiated_event_1.BookingInitiatedEvent.name:
                return this.bookingInitiated(new rxjs_1.Observable(observer => observer.next(event)));
            case seats_reserved_event_1.SeatsReservedEvent.name:
                return this.seatsReserved(new rxjs_1.Observable(observer => observer.next(event)));
            case payment_processed_event_1.PaymentProcessedEvent.name:
                return this.paymentProcessed(new rxjs_1.Observable(observer => observer.next(event)));
            case booking_confirmed_event_1.BookingConfirmedEvent.name:
                return this.bookingConfirmed(new rxjs_1.Observable(observer => observer.next(event)));
            case booking_cancelled_event_1.BookingCancelledEvent.name:
                return this.bookingCancelled(new rxjs_1.Observable(observer => observer.next(event)));
            case booking_expired_event_1.BookingExpiredEvent.name:
                return this.bookingExpired(new rxjs_1.Observable(observer => observer.next(event)));
            default:
                return new rxjs_1.Observable(observer => observer.complete());
        }
    }
};
exports.TicketBookingSaga = TicketBookingSaga;
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], TicketBookingSaga.prototype, "bookingInitiated", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], TicketBookingSaga.prototype, "seatsReserved", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], TicketBookingSaga.prototype, "paymentProcessed", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], TicketBookingSaga.prototype, "bookingConfirmed", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], TicketBookingSaga.prototype, "bookingCancelled", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], TicketBookingSaga.prototype, "bookingExpired", void 0);
exports.TicketBookingSaga = TicketBookingSaga = __decorate([
    (0, common_1.Injectable)()
], TicketBookingSaga);
//# sourceMappingURL=ticket-booking.saga.js.map