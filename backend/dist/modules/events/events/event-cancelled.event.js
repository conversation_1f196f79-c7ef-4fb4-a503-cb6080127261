"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventCancelledEvent = void 0;
const base_event_1 = require("../../../common/events/base.event");
class EventCancelledEvent extends base_event_1.DomainEvent {
    constructor(aggregateId, version = 1) {
        super(aggregateId, version);
        this.aggregateId = aggregateId;
    }
}
exports.EventCancelledEvent = EventCancelledEvent;
//# sourceMappingURL=event-cancelled.event.js.map