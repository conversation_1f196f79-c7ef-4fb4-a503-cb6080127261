# AWS Free Tier Configuration
# This configuration is optimized to stay within AWS Free Tier limits

# Basic Configuration
aws_region   = "us-east-1"  # Free tier is best in us-east-1
environment  = "dev"
project_name = "ticket-booking"
domain_name  = "your-domain.com"  # Update with your domain

# Networking - Minimal setup
vpc_cidr = "10.0.0.0/16"

# Database - Free Tier: 750 hours of db.t3.micro
db_instance_class = "db.t3.micro"
db_name          = "ticket_booking"
db_username      = "postgres"

# Redis - Free Tier: 750 hours of cache.t2.micro
redis_node_type = "cache.t2.micro"

# Kafka - Use containerized version (MSK is not free)
use_msk = false

# ECS Services - Free Tier: 750 hours of t2.micro equivalent
# Total: 256 CPU + 512 Memory per service = within free tier limits
backend_desired_count  = 1
frontend_desired_count = 1

backend_cpu    = 256  # 0.25 vCPU
backend_memory = 512  # 512 MB

frontend_cpu    = 256  # 0.25 vCPU  
frontend_memory = 512  # 512 MB

# Container Images - Update these with your ECR repository URLs
backend_image  = "your-account.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-backend:latest"
frontend_image = "your-account.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-frontend:latest"

# SSL Certificate - Leave empty for HTTP only (HTTPS requires ACM certificate)
ssl_certificate_arn = ""

# Secrets
jwt_secret = "your-super-secret-jwt-key-change-this-in-production"

# Free Tier Limits Summary:
# ========================
# EC2: 750 hours/month of t2.micro (covered by ECS Fargate free tier)
# RDS: 750 hours/month of db.t3.micro + 20GB storage
# ElastiCache: 750 hours/month of cache.t2.micro
# ECS: 750 hours/month of Fargate vCPU + 1.5GB memory
# ALB: 750 hours/month + 15 LCU hours
# NAT Gateway: NOT FREE (costs ~$45/month) - consider removing for dev
# VPC: Free (5 VPCs per region)
# CloudWatch: 10 custom metrics, 5GB log ingestion
# S3: 5GB storage, 20K GET, 2K PUT requests
