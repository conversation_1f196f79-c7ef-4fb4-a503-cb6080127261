#!/bin/bash

# API Testing Script
# Tests all major API endpoints to ensure they're working

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

BASE_URL="http://localhost:3001"
TOKEN=""

echo -e "${BLUE}🧪 API Testing Suite${NC}"
echo -e "${BLUE}===================${NC}"

# Helper function to test endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    local auth_header=""
    
    if [ ! -z "$TOKEN" ]; then
        auth_header="-H \"Authorization: Bearer $TOKEN\""
    fi
    
    echo -e "\n${YELLOW}Testing: $description${NC}"
    echo -e "${BLUE}$method $endpoint${NC}"
    
    if [ "$method" = "GET" ]; then
        if eval "curl -s -w \"%{http_code}\" -o /tmp/response.json $auth_header \"$BASE_URL$endpoint\"" | grep -q "200"; then
            echo -e "${GREEN}✅ Success${NC}"
            return 0
        else
            echo -e "${RED}❌ Failed${NC}"
            return 1
        fi
    elif [ "$method" = "POST" ]; then
        if eval "curl -s -w \"%{http_code}\" -o /tmp/response.json -X POST $auth_header -H \"Content-Type: application/json\" -d '$data' \"$BASE_URL$endpoint\"" | grep -q "20[01]"; then
            echo -e "${GREEN}✅ Success${NC}"
            return 0
        else
            echo -e "${RED}❌ Failed${NC}"
            cat /tmp/response.json 2>/dev/null || echo "No response body"
            return 1
        fi
    fi
}

# Test 1: Health Check
echo -e "\n${BLUE}🏥 Test 1: Health Checks${NC}"

test_endpoint "GET" "/api/v1/health" "" "General health check"
test_endpoint "GET" "/api/v1/health/db" "" "Database health check"
test_endpoint "GET" "/api/v1/health/redis" "" "Redis health check"

# Test 2: Events API (Public)
echo -e "\n${BLUE}🎭 Test 2: Events API${NC}"

test_endpoint "GET" "/api/v1/events" "" "Get all events"
test_endpoint "GET" "/api/v1/events/search?query=concert" "" "Search events"

# Test 3: User Registration
echo -e "\n${BLUE}👤 Test 3: User Management${NC}"

# Generate random email for testing
RANDOM_EMAIL="test$(date +%s)@example.com"

REGISTER_DATA='{
  "email": "'$RANDOM_EMAIL'",
  "password": "password123",
  "firstName": "Test",
  "lastName": "User"
}'

if test_endpoint "POST" "/api/v1/auth/register" "$REGISTER_DATA" "User registration"; then
    echo -e "${GREEN}✅ User registered successfully${NC}"
    
    # Test 4: User Login
    echo -e "\n${BLUE}🔐 Test 4: Authentication${NC}"
    
    LOGIN_DATA='{
      "email": "'$RANDOM_EMAIL'",
      "password": "password123"
    }'
    
    if test_endpoint "POST" "/api/v1/auth/login" "$LOGIN_DATA" "User login"; then
        # Extract token from response
        TOKEN=$(cat /tmp/response.json | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
        if [ ! -z "$TOKEN" ]; then
            echo -e "${GREEN}✅ Login successful, token received${NC}"
            
            # Test 5: Protected Endpoints
            echo -e "\n${BLUE}🔒 Test 5: Protected Endpoints${NC}"
            
            test_endpoint "GET" "/api/v1/auth/profile" "" "Get user profile"
            
            # Test 6: Create Event (if user has permission)
            echo -e "\n${BLUE}🎪 Test 6: Event Creation${NC}"
            
            EVENT_DATA='{
              "title": "Test Event API",
              "description": "A test event created via API",
              "category": "music",
              "venue": "Test Venue",
              "date": "2024-12-31T20:00:00Z",
              "price": 50,
              "totalSeats": 100
            }'
            
            test_endpoint "POST" "/api/v1/events" "$EVENT_DATA" "Create new event"
            
        else
            echo -e "${RED}❌ No token received from login${NC}"
        fi
    else
        echo -e "${RED}❌ Login failed${NC}"
    fi
else
    echo -e "${RED}❌ User registration failed${NC}"
fi

# Test 7: AI Endpoints (if available)
echo -e "\n${BLUE}🤖 Test 7: AI Features${NC}"

if [ ! -z "$TOKEN" ]; then
    # Test AI health
    if test_endpoint "GET" "/api/v1/ai/health" "" "AI services health"; then
        echo -e "${GREEN}✅ AI services are available${NC}"
        
        # Test semantic search
        SEARCH_DATA='{
          "query": "rock concert music",
          "topK": 5
        }'
        
        test_endpoint "POST" "/api/v1/ai/search/events" "$SEARCH_DATA" "AI semantic search"
        
    else
        echo -e "${YELLOW}⚠️  AI services not available (Pinecone not configured)${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  Skipping AI tests (no authentication token)${NC}"
fi

# Test 8: WebSocket Connection
echo -e "\n${BLUE}🔌 Test 8: WebSocket Connection${NC}"

if command -v wscat &> /dev/null; then
    echo -e "${YELLOW}Testing WebSocket connection...${NC}"
    timeout 5 wscat -c ws://localhost:3001/socket.io/ && echo -e "${GREEN}✅ WebSocket connection successful${NC}" || echo -e "${YELLOW}⚠️  WebSocket test skipped (connection timeout)${NC}"
else
    echo -e "${YELLOW}⚠️  WebSocket test skipped (wscat not installed)${NC}"
    echo -e "${YELLOW}   Install with: npm install -g wscat${NC}"
fi

# Test 9: API Documentation
echo -e "\n${BLUE}📚 Test 9: API Documentation${NC}"

if curl -s http://localhost:3001/api/docs > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Swagger documentation is available at http://localhost:3001/api/docs${NC}"
else
    echo -e "${YELLOW}⚠️  API documentation not available${NC}"
fi

# Test 10: Performance Test
echo -e "\n${BLUE}⚡ Test 10: Basic Performance${NC}"

echo -e "${YELLOW}Testing response time for events endpoint...${NC}"
RESPONSE_TIME=$(curl -s -w "%{time_total}" -o /dev/null http://localhost:3001/api/v1/events)
echo -e "${GREEN}✅ Response time: ${RESPONSE_TIME}s${NC}"

if (( $(echo "$RESPONSE_TIME < 1.0" | bc -l) )); then
    echo -e "${GREEN}✅ Good performance (< 1s)${NC}"
else
    echo -e "${YELLOW}⚠️  Slow response (> 1s)${NC}"
fi

# Summary
echo -e "\n${BLUE}📊 Test Summary${NC}"
echo -e "${BLUE}===============${NC}"

# Count successful tests (this is a simplified count)
echo -e "${GREEN}✅ Basic API functionality tested${NC}"
echo -e "${GREEN}✅ Authentication flow tested${NC}"
echo -e "${GREEN}✅ Database connectivity verified${NC}"

if [ ! -z "$TOKEN" ]; then
    echo -e "${GREEN}✅ Protected endpoints accessible${NC}"
else
    echo -e "${YELLOW}⚠️  Some protected endpoints not tested${NC}"
fi

echo -e "\n${BLUE}🎯 Next Steps:${NC}"
echo -e "${GREEN}• Visit frontend: http://localhost:3000${NC}"
echo -e "${GREEN}• View API docs: http://localhost:3001/api/docs${NC}"
echo -e "${GREEN}• Test in browser or Postman${NC}"

echo -e "\n${BLUE}🔧 Useful API Endpoints:${NC}"
echo -e "${GREEN}• Health: GET /api/v1/health${NC}"
echo -e "${GREEN}• Events: GET /api/v1/events${NC}"
echo -e "${GREEN}• Register: POST /api/v1/auth/register${NC}"
echo -e "${GREEN}• Login: POST /api/v1/auth/login${NC}"
echo -e "${GREEN}• Search: GET /api/v1/events/search?query=concert${NC}"

# Cleanup
rm -f /tmp/response.json

echo -e "\n${GREEN}🎉 API testing completed!${NC}"
