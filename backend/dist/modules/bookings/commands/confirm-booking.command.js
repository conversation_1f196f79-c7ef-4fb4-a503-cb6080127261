"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfirmBookingCommand = void 0;
const base_command_1 = require("../../../common/commands/base.command");
class ConfirmBookingCommand extends base_command_1.Command {
    constructor(bookingId) {
        super();
        this.bookingId = bookingId;
    }
}
exports.ConfirmBookingCommand = ConfirmBookingCommand;
//# sourceMappingURL=confirm-booking.command.js.map