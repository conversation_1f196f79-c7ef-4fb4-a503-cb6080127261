"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventStoreService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const cqrs_1 = require("@nestjs/cqrs");
const event_store_entity_1 = require("../entities/event-store.entity");
const kafka_config_1 = require("../../config/kafka.config");
let EventStoreService = class EventStoreService {
    constructor(eventStoreRepository, snapshotRepository, eventBus, kafkaConfig) {
        this.eventStoreRepository = eventStoreRepository;
        this.snapshotRepository = snapshotRepository;
        this.eventBus = eventBus;
        this.kafkaConfig = kafkaConfig;
    }
    async saveEvents(aggregateId, events, expectedVersion) {
        const queryRunner = this.eventStoreRepository.manager.connection.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const lastEvent = await queryRunner.manager
                .createQueryBuilder(event_store_entity_1.EventStoreEntity, 'event')
                .where('event.aggregateId = :aggregateId', { aggregateId })
                .orderBy('event.version', 'DESC')
                .limit(1)
                .getOne();
            const currentVersion = lastEvent ? lastEvent.version : 0;
            if (currentVersion !== expectedVersion) {
                throw new common_1.ConflictException(`Concurrency conflict. Expected version ${expectedVersion}, but current version is ${currentVersion}`);
            }
            for (let i = 0; i < events.length; i++) {
                const event = events[i];
                const eventEntity = new event_store_entity_1.EventStoreEntity();
                eventEntity.aggregateId = aggregateId;
                eventEntity.eventId = event.eventId;
                eventEntity.eventType = event.eventType;
                eventEntity.eventData = event;
                eventEntity.eventMetadata = {
                    correlationId: event.eventId,
                    causationId: event.eventId,
                };
                eventEntity.version = expectedVersion + i + 1;
                await queryRunner.manager.save(eventEntity);
            }
            await queryRunner.commitTransaction();
            for (const event of events) {
                this.eventBus.publish(event);
                await this.kafkaConfig.publishEvent('domain-events', event);
            }
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async getEvents(aggregateId, fromVersion) {
        const query = this.eventStoreRepository
            .createQueryBuilder('event')
            .where('event.aggregateId = :aggregateId', { aggregateId })
            .orderBy('event.version', 'ASC');
        if (fromVersion !== undefined) {
            query.andWhere('event.version > :fromVersion', { fromVersion });
        }
        const eventEntities = await query.getMany();
        return eventEntities.map(entity => this.deserializeEvent(entity));
    }
    async getAllEvents(fromTimestamp) {
        const query = this.eventStoreRepository
            .createQueryBuilder('event')
            .orderBy('event.timestamp', 'ASC');
        if (fromTimestamp) {
            query.where('event.timestamp >= :fromTimestamp', { fromTimestamp });
        }
        const eventEntities = await query.getMany();
        return eventEntities.map(entity => this.deserializeEvent(entity));
    }
    async getEventsByType(eventType, fromTimestamp) {
        const query = this.eventStoreRepository
            .createQueryBuilder('event')
            .where('event.eventType = :eventType', { eventType })
            .orderBy('event.timestamp', 'ASC');
        if (fromTimestamp) {
            query.andWhere('event.timestamp >= :fromTimestamp', { fromTimestamp });
        }
        const eventEntities = await query.getMany();
        return eventEntities.map(entity => this.deserializeEvent(entity));
    }
    async saveSnapshot(snapshot) {
        const existingSnapshot = await this.snapshotRepository.findOne({
            where: { aggregateId: snapshot.aggregateId }
        });
        if (existingSnapshot) {
            existingSnapshot.data = snapshot.data;
            existingSnapshot.version = snapshot.version;
            existingSnapshot.timestamp = snapshot.timestamp;
            await this.snapshotRepository.save(existingSnapshot);
        }
        else {
            const snapshotEntity = new event_store_entity_1.SnapshotEntity();
            snapshotEntity.aggregateId = snapshot.aggregateId;
            snapshotEntity.aggregateType = snapshot.aggregateType;
            snapshotEntity.data = snapshot.data;
            snapshotEntity.version = snapshot.version;
            snapshotEntity.timestamp = snapshot.timestamp;
            await this.snapshotRepository.save(snapshotEntity);
        }
    }
    async getSnapshot(aggregateId) {
        const snapshotEntity = await this.snapshotRepository.findOne({
            where: { aggregateId }
        });
        if (!snapshotEntity) {
            return null;
        }
        return {
            aggregateId: snapshotEntity.aggregateId,
            aggregateType: snapshotEntity.aggregateType,
            data: snapshotEntity.data,
            version: snapshotEntity.version,
            timestamp: snapshotEntity.timestamp,
        };
    }
    deserializeEvent(entity) {
        return entity.eventData;
    }
};
exports.EventStoreService = EventStoreService;
exports.EventStoreService = EventStoreService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(event_store_entity_1.EventStoreEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(event_store_entity_1.SnapshotEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        cqrs_1.EventBus,
        kafka_config_1.KafkaConfig])
], EventStoreService);
//# sourceMappingURL=event-store.service.js.map