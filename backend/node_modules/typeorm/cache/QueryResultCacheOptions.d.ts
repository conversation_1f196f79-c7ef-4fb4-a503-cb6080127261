/**
 * Options passed to QueryResultCache class.
 */
export interface QueryResultCacheOptions {
    /**
     * Cache identifier set by user.
     * Can be empty.
     */
    identifier?: string;
    /**
     * Time, when cache was created.
     */
    time?: number;
    /**
     * Duration in milliseconds during which results will be returned from cache.
     */
    duration: number;
    /**
     * Cached query.
     */
    query?: string;
    /**
     * Query result that will be cached.
     */
    result?: any;
}
