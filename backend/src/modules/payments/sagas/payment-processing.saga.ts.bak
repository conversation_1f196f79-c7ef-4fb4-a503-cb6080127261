import { Injectable } from '@nestjs/common';
import { Saga, ICommand, ofType } from '@nestjs/cqrs';
import { Observable } from 'rxjs';
import { map, delay, catchError } from 'rxjs/operators';

import { BaseSaga } from '../../../common/sagas/base.saga';
import { PaymentInitiatedEvent } from '../events/payment-initiated.event';
import { PaymentSucceededEvent } from '../events/payment-succeeded.event';
import { PaymentFailedEvent } from '../events/payment-failed.event';

import { ChargePaymentCommand } from '../commands/charge-payment.command';
import { RefundPaymentCommand } from '../commands/refund-payment.command';
import { ProcessPaymentCommand } from '../../bookings/commands/process-payment.command';
import { ConfirmBookingCommand } from '../../bookings/commands/confirm-booking.command';
import { CancelBookingCommand } from '../../bookings/commands/cancel-booking.command';
import { SendNotificationCommand, NotificationType } from '../../bookings/commands/send-notification.command';

@Injectable()
export class PaymentProcessingSaga extends BaseSaga {

  @Saga()
  paymentInitiated = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType(PaymentInitiatedEvent),
      map((event: PaymentInitiatedEvent) => {
        console.log(`💳 Payment Saga: Payment initiated for ${event.aggregateId}`);
        
        // Step 1: Charge the payment
        this.addStep({
          stepId: 'charge-payment',
          command: new ChargePaymentCommand(
            event.aggregateId,
            event.paymentDetails
          ),
          compensationCommand: new CancelBookingCommand(
            event.bookingId,
            'Payment processing failed'
          ),
          maxRetries: 3
        });

        this.startSaga();
        return this.steps[0].command;
      }),
      catchError((error) => {
        console.error('Payment initiation failed:', error);
        return new Observable(observer => 
          observer.next(new CancelBookingCommand(
            'unknown',
            'Payment initiation error'
          ))
        );
      })
    );
  };

  @Saga()
  paymentSucceeded = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType(PaymentSucceededEvent),
      map((event: PaymentSucceededEvent) => {
        console.log(`✅ Payment Saga: Payment succeeded for ${event.aggregateId}`);
        
        // Step 2: Update booking with payment data
        this.addStep({
          stepId: 'update-booking-payment',
          command: new ProcessPaymentCommand(
            event.bookingId,
            {
              paymentId: event.aggregateId,
              paymentMethod: 'stripe', // This would be dynamic
              amount: event.amount,
              currency: event.currency,
              transactionId: event.transactionId
            }
          ),
          compensationCommand: new RefundPaymentCommand(
            event.aggregateId,
            event.amount,
            'Booking update failed'
          ),
          maxRetries: 3
        });

        // Step 3: Send payment success notification
        this.addStep({
          stepId: 'send-payment-success-notification',
          command: new SendNotificationCommand(
            event.bookingId,
            NotificationType.EMAIL,
            'Payment Successful',
            'Your payment has been processed successfully.',
            {
              paymentId: event.aggregateId,
              transactionId: event.transactionId,
              amount: event.amount,
              currency: event.currency
            }
          )
        });

        return this.steps[this.steps.length - 2].command; // Return payment update command
      })
    );
  };

  @Saga()
  paymentFailed = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType(PaymentFailedEvent),
      delay(1000), // Small delay for proper event ordering
      map((event: PaymentFailedEvent) => {
        console.log(`❌ Payment Saga: Payment failed for ${event.aggregateId}`);
        
        // Step 1: Cancel the booking
        this.addStep({
          stepId: 'cancel-booking-payment-failed',
          command: new CancelBookingCommand(
            event.bookingId,
            `Payment failed: ${event.errorMessage}`
          ),
          maxRetries: 3
        });

        // Step 2: Send payment failure notification
        this.addStep({
          stepId: 'send-payment-failure-notification',
          command: new SendNotificationCommand(
            event.bookingId,
            NotificationType.EMAIL,
            'Payment Failed',
            'Your payment could not be processed. Please try again.',
            {
              paymentId: event.aggregateId,
              errorCode: event.errorCode,
              errorMessage: event.errorMessage,
              bookingId: event.bookingId
            }
          )
        });

        return this.steps[0].command; // Return cancel booking command
      })
    );
  };

  // Refund workflow saga
  @Saga()
  refundInitiated = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType('RefundInitiatedEvent'), // This would be a custom event
      map((event: any) => {
        console.log(`💰 Payment Saga: Refund initiated for ${event.paymentId}`);
        
        // Step 1: Process refund
        this.addStep({
          stepId: 'process-refund',
          command: new RefundPaymentCommand(
            event.paymentId,
            event.refundAmount,
            event.reason
          ),
          maxRetries: 3
        });

        // Step 2: Send refund notification
        this.addStep({
          stepId: 'send-refund-notification',
          command: new SendNotificationCommand(
            event.bookingId,
            NotificationType.EMAIL,
            'Refund Processed',
            'Your refund has been processed and will appear in your account within 3-5 business days.',
            {
              paymentId: event.paymentId,
              refundAmount: event.refundAmount,
              reason: event.reason
            }
          )
        });

        return this.steps[0].command;
      })
    );
  };

  // Override handle method for custom orchestration
  handle(event: any): Observable<ICommand> {
    switch (event.constructor.name) {
      case PaymentInitiatedEvent.name:
        return this.paymentInitiated(new Observable(observer => observer.next(event)));
      case PaymentSucceededEvent.name:
        return this.paymentSucceeded(new Observable(observer => observer.next(event)));
      case PaymentFailedEvent.name:
        return this.paymentFailed(new Observable(observer => observer.next(event)));
      case 'RefundInitiatedEvent':
        return this.refundInitiated(new Observable(observer => observer.next(event)));
      default:
        return new Observable(observer => observer.complete());
    }
  }
}
