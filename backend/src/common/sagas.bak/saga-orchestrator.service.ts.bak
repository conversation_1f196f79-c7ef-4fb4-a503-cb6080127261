import { Injectable, OnModuleInit } from '@nestjs/common';
import { EventBus, CommandBus } from '@nestjs/cqrs';
import { Subscription } from 'rxjs';

import { BaseSaga, SagaStatus } from './base.saga';
import { TicketBookingSaga } from '../../modules/bookings/sagas/ticket-booking.saga';
import { PaymentProcessingSaga } from '../../modules/payments/sagas/payment-processing.saga';
import { EventManagementSaga } from '../../modules/events/sagas/event-management.saga';
import { NotificationOrchestrationSaga } from '../../modules/notifications/sagas/notification-orchestration.saga';
import { KafkaConfig } from '../../config/kafka.config';
import { RedisConfig } from '../../config/redis.config';

export interface SagaInstance {
  id: string;
  type: string;
  status: SagaStatus;
  startedAt: Date;
  completedAt?: Date;
  progress: {
    completed: number;
    total: number;
    percentage: number;
  };
  metadata?: any;
}

@Injectable()
export class SagaOrchestratorService implements OnModuleInit {
  private activeSagas = new Map<string, BaseSaga>();
  private sagaSubscriptions = new Map<string, Subscription>();
  private sagaInstances = new Map<string, SagaInstance>();

  constructor(
    private eventBus: EventBus,
    private commandBus: CommandBus,
    private kafkaConfig: KafkaConfig,
    private redisConfig: RedisConfig,
    private ticketBookingSaga: TicketBookingSaga,
    private paymentProcessingSaga: PaymentProcessingSaga,
    private eventManagementSaga: EventManagementSaga,
    private notificationOrchestrationSaga: NotificationOrchestrationSaga
  ) {}

  async onModuleInit() {
    // Initialize saga monitoring and recovery
    await this.initializeSagaMonitoring();
    await this.recoverInProgressSagas();
    
    console.log('🎭 Saga Orchestrator initialized');
  }

  /**
   * Start a new saga instance
   */
  async startSaga(sagaType: string, event: any, metadata?: any): Promise<string> {
    const sagaId = this.generateSagaId();
    let saga: BaseSaga;

    // Create saga instance based on type
    switch (sagaType) {
      case 'TicketBookingSaga':
        saga = new TicketBookingSaga(this.commandBus);
        break;
      case 'PaymentProcessingSaga':
        saga = new PaymentProcessingSaga(this.commandBus);
        break;
      case 'EventManagementSaga':
        saga = new EventManagementSaga(this.commandBus);
        break;
      case 'NotificationOrchestrationSaga':
        saga = new NotificationOrchestrationSaga(this.commandBus);
        break;
      default:
        throw new Error(`Unknown saga type: ${sagaType}`);
    }

    // Register saga instance
    this.activeSagas.set(sagaId, saga);
    this.sagaInstances.set(sagaId, {
      id: sagaId,
      type: sagaType,
      status: SagaStatus.STARTED,
      startedAt: new Date(),
      progress: { completed: 0, total: 0, percentage: 0 },
      metadata
    });

    // Subscribe to saga events
    const subscription = saga.handle(event).subscribe({
      next: (command) => {
        this.commandBus.execute(command);
      },
      error: (error) => {
        console.error(`Saga ${sagaId} failed:`, error);
        this.handleSagaFailure(sagaId, error);
      },
      complete: () => {
        console.log(`Saga ${sagaId} completed`);
        this.handleSagaCompletion(sagaId);
      }
    });

    this.sagaSubscriptions.set(sagaId, subscription);

    // Persist saga state
    await this.persistSagaState(sagaId, saga);

    console.log(`🚀 Started ${sagaType} saga with ID: ${sagaId}`);
    return sagaId;
  }

  /**
   * Get saga status and progress
   */
  getSagaStatus(sagaId: string): SagaInstance | null {
    const instance = this.sagaInstances.get(sagaId);
    if (!instance) return null;

    const saga = this.activeSagas.get(sagaId);
    if (saga) {
      instance.status = saga.currentStatus;
      instance.progress = saga.progress;
    }

    return instance;
  }

  /**
   * Get all active sagas
   */
  getActiveSagas(): SagaInstance[] {
    return Array.from(this.sagaInstances.values())
      .filter(instance => 
        instance.status === SagaStatus.STARTED || 
        instance.status === SagaStatus.IN_PROGRESS
      );
  }

  /**
   * Cancel a saga
   */
  async cancelSaga(sagaId: string, reason?: string): Promise<void> {
    const saga = this.activeSagas.get(sagaId);
    const subscription = this.sagaSubscriptions.get(sagaId);
    
    if (saga && subscription) {
      // Trigger compensation
      await saga['compensate']();
      
      // Clean up
      subscription.unsubscribe();
      this.activeSagas.delete(sagaId);
      this.sagaSubscriptions.delete(sagaId);
      
      // Update instance status
      const instance = this.sagaInstances.get(sagaId);
      if (instance) {
        instance.status = SagaStatus.CANCELLED;
        instance.completedAt = new Date();
        instance.metadata = { ...instance.metadata, cancellationReason: reason };
      }

      await this.persistSagaState(sagaId, null);
      console.log(`❌ Cancelled saga ${sagaId}: ${reason}`);
    }
  }

  /**
   * Retry a failed saga
   */
  async retrySaga(sagaId: string): Promise<void> {
    const instance = this.sagaInstances.get(sagaId);
    if (!instance || instance.status !== SagaStatus.FAILED) {
      throw new Error('Saga not found or not in failed state');
    }

    // Restore saga from persisted state
    const sagaState = await this.restoreSagaState(sagaId);
    if (sagaState) {
      // Restart from last successful step
      await this.startSaga(instance.type, sagaState.lastEvent, {
        ...instance.metadata,
        isRetry: true,
        originalSagaId: sagaId
      });
    }
  }

  /**
   * Handle saga completion
   */
  private async handleSagaCompletion(sagaId: string): Promise<void> {
    const instance = this.sagaInstances.get(sagaId);
    if (instance) {
      instance.status = SagaStatus.COMPLETED;
      instance.completedAt = new Date();
      instance.progress = { completed: instance.progress.total, total: instance.progress.total, percentage: 100 };
    }

    // Clean up
    const subscription = this.sagaSubscriptions.get(sagaId);
    if (subscription) {
      subscription.unsubscribe();
      this.sagaSubscriptions.delete(sagaId);
    }
    
    this.activeSagas.delete(sagaId);
    await this.persistSagaState(sagaId, null);

    // Publish saga completion event
    await this.kafkaConfig.publishEvent('saga-events', {
      type: 'SagaCompleted',
      sagaId,
      completedAt: new Date(),
      sagaType: instance?.type
    });
  }

  /**
   * Handle saga failure
   */
  private async handleSagaFailure(sagaId: string, error: any): Promise<void> {
    const instance = this.sagaInstances.get(sagaId);
    if (instance) {
      instance.status = SagaStatus.FAILED;
      instance.completedAt = new Date();
      instance.metadata = { ...instance.metadata, error: error.message };
    }

    // Trigger compensation
    const saga = this.activeSagas.get(sagaId);
    if (saga) {
      try {
        await saga['compensate']();
      } catch (compensationError) {
        console.error(`Compensation failed for saga ${sagaId}:`, compensationError);
      }
    }

    // Clean up
    const subscription = this.sagaSubscriptions.get(sagaId);
    if (subscription) {
      subscription.unsubscribe();
      this.sagaSubscriptions.delete(sagaId);
    }
    
    this.activeSagas.delete(sagaId);
    await this.persistSagaState(sagaId, null);

    // Publish saga failure event
    await this.kafkaConfig.publishEvent('saga-events', {
      type: 'SagaFailed',
      sagaId,
      error: error.message,
      failedAt: new Date(),
      sagaType: instance?.type
    });
  }

  /**
   * Persist saga state to Redis
   */
  private async persistSagaState(sagaId: string, saga: BaseSaga | null): Promise<void> {
    const key = `saga:${sagaId}`;
    
    if (saga) {
      const state = {
        id: saga.id,
        status: saga.currentStatus,
        steps: saga['steps'],
        currentStepIndex: saga['currentStepIndex'],
        completedSteps: saga['completedSteps'],
        failedSteps: saga['failedSteps'],
        lastUpdated: new Date()
      };
      
      await this.redisConfig.set(key, JSON.stringify(state), 86400); // 24 hours TTL
    } else {
      await this.redisConfig.del(key);
    }
  }

  /**
   * Restore saga state from Redis
   */
  private async restoreSagaState(sagaId: string): Promise<any> {
    const key = `saga:${sagaId}`;
    const stateJson = await this.redisConfig.get(key);
    
    return stateJson ? JSON.parse(stateJson) : null;
  }

  /**
   * Initialize saga monitoring
   */
  private async initializeSagaMonitoring(): Promise<void> {
    // Set up periodic health checks
    setInterval(async () => {
      await this.performHealthCheck();
    }, 30000); // Every 30 seconds

    // Set up saga timeout monitoring
    setInterval(async () => {
      await this.checkSagaTimeouts();
    }, 60000); // Every minute
  }

  /**
   * Recover in-progress sagas after restart
   */
  private async recoverInProgressSagas(): Promise<void> {
    // This would scan Redis for persisted saga states and restore them
    console.log('🔄 Recovering in-progress sagas...');
    // Implementation would depend on your specific recovery strategy
  }

  /**
   * Perform health check on active sagas
   */
  private async performHealthCheck(): Promise<void> {
    const activeSagas = this.getActiveSagas();
    
    for (const saga of activeSagas) {
      // Check for stuck sagas
      const timeSinceStart = new Date().getTime() - saga.startedAt.getTime();
      const maxSagaDuration = 30 * 60 * 1000; // 30 minutes
      
      if (timeSinceStart > maxSagaDuration) {
        console.warn(`⚠️ Saga ${saga.id} has been running for ${timeSinceStart / 1000}s`);
        // Optionally cancel or escalate
      }
    }
  }

  /**
   * Check for saga timeouts
   */
  private async checkSagaTimeouts(): Promise<void> {
    // Implementation for checking and handling saga timeouts
  }

  private generateSagaId(): string {
    return `saga-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}
