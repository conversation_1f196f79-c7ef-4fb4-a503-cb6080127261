"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BookingConfirmedEvent = void 0;
const base_event_1 = require("../../../common/events/base.event");
class BookingConfirmedEvent extends base_event_1.DomainEvent {
    constructor(aggregateId, confirmedAt, version = 1) {
        super(aggregateId, version);
        this.aggregateId = aggregateId;
        this.confirmedAt = confirmedAt;
    }
}
exports.BookingConfirmedEvent = BookingConfirmedEvent;
//# sourceMappingURL=booking-confirmed.event.js.map