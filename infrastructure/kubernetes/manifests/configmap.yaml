apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-config
  namespace: ticket-booking
data:
  NODE_ENV: "production"
  PORT: "3001"
  # Database configuration
  DB_HOST: "ticket-booking-postgres.ticket-booking.svc.cluster.local"
  DB_PORT: "5432"
  DB_NAME: "ticket_booking"
  DB_USERNAME: "postgres"
  # Redis configuration
  REDIS_HOST: "ticket-booking-redis.ticket-booking.svc.cluster.local"
  REDIS_PORT: "6379"
  # Kafka configuration
  KAFKA_BROKERS: "ticket-booking-kafka.ticket-booking.svc.cluster.local:9092"
  # Application URLs
  FRONTEND_URL: "http://frontend-service.ticket-booking.svc.cluster.local:3000"
  # Saga configuration
  SAGA_TIMEOUT: "300000"
  SAGA_RETRY_ATTEMPTS: "3"
  # Rate limiting
  RATE_LIMIT_TTL: "60"
  RATE_LIMIT_LIMIT: "100"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: frontend-config
  namespace: ticket-booking
data:
  NODE_ENV: "production"
  NEXT_PUBLIC_API_URL: "http://backend-service.ticket-booking.svc.cluster.local:3001"
  NEXT_PUBLIC_WS_URL: "ws://backend-service.ticket-booking.svc.cluster.local:3001"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  namespace: ticket-booking
data:
  POSTGRES_DB: "ticket_booking"
  POSTGRES_USER: "postgres"
  PGDATA: "/var/lib/postgresql/data/pgdata"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: ticket-booking
data:
  redis.conf: |
    maxmemory 256mb
    maxmemory-policy allkeys-lru
    save 900 1
    save 300 10
    save 60 10000
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: kafka-config
  namespace: ticket-booking
data:
  KAFKA_BROKER_ID: "1"
  KAFKA_ZOOKEEPER_CONNECT: "ticket-booking-zookeeper.ticket-booking.svc.cluster.local:2181"
  KAFKA_ADVERTISED_LISTENERS: "PLAINTEXT://ticket-booking-kafka.ticket-booking.svc.cluster.local:9092"
  KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: "1"
  KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: "1"
  KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: "1"
  KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
  KAFKA_NUM_PARTITIONS: "3"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: zookeeper-config
  namespace: ticket-booking
data:
  ZOOKEEPER_CLIENT_PORT: "2181"
  ZOOKEEPER_TICK_TIME: "2000"
