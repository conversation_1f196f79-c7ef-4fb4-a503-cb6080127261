apiVersion: v2
name: ticket-booking
description: A Helm chart for Ticket Booking System with Kubernetes scaling
type: application
version: 1.0.0
appVersion: "1.0.0"

keywords:
  - ticket-booking
  - microservices
  - kubernetes
  - scaling
  - event-driven

home: https://github.com/your-org/ticket-booking-system
sources:
  - https://github.com/your-org/ticket-booking-system

maintainers:
  - name: Development Team
    email: <EMAIL>

dependencies:
  - name: postgresql
    version: "12.x.x"
    repository: "https://charts.bitnami.com/bitnami"
    condition: postgresql.enabled
  - name: redis
    version: "17.x.x"
    repository: "https://charts.bitnami.com/bitnami"
    condition: redis.enabled
  - name: kafka
    version: "22.x.x"
    repository: "https://charts.bitnami.com/bitnami"
    condition: kafka.enabled

annotations:
  category: Application
  licenses: MIT
