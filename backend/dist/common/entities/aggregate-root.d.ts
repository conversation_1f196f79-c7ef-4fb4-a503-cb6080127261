import { AggregateRoot as NestAggregateRoot } from '@nestjs/cqrs';
import { BaseEvent } from '../events/base.event';
export declare abstract class AggregateRoot extends NestAggregateRoot {
    private _id;
    private _version;
    private _uncommittedEvents;
    constructor(id?: string);
    get id(): string;
    get version(): number;
    apply(event: BaseEvent): void;
    getUncommittedEvents(): BaseEvent[];
    markEventsAsCommitted(): void;
    loadFromHistory(events: BaseEvent[]): void;
    protected abstract applyEvent(event: BaseEvent): void;
    private generateId;
}
