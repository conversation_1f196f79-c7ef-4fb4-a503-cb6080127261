import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../auth/enums/user-role.enum';
import { PineconeService } from '../services/pinecone.service';
import {
  SearchEventsDto,
  SimilarEventsDto,
  RecommendationsDto,
  SearchResultDto,
  RecommendationResultDto,
} from '../dtos/search.dto';
import {
  AnalyticsQueryDto,
  FraudDetectionDto,
  ContentModerationDto,
  PricingOptimizationDto,
  FraudDetectionResultDto,
  ContentModerationResultDto,
  PricingOptimizationResultDto,
  AnalyticsResultDto,
} from '../dtos/analytics.dto';

@ApiTags('AI & Vector Search')
@Controller('api/v1/ai')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class PineconeController {
  constructor(private readonly pineconeService: PineconeService) {}

  // ==================== SEARCH ENDPOINTS ====================

  @Post('search/events')
  @ApiOperation({
    summary: 'Search events using natural language',
    description: 'Find events using semantic search with natural language queries',
  })
  @ApiResponse({
    status: 200,
    description: 'Search results returned successfully',
    type: [SearchResultDto],
  })
  @HttpCode(HttpStatus.OK)
  async searchEvents(@Body() searchDto: SearchEventsDto): Promise<SearchResultDto[]> {
    return this.pineconeService.searchSimilarEvents(searchDto.query, {
      topK: searchDto.topK,
      includeMetadata: searchDto.includeMetadata,
      filter: searchDto.filter,
      namespace: searchDto.namespace,
    });
  }

  @Post('search/similar')
  @ApiOperation({
    summary: 'Find similar events',
    description: 'Find events similar to a specific event using vector similarity',
  })
  @ApiResponse({
    status: 200,
    description: 'Similar events found successfully',
    type: [SearchResultDto],
  })
  @HttpCode(HttpStatus.OK)
  async findSimilarEvents(@Body() similarDto: SimilarEventsDto): Promise<SearchResultDto[]> {
    return this.pineconeService.findSimilarEvents({
      eventId: similarDto.eventId,
      limit: similarDto.limit,
      threshold: similarDto.threshold,
      includeMetadata: similarDto.includeMetadata,
    });
  }

  @Post('recommendations')
  @ApiOperation({
    summary: 'Get personalized recommendations',
    description: 'Get AI-powered personalized event recommendations for a user',
  })
  @ApiResponse({
    status: 200,
    description: 'Recommendations generated successfully',
    type: [RecommendationResultDto],
  })
  @HttpCode(HttpStatus.OK)
  async getRecommendations(@Body() recommendationsDto: RecommendationsDto): Promise<RecommendationResultDto[]> {
    const request = {
      userId: recommendationsDto.userId,
      limit: recommendationsDto.limit,
      categories: recommendationsDto.categories,
      priceRange: recommendationsDto.priceRange,
      dateRange: recommendationsDto.dateRange ? {
        start: new Date(recommendationsDto.dateRange.start),
        end: new Date(recommendationsDto.dateRange.end),
      } : undefined,
      location: recommendationsDto.location,
      excludeEventIds: recommendationsDto.excludeEventIds,
    };

    return this.pineconeService.getPersonalizedRecommendations(request);
  }

  // ==================== ANALYTICS ENDPOINTS ====================

  @Post('analytics')
  @ApiOperation({
    summary: 'Perform AI analytics',
    description: 'Run advanced analytics using vector analysis',
  })
  @ApiResponse({
    status: 200,
    description: 'Analytics completed successfully',
    type: AnalyticsResultDto,
  })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.ANALYST)
  @HttpCode(HttpStatus.OK)
  async performAnalytics(@Body() analyticsDto: AnalyticsQueryDto): Promise<AnalyticsResultDto> {
    const query = {
      type: analyticsDto.type,
      timeRange: {
        start: new Date(analyticsDto.startDate),
        end: new Date(analyticsDto.endDate),
      },
      filters: analyticsDto.filters,
      groupBy: analyticsDto.groupBy,
    };

    return this.pineconeService.performAnalytics(query);
  }

  @Post('fraud-detection')
  @ApiOperation({
    summary: 'Detect fraudulent activity',
    description: 'Analyze user behavior patterns to detect potential fraud',
  })
  @ApiResponse({
    status: 200,
    description: 'Fraud analysis completed successfully',
    type: FraudDetectionResultDto,
  })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.SECURITY)
  @HttpCode(HttpStatus.OK)
  async detectFraud(@Body() fraudDto: FraudDetectionDto): Promise<FraudDetectionResultDto> {
    const request = {
      userId: fraudDto.userId,
      eventId: fraudDto.eventId,
      purchaseAmount: fraudDto.purchaseAmount,
      userBehavior: fraudDto.userBehavior.map(behavior => ({
        ...behavior,
        timestamp: new Date(behavior.timestamp),
      })),
      deviceInfo: fraudDto.deviceInfo,
    };

    return this.pineconeService.detectFraud(request);
  }

  @Post('content-moderation')
  @ApiOperation({
    summary: 'Moderate user content',
    description: 'Analyze and moderate user-generated content using AI',
  })
  @ApiResponse({
    status: 200,
    description: 'Content moderation completed successfully',
    type: ContentModerationResultDto,
  })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.MODERATOR)
  @HttpCode(HttpStatus.OK)
  async moderateContent(@Body() moderationDto: ContentModerationDto): Promise<ContentModerationResultDto> {
    return this.pineconeService.moderateContent({
      content: moderationDto.content,
      contentType: moderationDto.contentType,
      userId: moderationDto.userId,
      eventId: moderationDto.eventId,
    });
  }

  @Post('pricing-optimization')
  @ApiOperation({
    summary: 'Optimize event pricing',
    description: 'Get AI-powered pricing recommendations based on market conditions',
  })
  @ApiResponse({
    status: 200,
    description: 'Pricing optimization completed successfully',
    type: PricingOptimizationResultDto,
  })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.PRICING_MANAGER)
  @HttpCode(HttpStatus.OK)
  async optimizePricing(@Body() pricingDto: PricingOptimizationDto): Promise<PricingOptimizationResultDto> {
    const request = {
      eventId: pricingDto.eventId,
      currentPrice: pricingDto.currentPrice,
      marketConditions: {
        competitorPrices: pricingDto.marketConditions.competitorPrices,
        seasonality: pricingDto.marketConditions.seasonality,
        economicIndicators: pricingDto.marketConditions.economicIndicators,
        weatherForecast: pricingDto.marketConditions.weatherForecast,
      },
      demandSignals: pricingDto.demandSignals.map(signal => ({
        ...signal,
        timestamp: new Date(signal.timestamp),
      })),
    };

    return this.pineconeService.optimizePricing(request);
  }

  // ==================== UTILITY ENDPOINTS ====================

  @Get('health')
  @ApiOperation({
    summary: 'Check AI service health',
    description: 'Check the health status of Pinecone and AI services',
  })
  @ApiResponse({
    status: 200,
    description: 'Health check completed successfully',
  })
  async healthCheck(): Promise<{ status: string; services: any }> {
    try {
      const eventsStats = await this.pineconeService.getIndexStats('events-index');
      const usersStats = await this.pineconeService.getIndexStats('users-index');
      const contentStats = await this.pineconeService.getIndexStats('content-index');

      return {
        status: 'healthy',
        services: {
          pinecone: 'connected',
          openai: 'connected',
          indexes: {
            events: eventsStats,
            users: usersStats,
            content: contentStats,
          },
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        services: {
          pinecone: 'error',
          openai: 'unknown',
          error: error.message,
        },
      };
    }
  }

  @Get('stats/:indexName')
  @ApiOperation({
    summary: 'Get index statistics',
    description: 'Get detailed statistics for a specific Pinecone index',
  })
  @ApiParam({
    name: 'indexName',
    description: 'Name of the Pinecone index',
    example: 'events-index',
  })
  @ApiResponse({
    status: 200,
    description: 'Index statistics retrieved successfully',
  })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  async getIndexStats(@Param('indexName') indexName: string): Promise<any> {
    return this.pineconeService.getIndexStats(indexName);
  }

  @Post('embeddings/text')
  @ApiOperation({
    summary: 'Generate text embedding',
    description: 'Generate vector embedding for text using OpenAI',
  })
  @ApiResponse({
    status: 200,
    description: 'Text embedding generated successfully',
  })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.DEVELOPER)
  @HttpCode(HttpStatus.OK)
  async generateTextEmbedding(@Body('text') text: string): Promise<{ embedding: number[]; dimensions: number }> {
    const embedding = await this.pineconeService.generateTextEmbedding(text);
    return {
      embedding,
      dimensions: embedding.length,
    };
  }

  // ==================== BATCH OPERATIONS ====================

  @Post('batch/events/upsert')
  @ApiOperation({
    summary: 'Batch upsert events',
    description: 'Upsert multiple events to Pinecone in batch',
  })
  @ApiResponse({
    status: 200,
    description: 'Batch upsert completed successfully',
  })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.DATA_MANAGER)
  @HttpCode(HttpStatus.OK)
  async batchUpsertEvents(@Body('events') events: any[]): Promise<{ processed: number; errors: any[] }> {
    const results = { processed: 0, errors: [] };

    for (const event of events) {
      try {
        await this.pineconeService.upsertEvent(event);
        results.processed++;
      } catch (error) {
        results.errors.push({
          eventId: event.eventId,
          error: error.message,
        });
      }
    }

    return results;
  }

  @Post('batch/users/upsert')
  @ApiOperation({
    summary: 'Batch upsert users',
    description: 'Upsert multiple user profiles to Pinecone in batch',
  })
  @ApiResponse({
    status: 200,
    description: 'Batch upsert completed successfully',
  })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN, UserRole.DATA_MANAGER)
  @HttpCode(HttpStatus.OK)
  async batchUpsertUsers(@Body('users') users: any[]): Promise<{ processed: number; errors: any[] }> {
    const results = { processed: 0, errors: [] };

    for (const user of users) {
      try {
        await this.pineconeService.upsertUser(user);
        results.processed++;
      } catch (error) {
        results.errors.push({
          userId: user.userId,
          error: error.message,
        });
      }
    }

    return results;
  }
}
