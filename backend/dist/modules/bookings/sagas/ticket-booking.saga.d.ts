import { ICommand } from '@nestjs/cqrs';
import { Observable } from 'rxjs';
import { BaseSaga } from '../../../common/sagas/base.saga';
export declare class TicketBookingSaga extends BaseSaga {
    bookingInitiated: (events$: Observable<any>) => Observable<ICommand>;
    seatsReserved: (events$: Observable<any>) => Observable<ICommand>;
    paymentProcessed: (events$: Observable<any>) => Observable<ICommand>;
    bookingConfirmed: (events$: Observable<any>) => Observable<ICommand>;
    bookingCancelled: (events$: Observable<any>) => Observable<ICommand>;
    bookingExpired: (events$: Observable<any>) => Observable<ICommand>;
    handle(event: any): Observable<ICommand>;
}
