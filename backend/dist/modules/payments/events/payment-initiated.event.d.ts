import { DomainEvent } from '../../../common/events/base.event';
import { PaymentDetails } from '../commands/charge-payment.command';
export declare class PaymentInitiatedEvent extends DomainEvent {
    readonly aggregateId: string;
    readonly bookingId: string;
    readonly paymentDetails: PaymentDetails;
    constructor(aggregateId: string, bookingId: string, paymentDetails: PaymentDetails, version?: number);
}
