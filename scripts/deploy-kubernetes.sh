#!/bin/bash

# Kubernetes Deployment Script for Ticket Booking System
# Deploys to EKS cluster with 1 master + 3 worker nodes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
AWS_REGION="us-east-1"
PROJECT_NAME="ticket-booking"
ENVIRONMENT="dev"
CLUSTER_NAME="${PROJECT_NAME}-${ENVIRONMENT}-eks-cluster"

echo -e "${BLUE}🚀 Starting Kubernetes Deployment${NC}"
echo -e "${BLUE}Cluster: ${CLUSTER_NAME}${NC}"
echo -e "${BLUE}Region: ${AWS_REGION}${NC}"

# Check prerequisites
echo -e "${BLUE}📋 Checking prerequisites...${NC}"

# Check if required tools are installed
for tool in aws terraform kubectl helm; do
    if ! command -v $tool &> /dev/null; then
        echo -e "${RED}❌ $tool is not installed. Please install it first.${NC}"
        exit 1
    fi
done

# Check AWS credentials
if ! aws sts get-caller-identity &> /dev/null; then
    echo -e "${RED}❌ AWS credentials not configured. Please run 'aws configure'${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Get AWS Account ID
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
echo -e "${BLUE}🔍 AWS Account ID: ${AWS_ACCOUNT_ID}${NC}"

# Step 1: Deploy EKS Infrastructure
echo -e "${BLUE}🏗️  Deploying EKS infrastructure...${NC}"

cd infrastructure/kubernetes/terraform-eks

# Initialize Terraform
terraform init

# Create terraform.tfvars
cat > terraform.tfvars << EOF
aws_region = "${AWS_REGION}"
environment = "${ENVIRONMENT}"
project_name = "${PROJECT_NAME}"

# EKS Configuration
kubernetes_version = "1.28"
worker_instance_type = "t3.medium"
capacity_type = "SPOT"  # Cost optimization

# Scaling Configuration
min_nodes_per_group = 1
max_nodes_per_group = 2
desired_nodes_per_group = 1

# Cost Optimization
single_nat_gateway = true

# Container Images
backend_image = "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/ticket-booking-backend:latest"
frontend_image = "${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/ticket-booking-frontend:latest"
EOF

# Plan and apply
terraform plan
terraform apply -auto-approve

echo -e "${GREEN}✅ EKS infrastructure deployed${NC}"

# Step 2: Configure kubectl
echo -e "${BLUE}⚙️  Configuring kubectl...${NC}"

aws eks update-kubeconfig --region ${AWS_REGION} --name ${CLUSTER_NAME}

# Verify cluster connection
kubectl cluster-info
kubectl get nodes

echo -e "${GREEN}✅ kubectl configured${NC}"

# Step 3: Create ECR repositories and build images
echo -e "${BLUE}📦 Setting up container images...${NC}"

cd ../../..

# Create ECR repositories
aws ecr describe-repositories --repository-names "${PROJECT_NAME}-backend" --region ${AWS_REGION} 2>/dev/null || \
aws ecr create-repository --repository-name "${PROJECT_NAME}-backend" --region ${AWS_REGION}

aws ecr describe-repositories --repository-names "${PROJECT_NAME}-frontend" --region ${AWS_REGION} 2>/dev/null || \
aws ecr create-repository --repository-name "${PROJECT_NAME}-frontend" --region ${AWS_REGION}

# Login to ECR
aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com

# Build and push backend
echo -e "${BLUE}🔨 Building backend image...${NC}"
cd backend
docker build -t ${PROJECT_NAME}-backend:latest --target production .
docker tag ${PROJECT_NAME}-backend:latest ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PROJECT_NAME}-backend:latest
docker push ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PROJECT_NAME}-backend:latest
cd ..

# Build and push frontend
echo -e "${BLUE}🔨 Building frontend image...${NC}"
cd frontend
docker build -t ${PROJECT_NAME}-frontend:latest --target production .
docker tag ${PROJECT_NAME}-frontend:latest ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PROJECT_NAME}-frontend:latest
docker push ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PROJECT_NAME}-frontend:latest
cd ..

echo -e "${GREEN}✅ Container images built and pushed${NC}"

# Step 4: Update Kubernetes manifests with actual image URLs
echo -e "${BLUE}⚙️  Updating Kubernetes manifests...${NC}"

# Update backend deployment
sed -i.bak "s|your-account|${AWS_ACCOUNT_ID}|g" infrastructure/kubernetes/manifests/backend-deployment.yaml
sed -i.bak "s|us-east-1|${AWS_REGION}|g" infrastructure/kubernetes/manifests/backend-deployment.yaml

# Update frontend deployment
sed -i.bak "s|your-account|${AWS_ACCOUNT_ID}|g" infrastructure/kubernetes/manifests/frontend-deployment.yaml
sed -i.bak "s|us-east-1|${AWS_REGION}|g" infrastructure/kubernetes/manifests/frontend-deployment.yaml

echo -e "${GREEN}✅ Kubernetes manifests updated${NC}"

# Step 5: Deploy applications to Kubernetes
echo -e "${BLUE}🚀 Deploying applications to Kubernetes...${NC}"

cd infrastructure/kubernetes/manifests

# Apply manifests in order
echo -e "${BLUE}📝 Creating namespaces...${NC}"
kubectl apply -f namespace.yaml

echo -e "${BLUE}📝 Creating secrets and config maps...${NC}"
kubectl apply -f secrets.yaml
kubectl apply -f configmap.yaml

echo -e "${BLUE}📝 Deploying database services...${NC}"
kubectl apply -f database-deployment.yaml
kubectl apply -f redis-deployment.yaml
kubectl apply -f kafka-deployment.yaml

# Wait for infrastructure services to be ready
echo -e "${BLUE}⏳ Waiting for infrastructure services...${NC}"
kubectl wait --for=condition=ready pod -l app=postgres -n ticket-booking --timeout=300s
kubectl wait --for=condition=ready pod -l app=redis -n ticket-booking --timeout=300s
kubectl wait --for=condition=ready pod -l app=zookeeper -n ticket-booking --timeout=300s
kubectl wait --for=condition=ready pod -l app=kafka -n ticket-booking --timeout=300s

echo -e "${BLUE}📝 Deploying application services...${NC}"
kubectl apply -f backend-deployment.yaml
kubectl apply -f frontend-deployment.yaml

# Wait for application services to be ready
echo -e "${BLUE}⏳ Waiting for application services...${NC}"
kubectl wait --for=condition=ready pod -l app=backend -n ticket-booking --timeout=300s
kubectl wait --for=condition=ready pod -l app=frontend -n ticket-booking --timeout=300s

echo -e "${BLUE}📝 Deploying ingress...${NC}"
kubectl apply -f ingress.yaml

echo -e "${GREEN}✅ Applications deployed to Kubernetes${NC}"

# Step 6: Get deployment information
echo -e "${BLUE}📋 Getting deployment information...${NC}"

echo -e "${BLUE}🔍 Cluster Information:${NC}"
kubectl get nodes -o wide

echo -e "${BLUE}🔍 Pod Distribution:${NC}"
kubectl get pods -n ticket-booking -o wide

echo -e "${BLUE}🔍 Services:${NC}"
kubectl get svc -n ticket-booking

echo -e "${BLUE}🔍 Ingress:${NC}"
kubectl get ingress -n ticket-booking

# Get Load Balancer URL
LB_URL=$(kubectl get ingress ticket-booking-ingress -n ticket-booking -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "pending")

echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
echo -e "${BLUE}📍 Load Balancer URL: ${LB_URL}${NC}"
echo -e "${BLUE}📍 Frontend: http://${LB_URL}${NC}"
echo -e "${BLUE}📍 Backend API: http://${LB_URL}/api/v1/health${NC}"

# Step 7: Display scaling information
echo -e "${BLUE}📊 Scaling Configuration:${NC}"
echo -e "${GREEN}✅ EKS Cluster: 1 master (managed) + 3 worker node groups${NC}"
echo -e "${GREEN}✅ Worker Nodes: 1-2 nodes per group (3-6 total)${NC}"
echo -e "${GREEN}✅ Backend Pods: 2-10 replicas with HPA${NC}"
echo -e "${GREEN}✅ Frontend Pods: 2-5 replicas with HPA${NC}"
echo -e "${GREEN}✅ Auto-scaling: CPU and memory based${NC}"

echo -e "${BLUE}🔧 Useful Commands:${NC}"
echo -e "${YELLOW}# Check pod status${NC}"
echo -e "${YELLOW}kubectl get pods -n ticket-booking${NC}"
echo -e "${YELLOW}# Check HPA status${NC}"
echo -e "${YELLOW}kubectl get hpa -n ticket-booking${NC}"
echo -e "${YELLOW}# Check logs${NC}"
echo -e "${YELLOW}kubectl logs -f deployment/backend-deployment -n ticket-booking${NC}"
echo -e "${YELLOW}# Scale manually${NC}"
echo -e "${YELLOW}kubectl scale deployment backend-deployment --replicas=5 -n ticket-booking${NC}"

echo -e "${GREEN}✅ Kubernetes deployment script completed${NC}"
