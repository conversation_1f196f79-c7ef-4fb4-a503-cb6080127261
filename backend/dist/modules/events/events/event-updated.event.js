"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventUpdatedEvent = void 0;
const base_event_1 = require("../../../common/events/base.event");
class EventUpdatedEvent extends base_event_1.DomainEvent {
    constructor(aggregateId, eventData, version = 1) {
        super(aggregateId, version);
        this.aggregateId = aggregateId;
        this.eventData = eventData;
    }
}
exports.EventUpdatedEvent = EventUpdatedEvent;
//# sourceMappingURL=event-updated.event.js.map