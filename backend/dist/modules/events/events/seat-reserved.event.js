"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeatReservedEvent = void 0;
const base_event_1 = require("../../../common/events/base.event");
class SeatReservedEvent extends base_event_1.DomainEvent {
    constructor(aggregateId, seatIds, customerId, version = 1) {
        super(aggregateId, version);
        this.aggregateId = aggregateId;
        this.seatIds = seatIds;
        this.customerId = customerId;
    }
}
exports.SeatReservedEvent = SeatReservedEvent;
//# sourceMappingURL=seat-reserved.event.js.map