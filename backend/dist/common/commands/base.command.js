"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Command = exports.BaseCommand = void 0;
class BaseCommand {
    constructor() {
        this.commandId = this.generateCommandId();
        this.timestamp = new Date();
    }
    generateCommandId() {
        return `cmd-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
}
exports.BaseCommand = BaseCommand;
class Command extends BaseCommand {
    constructor() {
        super();
    }
}
exports.Command = Command;
//# sourceMappingURL=base.command.js.map