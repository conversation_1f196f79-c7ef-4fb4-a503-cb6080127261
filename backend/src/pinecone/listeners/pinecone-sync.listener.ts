import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { PineconeService } from '../services/pinecone.service';
import { EventEmbedding, UserEmbedding } from '../interfaces/pinecone.interface';

@Injectable()
export class PineconeSyncListener {
  private readonly logger = new Logger(PineconeSyncListener.name);

  constructor(private readonly pineconeService: PineconeService) {}

  // ==================== EVENT SYNC LISTENERS ====================

  @OnEvent('event.created')
  async handleEventCreated(payload: any) {
    try {
      this.logger.log(`Syncing new event to Pinecone: ${payload.eventId}`);
      
      const eventEmbedding: EventEmbedding = {
        eventId: payload.eventId,
        title: payload.title,
        description: payload.description,
        category: payload.category,
        venue: payload.venue,
        artist: payload.artist || '',
        date: new Date(payload.date),
        price: payload.price,
        tags: payload.tags || [],
        imageUrl: payload.imageUrl,
        audioUrl: payload.audioUrl,
      };

      await this.pineconeService.upsertEvent(eventEmbedding);
      this.logger.log(`Event ${payload.eventId} synced to Pinecone successfully`);
    } catch (error) {
      this.logger.error(`Failed to sync event ${payload.eventId} to Pinecone`, error);
    }
  }

  @OnEvent('event.updated')
  async handleEventUpdated(payload: any) {
    try {
      this.logger.log(`Updating event in Pinecone: ${payload.eventId}`);
      
      const eventEmbedding: EventEmbedding = {
        eventId: payload.eventId,
        title: payload.title,
        description: payload.description,
        category: payload.category,
        venue: payload.venue,
        artist: payload.artist || '',
        date: new Date(payload.date),
        price: payload.price,
        tags: payload.tags || [],
        imageUrl: payload.imageUrl,
        audioUrl: payload.audioUrl,
      };

      await this.pineconeService.upsertEvent(eventEmbedding);
      this.logger.log(`Event ${payload.eventId} updated in Pinecone successfully`);
    } catch (error) {
      this.logger.error(`Failed to update event ${payload.eventId} in Pinecone`, error);
    }
  }

  @OnEvent('event.deleted')
  async handleEventDeleted(payload: any) {
    try {
      this.logger.log(`Deleting event from Pinecone: ${payload.eventId}`);
      
      await this.pineconeService.deleteVector('events-index', payload.eventId);
      this.logger.log(`Event ${payload.eventId} deleted from Pinecone successfully`);
    } catch (error) {
      this.logger.error(`Failed to delete event ${payload.eventId} from Pinecone`, error);
    }
  }

  // ==================== USER BEHAVIOR SYNC LISTENERS ====================

  @OnEvent('user.registered')
  async handleUserRegistered(payload: any) {
    try {
      this.logger.log(`Creating user profile in Pinecone: ${payload.userId}`);
      
      const userEmbedding: UserEmbedding = {
        userId: payload.userId,
        preferences: payload.preferences || [],
        behaviorHistory: [],
        demographics: {
          ageRange: payload.ageRange,
          location: payload.location,
          interests: payload.interests || [],
          spendingPattern: 'low',
        },
        purchaseHistory: [],
      };

      await this.pineconeService.upsertUser(userEmbedding);
      this.logger.log(`User ${payload.userId} profile created in Pinecone successfully`);
    } catch (error) {
      this.logger.error(`Failed to create user ${payload.userId} profile in Pinecone`, error);
    }
  }

  @OnEvent('user.behavior.tracked')
  async handleUserBehaviorTracked(payload: any) {
    try {
      this.logger.log(`Updating user behavior in Pinecone: ${payload.userId}`);
      
      // In a real implementation, you would fetch the current user data,
      // update the behavior history, and re-upsert the user embedding
      
      // For now, we'll just log the behavior
      this.logger.log(`User ${payload.userId} performed action: ${payload.action}`);
      
      // TODO: Implement incremental behavior updates
      // This could involve:
      // 1. Fetching current user embedding
      // 2. Adding new behavior to history
      // 3. Re-generating user embedding
      // 4. Upserting updated embedding
      
    } catch (error) {
      this.logger.error(`Failed to update user ${payload.userId} behavior in Pinecone`, error);
    }
  }

  @OnEvent('booking.created')
  async handleBookingCreated(payload: any) {
    try {
      this.logger.log(`Recording purchase in user profile: ${payload.userId}`);
      
      // Update user's purchase history and spending pattern
      // This would trigger a re-calculation of user embeddings
      
      // Also track this as a positive interaction for recommendations
      const behaviorPayload = {
        userId: payload.userId,
        action: 'purchase',
        eventId: payload.eventId,
        timestamp: new Date(),
        metadata: {
          amount: payload.amount,
          ticketCount: payload.ticketCount,
        },
      };

      // Emit behavior tracking event
      // In a real implementation, you might use EventEmitter2 or similar
      await this.handleUserBehaviorTracked(behaviorPayload);
      
    } catch (error) {
      this.logger.error(`Failed to record purchase for user ${payload.userId}`, error);
    }
  }

  @OnEvent('search.performed')
  async handleSearchPerformed(payload: any) {
    try {
      this.logger.log(`Recording search behavior: ${payload.userId}`);
      
      const behaviorPayload = {
        userId: payload.userId,
        action: 'search',
        query: payload.query,
        timestamp: new Date(),
        metadata: {
          resultsCount: payload.resultsCount,
          filters: payload.filters,
        },
      };

      await this.handleUserBehaviorTracked(behaviorPayload);
      
    } catch (error) {
      this.logger.error(`Failed to record search for user ${payload.userId}`, error);
    }
  }

  @OnEvent('event.viewed')
  async handleEventViewed(payload: any) {
    try {
      this.logger.log(`Recording event view: ${payload.userId} viewed ${payload.eventId}`);
      
      const behaviorPayload = {
        userId: payload.userId,
        action: 'view',
        eventId: payload.eventId,
        timestamp: new Date(),
        duration: payload.duration,
        metadata: {
          source: payload.source, // search, recommendation, direct, etc.
          referrer: payload.referrer,
        },
      };

      await this.handleUserBehaviorTracked(behaviorPayload);
      
    } catch (error) {
      this.logger.error(`Failed to record event view for user ${payload.userId}`, error);
    }
  }

  @OnEvent('recommendation.clicked')
  async handleRecommendationClicked(payload: any) {
    try {
      this.logger.log(`Recording recommendation click: ${payload.userId} clicked ${payload.eventId}`);
      
      const behaviorPayload = {
        userId: payload.userId,
        action: 'click',
        eventId: payload.eventId,
        timestamp: new Date(),
        metadata: {
          recommendationScore: payload.score,
          recommendationReason: payload.reason,
          position: payload.position, // position in recommendation list
        },
      };

      await this.handleUserBehaviorTracked(behaviorPayload);
      
    } catch (error) {
      this.logger.error(`Failed to record recommendation click for user ${payload.userId}`, error);
    }
  }

  // ==================== CONTENT SYNC LISTENERS ====================

  @OnEvent('review.created')
  async handleReviewCreated(payload: any) {
    try {
      this.logger.log(`Processing new review for content analysis: ${payload.reviewId}`);
      
      // Moderate the review content
      const moderationResult = await this.pineconeService.moderateContent({
        content: payload.content,
        contentType: 'review',
        userId: payload.userId,
        eventId: payload.eventId,
      });

      // If content is inappropriate, emit an event for further action
      if (!moderationResult.isAppropriate) {
        this.logger.warn(`Inappropriate review detected: ${payload.reviewId}`);
        // Emit event for content moderation action
        // EventEmitter.emit('review.flagged', { reviewId: payload.reviewId, ...moderationResult });
      }

      // Store the review embedding for future similarity searches
      // This could be used for finding similar reviews or sentiment analysis
      
    } catch (error) {
      this.logger.error(`Failed to process review ${payload.reviewId}`, error);
    }
  }

  @OnEvent('fraud.suspected')
  async handleFraudSuspected(payload: any) {
    try {
      this.logger.log(`Analyzing suspected fraud: ${payload.userId}`);
      
      const fraudResult = await this.pineconeService.detectFraud({
        userId: payload.userId,
        eventId: payload.eventId,
        purchaseAmount: payload.amount,
        userBehavior: payload.behaviorHistory || [],
        deviceInfo: payload.deviceInfo,
      });

      if (fraudResult.riskLevel === 'high') {
        this.logger.warn(`High fraud risk detected for user ${payload.userId}: ${fraudResult.riskScore}`);
        // Emit event for immediate action
        // EventEmitter.emit('fraud.confirmed', { userId: payload.userId, ...fraudResult });
      }

      // Store fraud pattern for future detection
      // This helps improve the fraud detection model over time
      
    } catch (error) {
      this.logger.error(`Failed to analyze fraud for user ${payload.userId}`, error);
    }
  }

  // ==================== PRICING SYNC LISTENERS ====================

  @OnEvent('pricing.optimization.requested')
  async handlePricingOptimizationRequested(payload: any) {
    try {
      this.logger.log(`Optimizing pricing for event: ${payload.eventId}`);
      
      const pricingResult = await this.pineconeService.optimizePricing({
        eventId: payload.eventId,
        currentPrice: payload.currentPrice,
        marketConditions: payload.marketConditions,
        demandSignals: payload.demandSignals,
      });

      // Emit event with pricing recommendations
      // EventEmitter.emit('pricing.optimized', { eventId: payload.eventId, ...pricingResult });
      
      this.logger.log(`Pricing optimization completed for event ${payload.eventId}: $${pricingResult.recommendedPrice}`);
      
    } catch (error) {
      this.logger.error(`Failed to optimize pricing for event ${payload.eventId}`, error);
    }
  }

  // ==================== ANALYTICS SYNC LISTENERS ====================

  @OnEvent('analytics.scheduled')
  async handleScheduledAnalytics(payload: any) {
    try {
      this.logger.log(`Running scheduled analytics: ${payload.type}`);
      
      const analyticsResult = await this.pineconeService.performAnalytics({
        type: payload.type,
        timeRange: payload.timeRange,
        filters: payload.filters,
        groupBy: payload.groupBy,
      });

      // Store analytics results for dashboard display
      // EventEmitter.emit('analytics.completed', { type: payload.type, ...analyticsResult });
      
      this.logger.log(`Analytics completed: ${payload.type} with confidence ${analyticsResult.confidence}`);
      
    } catch (error) {
      this.logger.error(`Failed to run analytics: ${payload.type}`, error);
    }
  }
}
