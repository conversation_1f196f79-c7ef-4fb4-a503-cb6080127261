#!/bin/bash

# AWS Cost Monitoring Script for Free Tier
# This script helps monitor your AWS costs and free tier usage

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}💰 AWS Free Tier Cost Monitor${NC}"
echo -e "${BLUE}================================${NC}"

# Check if AWS CLI is installed and configured
if ! command -v aws &> /dev/null; then
    echo -e "${RED}❌ AWS CLI is not installed${NC}"
    exit 1
fi

if ! aws sts get-caller-identity &> /dev/null; then
    echo -e "${RED}❌ AWS credentials not configured${NC}"
    exit 1
fi

# Get current date info
CURRENT_MONTH=$(date +%Y-%m-01)
NEXT_MONTH=$(date -d "$(date +%Y-%m-01) +1 month" +%Y-%m-01)
CURRENT_DATE=$(date +%Y-%m-%d)

echo -e "${BLUE}📅 Monitoring period: ${CURRENT_MONTH} to ${CURRENT_DATE}${NC}"

# Function to get cost and usage
get_costs() {
    local service=$1
    local metric=${2:-BlendedCost}
    
    aws ce get-cost-and-usage \
        --time-period Start=${CURRENT_MONTH},End=${CURRENT_DATE} \
        --granularity MONTHLY \
        --metrics ${metric} \
        --group-by Type=DIMENSION,Key=SERVICE \
        --query "ResultsByTime[0].Groups[?Keys[0]=='${service}'].Metrics.${metric}.Amount" \
        --output text 2>/dev/null || echo "0"
}

# Function to format cost
format_cost() {
    local cost=$1
    if [[ "$cost" == "0" || "$cost" == "" ]]; then
        echo "\$0.00"
    else
        printf "\$%.2f" "$cost"
    fi
}

# Function to check if cost exceeds threshold
check_threshold() {
    local cost=$1
    local threshold=$2
    local service=$3
    
    if (( $(echo "$cost > $threshold" | bc -l) )); then
        echo -e "${RED}⚠️  ${service}: $(format_cost $cost) (exceeds \$${threshold})${NC}"
        return 1
    else
        echo -e "${GREEN}✅ ${service}: $(format_cost $cost)${NC}"
        return 0
    fi
}

echo -e "\n${BLUE}📊 Current Month Costs by Service${NC}"
echo -e "${BLUE}===================================${NC}"

# Get costs for major services
ec2_cost=$(get_costs "Amazon Elastic Compute Cloud - Compute")
rds_cost=$(get_costs "Amazon Relational Database Service")
elasticache_cost=$(get_costs "Amazon ElastiCache")
elb_cost=$(get_costs "Amazon Elastic Load Balancing")
vpc_cost=$(get_costs "Amazon Virtual Private Cloud")
ecr_cost=$(get_costs "Amazon EC2 Container Registry (ECR)")
cloudwatch_cost=$(get_costs "Amazon CloudWatch")
s3_cost=$(get_costs "Amazon Simple Storage Service")
route53_cost=$(get_costs "Amazon Route 53")

# Check against free tier limits (warning thresholds)
total_issues=0

check_threshold "$ec2_cost" "0" "EC2/ECS Fargate" || ((total_issues++))
check_threshold "$rds_cost" "0" "RDS PostgreSQL" || ((total_issues++))
check_threshold "$elasticache_cost" "0" "ElastiCache Redis" || ((total_issues++))
check_threshold "$elb_cost" "0" "Load Balancer" || ((total_issues++))
check_threshold "$vpc_cost" "45" "VPC (NAT Gateway)" || ((total_issues++))
check_threshold "$ecr_cost" "0" "ECR" || ((total_issues++))
check_threshold "$cloudwatch_cost" "0" "CloudWatch" || ((total_issues++))
check_threshold "$s3_cost" "0" "S3" || ((total_issues++))
check_threshold "$route53_cost" "0.50" "Route 53" || ((total_issues++))

# Calculate total cost
total_cost=$(aws ce get-cost-and-usage \
    --time-period Start=${CURRENT_MONTH},End=${CURRENT_DATE} \
    --granularity MONTHLY \
    --metrics BlendedCost \
    --query "ResultsByTime[0].Total.BlendedCost.Amount" \
    --output text 2>/dev/null || echo "0")

echo -e "\n${BLUE}💵 Total Cost This Month: $(format_cost $total_cost)${NC}"

# Check total cost threshold
if (( $(echo "$total_cost > 50" | bc -l) )); then
    echo -e "${RED}🚨 WARNING: Total cost exceeds \$50!${NC}"
elif (( $(echo "$total_cost > 20" | bc -l) )); then
    echo -e "${YELLOW}⚠️  CAUTION: Total cost exceeds \$20${NC}"
else
    echo -e "${GREEN}✅ Total cost is within reasonable limits${NC}"
fi

# Free tier usage warnings
echo -e "\n${BLUE}🆓 Free Tier Usage Reminders${NC}"
echo -e "${BLUE}=============================${NC}"
echo -e "${YELLOW}• ECS Fargate: 750 hours/month (31 days = 744 hours)${NC}"
echo -e "${YELLOW}• RDS db.t3.micro: 750 hours/month${NC}"
echo -e "${YELLOW}• ElastiCache cache.t2.micro: 750 hours/month${NC}"
echo -e "${YELLOW}• ALB: 750 hours/month + 15 LCU hours${NC}"
echo -e "${RED}• NAT Gateway: NOT FREE (~\$45/month)${NC}"

# Get free tier usage (if available)
echo -e "\n${BLUE}📈 Free Tier Usage (Current Month)${NC}"
echo -e "${BLUE}===================================${NC}"

# Note: Free tier usage API is limited, so we'll estimate based on running resources
echo -e "${BLUE}Checking running resources...${NC}"

# Check ECS services
ecs_clusters=$(aws ecs list-clusters --query "clusterArns" --output text 2>/dev/null | wc -l)
if [ "$ecs_clusters" -gt 0 ]; then
    echo -e "${GREEN}✅ ECS clusters found: $ecs_clusters${NC}"
    
    # List running tasks
    for cluster in $(aws ecs list-clusters --query "clusterArns[]" --output text 2>/dev/null); do
        cluster_name=$(basename "$cluster")
        running_tasks=$(aws ecs list-tasks --cluster "$cluster_name" --desired-status RUNNING --query "taskArns" --output text 2>/dev/null | wc -w)
        if [ "$running_tasks" -gt 0 ]; then
            echo -e "${BLUE}  • Cluster $cluster_name: $running_tasks running tasks${NC}"
        fi
    done
fi

# Check RDS instances
rds_instances=$(aws rds describe-db-instances --query "DBInstances[?DBInstanceStatus=='available'].DBInstanceIdentifier" --output text 2>/dev/null | wc -w)
if [ "$rds_instances" -gt 0 ]; then
    echo -e "${GREEN}✅ RDS instances running: $rds_instances${NC}"
fi

# Check ElastiCache clusters
redis_clusters=$(aws elasticache describe-cache-clusters --query "CacheClusters[?CacheClusterStatus=='available'].CacheClusterId" --output text 2>/dev/null | wc -w)
if [ "$redis_clusters" -gt 0 ]; then
    echo -e "${GREEN}✅ ElastiCache clusters running: $redis_clusters${NC}"
fi

# Recommendations
echo -e "\n${BLUE}💡 Cost Optimization Recommendations${NC}"
echo -e "${BLUE}====================================${NC}"

if (( $(echo "$vpc_cost > 40" | bc -l) )); then
    echo -e "${YELLOW}• Consider removing NAT Gateway for development (saves ~\$45/month)${NC}"
fi

if [ "$total_issues" -gt 0 ]; then
    echo -e "${YELLOW}• Review services exceeding free tier limits${NC}"
fi

echo -e "${GREEN}• Stop services when not in use to save costs${NC}"
echo -e "${GREEN}• Use Fargate Spot for non-production workloads${NC}"
echo -e "${GREEN}• Set up billing alerts for early warning${NC}"

# Billing alerts check
echo -e "\n${BLUE}🔔 Billing Alerts Status${NC}"
echo -e "${BLUE}========================${NC}"

billing_alarms=$(aws cloudwatch describe-alarms --alarm-names "BillingAlarm" --query "MetricAlarms[0].AlarmName" --output text 2>/dev/null)
if [ "$billing_alarms" = "BillingAlarm" ]; then
    echo -e "${GREEN}✅ Billing alarm is configured${NC}"
else
    echo -e "${YELLOW}⚠️  No billing alarm found. Consider setting one up:${NC}"
    echo -e "${BLUE}   aws cloudwatch put-metric-alarm --alarm-name BillingAlarm --threshold 10.0 ...${NC}"
fi

# Summary
echo -e "\n${BLUE}📋 Summary${NC}"
echo -e "${BLUE}==========${NC}"
echo -e "Total Cost: $(format_cost $total_cost)"
echo -e "Issues Found: $total_issues"
echo -e "Date: $(date)"

if [ "$total_issues" -eq 0 ] && (( $(echo "$total_cost < 20" | bc -l) )); then
    echo -e "${GREEN}🎉 Everything looks good! You're staying within free tier limits.${NC}"
else
    echo -e "${YELLOW}⚠️  Review the warnings above to optimize costs.${NC}"
fi
