"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseSaga = exports.SagaStatus = void 0;
const common_1 = require("@nestjs/common");
const cqrs_1 = require("@nestjs/cqrs");
var SagaStatus;
(function (SagaStatus) {
    SagaStatus["STARTED"] = "STARTED";
    SagaStatus["IN_PROGRESS"] = "IN_PROGRESS";
    SagaStatus["COMPLETED"] = "COMPLETED";
    SagaStatus["FAILED"] = "FAILED";
    SagaStatus["COMPENSATING"] = "COMPENSATING";
    SagaStatus["COMPENSATED"] = "COMPENSATED";
    SagaStatus["CANCELLED"] = "CANCELLED";
})(SagaStatus || (exports.SagaStatus = SagaStatus = {}));
let BaseSaga = class BaseSaga {
    constructor(commandBus) {
        this.commandBus = commandBus;
        this.steps = [];
        this.currentStepIndex = 0;
        this.completedSteps = [];
        this.failedSteps = [];
        this.sagaId = this.generateSagaId();
        this.status = SagaStatus.STARTED;
    }
    async executeStep(step) {
        try {
            console.log(`Executing saga step: ${step.stepId}`);
            await this.commandBus.execute(step.command);
            this.completedSteps.push(step.stepId);
            this.currentStepIndex++;
            if (this.currentStepIndex >= this.steps.length) {
                this.status = SagaStatus.COMPLETED;
                console.log(`Saga ${this.sagaId} completed successfully`);
            }
        }
        catch (error) {
            console.error(`Saga step ${step.stepId} failed:`, error);
            this.failedSteps.push(step.stepId);
            this.status = SagaStatus.FAILED;
            await this.compensate();
        }
    }
    async compensate() {
        this.status = SagaStatus.COMPENSATING;
        console.log(`Starting compensation for saga ${this.sagaId}`);
        for (let i = this.completedSteps.length - 1; i >= 0; i--) {
            const stepId = this.completedSteps[i];
            const step = this.steps.find(s => s.stepId === stepId);
            if (step?.compensationCommand) {
                try {
                    console.log(`Executing compensation for step: ${stepId}`);
                    await this.commandBus.execute(step.compensationCommand);
                }
                catch (error) {
                    console.error(`Compensation failed for step ${stepId}:`, error);
                }
            }
        }
        this.status = SagaStatus.COMPENSATED;
        console.log(`Saga ${this.sagaId} compensation completed`);
    }
    addStep(step) {
        this.steps.push(step);
    }
    async executeNextStep() {
        if (this.currentStepIndex < this.steps.length && this.status === SagaStatus.IN_PROGRESS) {
            const nextStep = this.steps[this.currentStepIndex];
            await this.executeStep(nextStep);
        }
    }
    startSaga() {
        this.status = SagaStatus.IN_PROGRESS;
        console.log(`Saga ${this.sagaId} started`);
    }
    generateSagaId() {
        return `saga-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    get id() {
        return this.sagaId;
    }
    get currentStatus() {
        return this.status;
    }
    get progress() {
        const completed = this.completedSteps.length;
        const total = this.steps.length;
        const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
        return { completed, total, percentage };
    }
};
exports.BaseSaga = BaseSaga;
exports.BaseSaga = BaseSaga = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [cqrs_1.CommandBus])
], BaseSaga);
//# sourceMappingURL=base.saga.js.map