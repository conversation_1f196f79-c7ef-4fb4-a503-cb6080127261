import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CqrsModule } from '@nestjs/cqrs';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ThrottlerModule } from '@nestjs/throttler';

// Simple controllers for basic functionality
import { AppController } from './app.controller';
import { AppService } from './app.service';

// Database configuration
import { DatabaseConfig } from './config/database.config';

// Basic modules
import { UsersModule } from './users/users.module';
import { EventsModule } from './events/events.module';

// Pinecone AI module
import { PineconeModule } from './pinecone/pinecone.module';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),

    // Database
    TypeOrmModule.forRootAsync({
      useClass: DatabaseConfig,
    }),

    // CQRS
    CqrsModule,

    // Event Emitter
    EventEmitterModule.forRoot(),

    // Rate Limiting
    ThrottlerModule.forRoot([
      {
        ttl: 60000, // 1 minute
        limit: 100, // 100 requests per minute
      },
    ]),

    // Application modules
    UsersModule,
    EventsModule,
    PineconeModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppSimpleModule {}
