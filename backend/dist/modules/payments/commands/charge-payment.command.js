"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChargePaymentCommand = void 0;
const base_command_1 = require("../../../common/commands/base.command");
class ChargePaymentCommand extends base_command_1.Command {
    constructor(paymentId, paymentDetails) {
        super();
        this.paymentId = paymentId;
        this.paymentDetails = paymentDetails;
    }
}
exports.ChargePaymentCommand = ChargePaymentCommand;
//# sourceMappingURL=charge-payment.command.js.map