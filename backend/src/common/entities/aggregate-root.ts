import { AggregateRoot as NestAggregateRoot } from '@nestjs/cqrs';
import { BaseEvent } from '../events/base.event';

export abstract class AggregateRoot extends NestAggregateRoot {
  private _id: string;
  private _version: number = 0;
  private _uncommittedEvents: BaseEvent[] = [];

  constructor(id?: string) {
    super();
    this._id = id || this.generateId();
  }

  get id(): string {
    return this._id;
  }

  get version(): number {
    return this._version;
  }

  public apply(event: BaseEvent): void {
    this._uncommittedEvents.push(event);
    this.applyEvent(event);
    this._version++;
  }

  public getUncommittedEvents(): BaseEvent[] {
    return [...this._uncommittedEvents];
  }

  public markEventsAsCommitted(): void {
    this._uncommittedEvents = [];
  }

  public loadFromHistory(events: BaseEvent[]): void {
    events.forEach(event => {
      this.applyEvent(event);
      this._version++;
    });
  }

  protected abstract applyEvent(event: BaseEvent): void;

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}
