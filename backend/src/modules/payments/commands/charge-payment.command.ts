import { Command } from '../../../common/commands/base.command';

export interface PaymentDetails {
  amount: number;
  currency: string;
  paymentMethodId: string;
  customerId: string;
  description: string;
  metadata?: Record<string, any>;
}

export class ChargePaymentCommand extends Command {
  constructor(
    public readonly paymentId: string,
    public readonly paymentDetails: PaymentDetails
  ) {
    super();
  }
}
