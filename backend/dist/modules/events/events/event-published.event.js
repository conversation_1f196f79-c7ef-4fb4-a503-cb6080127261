"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventPublishedEvent = void 0;
const base_event_1 = require("../../../common/events/base.event");
class EventPublishedEvent extends base_event_1.DomainEvent {
    constructor(aggregateId, version = 1) {
        super(aggregateId, version);
        this.aggregateId = aggregateId;
    }
}
exports.EventPublishedEvent = EventPublishedEvent;
//# sourceMappingURL=event-published.event.js.map