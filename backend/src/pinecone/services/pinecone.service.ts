import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Pinecone } from '@pinecone-database/pinecone';
import { OpenAI } from 'openai';
import {
  PineconeConfig,
  SearchQuery,
  SearchResult,
  UpsertRequest,
  EventEmbedding,
  UserEmbedding,
  RecommendationRequest,
  RecommendationResult,
  SimilarityRequest,
  FraudDetectionRequest,
  FraudDetectionResult,
  ContentModerationRequest,
  ContentModerationResult,
  AnalyticsQuery,
  AnalyticsResult,
  PricingOptimizationRequest,
  PricingOptimizationResult,
} from '../interfaces/pinecone.interface';

@Injectable()
export class PineconeService {
  private readonly logger = new Logger(PineconeService.name);
  private pinecone: Pinecone;
  private openai: OpenAI;
  private eventsIndex: any;
  private usersIndex: any;
  private contentIndex: any;

  constructor(private configService: ConfigService) {
    this.initializePinecone();
    this.initializeOpenAI();
  }

  private async initializePinecone() {
    try {
      this.pinecone = new Pinecone({
        apiKey: this.configService.get<string>('PINECONE_API_KEY'),
        environment: this.configService.get<string>('PINECONE_ENVIRONMENT'),
      });

      // Initialize indexes
      this.eventsIndex = this.pinecone.index('events-index');
      this.usersIndex = this.pinecone.index('users-index');
      this.contentIndex = this.pinecone.index('content-index');

      this.logger.log('Pinecone initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Pinecone', error);
      throw error;
    }
  }

  private initializeOpenAI() {
    this.openai = new OpenAI({
      apiKey: this.configService.get<string>('OPENAI_API_KEY'),
    });
  }

  // ==================== EMBEDDING GENERATION ====================

  async generateTextEmbedding(text: string): Promise<number[]> {
    try {
      const response = await this.openai.embeddings.create({
        model: 'text-embedding-ada-002',
        input: text,
      });

      return response.data[0].embedding;
    } catch (error) {
      this.logger.error('Failed to generate text embedding', error);
      throw error;
    }
  }

  async generateEventEmbedding(event: EventEmbedding): Promise<number[]> {
    const combinedText = [
      event.title,
      event.description,
      event.category,
      event.venue,
      event.artist,
      ...event.tags,
    ].join(' ');

    return this.generateTextEmbedding(combinedText);
  }

  async generateUserEmbedding(user: UserEmbedding): Promise<number[]> {
    // Create user profile text from preferences and behavior
    const profileText = [
      ...user.preferences,
      ...user.behaviorHistory.map(b => `${b.action} ${b.eventId || b.query || ''}`),
      user.demographics.location || '',
      ...(user.demographics.interests || []),
    ].join(' ');

    return this.generateTextEmbedding(profileText);
  }

  // ==================== EVENT OPERATIONS ====================

  async upsertEvent(event: EventEmbedding): Promise<void> {
    try {
      const embedding = await this.generateEventEmbedding(event);
      
      const upsertRequest: UpsertRequest = {
        id: event.eventId,
        values: embedding,
        metadata: {
          id: event.eventId,
          eventId: event.eventId,
          type: 'event',
          title: event.title,
          description: event.description,
          category: event.category,
          venue: event.venue,
          date: event.date.toISOString(),
          price: event.price,
          tags: event.tags,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      };

      await this.eventsIndex.upsert([upsertRequest]);
      this.logger.log(`Event ${event.eventId} upserted to Pinecone`);
    } catch (error) {
      this.logger.error(`Failed to upsert event ${event.eventId}`, error);
      throw error;
    }
  }

  async searchSimilarEvents(query: string, options: SearchQuery = {}): Promise<SearchResult[]> {
    try {
      const queryEmbedding = await this.generateTextEmbedding(query);
      
      const searchRequest = {
        vector: queryEmbedding,
        topK: options.topK || 10,
        includeMetadata: options.includeMetadata !== false,
        filter: options.filter,
        namespace: options.namespace,
      };

      const results = await this.eventsIndex.query(searchRequest);
      
      return results.matches.map(match => ({
        id: match.id,
        score: match.score,
        metadata: match.metadata,
      }));
    } catch (error) {
      this.logger.error('Failed to search similar events', error);
      throw error;
    }
  }

  async findSimilarEvents(request: SimilarityRequest): Promise<SearchResult[]> {
    try {
      // Get the event vector first
      const eventVector = await this.eventsIndex.fetch([request.eventId]);
      
      if (!eventVector.vectors[request.eventId]) {
        throw new Error(`Event ${request.eventId} not found in index`);
      }

      const searchRequest = {
        vector: eventVector.vectors[request.eventId].values,
        topK: (request.limit || 10) + 1, // +1 to exclude the original event
        includeMetadata: request.includeMetadata !== false,
        filter: { eventId: { $ne: request.eventId } }, // Exclude the original event
      };

      const results = await this.eventsIndex.query(searchRequest);
      
      return results.matches
        .filter(match => match.score >= (request.threshold || 0.7))
        .slice(0, request.limit || 10)
        .map(match => ({
          id: match.id,
          score: match.score,
          metadata: match.metadata,
        }));
    } catch (error) {
      this.logger.error('Failed to find similar events', error);
      throw error;
    }
  }

  // ==================== USER OPERATIONS ====================

  async upsertUser(user: UserEmbedding): Promise<void> {
    try {
      const embedding = await this.generateUserEmbedding(user);
      
      const upsertRequest: UpsertRequest = {
        id: user.userId,
        values: embedding,
        metadata: {
          id: user.userId,
          userId: user.userId,
          type: 'user',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      };

      await this.usersIndex.upsert([upsertRequest]);
      this.logger.log(`User ${user.userId} upserted to Pinecone`);
    } catch (error) {
      this.logger.error(`Failed to upsert user ${user.userId}`, error);
      throw error;
    }
  }

  async getPersonalizedRecommendations(request: RecommendationRequest): Promise<RecommendationResult[]> {
    try {
      // Get user vector
      const userVector = await this.usersIndex.fetch([request.userId]);
      
      if (!userVector.vectors[request.userId]) {
        // Fallback to popular events if user not found
        return this.getPopularEvents(request.limit || 10);
      }

      // Build filter based on request parameters
      const filter: any = {};
      
      if (request.categories?.length) {
        filter.category = { $in: request.categories };
      }
      
      if (request.priceRange) {
        filter.price = {
          $gte: request.priceRange.min,
          $lte: request.priceRange.max,
        };
      }
      
      if (request.excludeEventIds?.length) {
        filter.eventId = { $nin: request.excludeEventIds };
      }

      const searchRequest = {
        vector: userVector.vectors[request.userId].values,
        topK: request.limit || 10,
        includeMetadata: true,
        filter: Object.keys(filter).length > 0 ? filter : undefined,
      };

      const results = await this.eventsIndex.query(searchRequest);
      
      return results.matches.map(match => ({
        eventId: match.metadata.eventId,
        score: match.score,
        reason: this.generateRecommendationReason(match.score, match.metadata),
        confidence: match.score,
        metadata: match.metadata,
      }));
    } catch (error) {
      this.logger.error('Failed to get personalized recommendations', error);
      throw error;
    }
  }

  private async getPopularEvents(limit: number): Promise<RecommendationResult[]> {
    // Implement fallback logic for popular events
    // This could query your main database for trending events
    return [];
  }

  private generateRecommendationReason(score: number, metadata: any): string {
    if (score > 0.9) return 'Perfect match for your preferences';
    if (score > 0.8) return 'Highly recommended based on your history';
    if (score > 0.7) return 'Good match for your interests';
    if (score > 0.6) return 'You might enjoy this event';
    return 'Suggested based on similar users';
  }

  // ==================== FRAUD DETECTION ====================

  async detectFraud(request: FraudDetectionRequest): Promise<FraudDetectionResult> {
    try {
      // Generate behavior embedding
      const behaviorText = request.userBehavior
        .map(b => `${b.action} ${b.timestamp.getTime()} ${b.duration || 0}`)
        .join(' ');
      
      const behaviorEmbedding = await this.generateTextEmbedding(
        `${behaviorText} amount:${request.purchaseAmount} event:${request.eventId}`
      );

      // Search for similar behavior patterns
      const searchRequest = {
        vector: behaviorEmbedding,
        topK: 100,
        includeMetadata: true,
        filter: { type: 'fraud_pattern' },
      };

      const results = await this.contentIndex.query(searchRequest);
      
      // Calculate risk score based on similarity to known fraud patterns
      let riskScore = 0;
      const reasons: string[] = [];
      
      if (results.matches.length > 0) {
        const avgSimilarity = results.matches.reduce((sum, match) => sum + match.score, 0) / results.matches.length;
        riskScore = avgSimilarity * 100;
        
        if (avgSimilarity > 0.8) reasons.push('Behavior matches known fraud patterns');
        if (avgSimilarity > 0.6) reasons.push('Suspicious activity detected');
      }

      // Additional risk factors
      if (request.purchaseAmount > 1000) {
        riskScore += 20;
        reasons.push('High-value transaction');
      }

      const riskLevel = riskScore > 70 ? 'high' : riskScore > 40 ? 'medium' : 'low';
      
      return {
        riskScore,
        riskLevel,
        reasons,
        recommendations: this.generateFraudRecommendations(riskLevel),
      };
    } catch (error) {
      this.logger.error('Failed to detect fraud', error);
      throw error;
    }
  }

  private generateFraudRecommendations(riskLevel: string): string[] {
    switch (riskLevel) {
      case 'high':
        return ['Block transaction', 'Require additional verification', 'Manual review'];
      case 'medium':
        return ['Additional verification', 'Monitor closely', 'Limit transaction amount'];
      case 'low':
        return ['Standard processing', 'Log for analysis'];
      default:
        return [];
    }
  }

  // ==================== CONTENT MODERATION ====================

  async moderateContent(request: ContentModerationRequest): Promise<ContentModerationResult> {
    try {
      const contentEmbedding = await this.generateTextEmbedding(request.content);
      
      // Search for similar inappropriate content
      const searchRequest = {
        vector: contentEmbedding,
        topK: 50,
        includeMetadata: true,
        filter: { type: 'inappropriate_content' },
      };

      const results = await this.contentIndex.query(searchRequest);
      
      let toxicityScore = 0;
      const categories: string[] = [];
      
      if (results.matches.length > 0) {
        const maxSimilarity = Math.max(...results.matches.map(m => m.score));
        toxicityScore = maxSimilarity * 100;
        
        // Extract categories from similar content
        results.matches.forEach(match => {
          if (match.metadata?.category && !categories.includes(match.metadata.category)) {
            categories.push(match.metadata.category);
          }
        });
      }

      // Simple sentiment analysis (in production, use a proper sentiment model)
      const sentiment = this.analyzeSentiment(request.content);
      
      return {
        isAppropriate: toxicityScore < 50,
        confidence: Math.abs(toxicityScore - 50) / 50,
        categories,
        sentiment,
        toxicityScore,
      };
    } catch (error) {
      this.logger.error('Failed to moderate content', error);
      throw error;
    }
  }

  private analyzeSentiment(content: string): 'positive' | 'negative' | 'neutral' {
    // Simple keyword-based sentiment analysis
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'love', 'fantastic'];
    const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'horrible', 'worst'];
    
    const words = content.toLowerCase().split(/\s+/);
    const positiveCount = words.filter(word => positiveWords.includes(word)).length;
    const negativeCount = words.filter(word => negativeWords.includes(word)).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  // ==================== ANALYTICS ====================

  async performAnalytics(query: AnalyticsQuery): Promise<AnalyticsResult> {
    try {
      switch (query.type) {
        case 'user_clustering':
          return this.performUserClustering(query);
        case 'event_performance':
          return this.analyzeEventPerformance(query);
        case 'trend_analysis':
          return this.analyzeTrends(query);
        case 'market_insights':
          return this.generateMarketInsights(query);
        default:
          throw new Error(`Unknown analytics type: ${query.type}`);
      }
    } catch (error) {
      this.logger.error('Failed to perform analytics', error);
      throw error;
    }
  }

  private async performUserClustering(query: AnalyticsQuery): Promise<AnalyticsResult> {
    // Implementation for user clustering analysis
    return {
      type: 'user_clustering',
      data: [],
      insights: ['User segments identified', 'Behavior patterns analyzed'],
      recommendations: ['Target specific segments', 'Personalize marketing'],
      confidence: 0.85,
    };
  }

  private async analyzeEventPerformance(query: AnalyticsQuery): Promise<AnalyticsResult> {
    // Implementation for event performance analysis
    return {
      type: 'event_performance',
      data: [],
      insights: ['Top performing events identified', 'Success factors analyzed'],
      recommendations: ['Replicate successful patterns', 'Optimize underperforming events'],
      confidence: 0.78,
    };
  }

  private async analyzeTrends(query: AnalyticsQuery): Promise<AnalyticsResult> {
    // Implementation for trend analysis
    return {
      type: 'trend_analysis',
      data: [],
      insights: ['Emerging trends detected', 'Seasonal patterns identified'],
      recommendations: ['Capitalize on trends', 'Plan seasonal campaigns'],
      confidence: 0.72,
    };
  }

  private async generateMarketInsights(query: AnalyticsQuery): Promise<AnalyticsResult> {
    // Implementation for market insights
    return {
      type: 'market_insights',
      data: [],
      insights: ['Market opportunities identified', 'Competitive analysis completed'],
      recommendations: ['Enter new markets', 'Differentiate offerings'],
      confidence: 0.68,
    };
  }

  // ==================== PRICING OPTIMIZATION ====================

  async optimizePricing(request: PricingOptimizationRequest): Promise<PricingOptimizationResult> {
    try {
      // Create market conditions embedding
      const marketText = [
        `current_price:${request.currentPrice}`,
        `competitor_avg:${request.marketConditions.competitorPrices.reduce((a, b) => a + b, 0) / request.marketConditions.competitorPrices.length}`,
        `seasonality:${request.marketConditions.seasonality}`,
        `demand:${request.demandSignals.reduce((sum, signal) => sum + signal.searchVolume, 0)}`,
      ].join(' ');

      const marketEmbedding = await this.generateTextEmbedding(marketText);
      
      // Search for similar market conditions
      const searchRequest = {
        vector: marketEmbedding,
        topK: 20,
        includeMetadata: true,
        filter: { type: 'pricing_data' },
      };

      const results = await this.contentIndex.query(searchRequest);
      
      // Calculate recommended price based on similar scenarios
      let recommendedPrice = request.currentPrice;
      const reasoning: string[] = [];
      
      if (results.matches.length > 0) {
        const avgOptimalPrice = results.matches.reduce((sum, match) => {
          return sum + (match.metadata?.optimal_price || request.currentPrice);
        }, 0) / results.matches.length;
        
        recommendedPrice = avgOptimalPrice;
        reasoning.push('Based on similar market conditions');
      }

      // Apply demand-based adjustments
      const totalDemand = request.demandSignals.reduce((sum, signal) => sum + signal.searchVolume, 0);
      if (totalDemand > 1000) {
        recommendedPrice *= 1.1;
        reasoning.push('High demand detected');
      } else if (totalDemand < 100) {
        recommendedPrice *= 0.9;
        reasoning.push('Low demand, price reduction recommended');
      }

      return {
        recommendedPrice: Math.round(recommendedPrice * 100) / 100,
        priceRange: {
          min: Math.round(recommendedPrice * 0.8 * 100) / 100,
          max: Math.round(recommendedPrice * 1.2 * 100) / 100,
        },
        confidence: 0.75,
        reasoning,
        expectedDemand: totalDemand * 1.2,
        revenueProjection: recommendedPrice * totalDemand * 1.2,
      };
    } catch (error) {
      this.logger.error('Failed to optimize pricing', error);
      throw error;
    }
  }

  // ==================== UTILITY METHODS ====================

  async deleteVector(indexName: string, id: string): Promise<void> {
    try {
      const index = this.pinecone.index(indexName);
      await index.deleteOne(id);
      this.logger.log(`Vector ${id} deleted from ${indexName}`);
    } catch (error) {
      this.logger.error(`Failed to delete vector ${id} from ${indexName}`, error);
      throw error;
    }
  }

  async getIndexStats(indexName: string): Promise<any> {
    try {
      const index = this.pinecone.index(indexName);
      return await index.describeIndexStats();
    } catch (error) {
      this.logger.error(`Failed to get stats for ${indexName}`, error);
      throw error;
    }
  }
}
