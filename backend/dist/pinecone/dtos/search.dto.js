"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContentModerationResultDto = exports.FraudDetectionResultDto = exports.ContentModerationDto = exports.ContentType = exports.FraudDetectionDto = exports.UserBehaviorDto = exports.UserBehaviorAction = exports.RecommendationResultDto = exports.SearchResultDto = exports.RecommendationsDto = exports.SimilarEventsDto = exports.SearchEventsDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
class SearchEventsDto {
    constructor() {
        this.topK = 10;
        this.includeMetadata = true;
    }
}
exports.SearchEventsDto = SearchEventsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Search query text',
        example: 'rock concert in New York this weekend',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SearchEventsDto.prototype, "query", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of results to return',
        example: 10,
        minimum: 1,
        maximum: 100,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], SearchEventsDto.prototype, "topK", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include metadata in results',
        example: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SearchEventsDto.prototype, "includeMetadata", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter criteria',
        example: { category: 'music', price: { $lte: 100 } },
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], SearchEventsDto.prototype, "filter", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Namespace to search in',
        example: 'production',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SearchEventsDto.prototype, "namespace", void 0);
class SimilarEventsDto {
    constructor() {
        this.limit = 5;
        this.threshold = 0.7;
        this.includeMetadata = true;
    }
}
exports.SimilarEventsDto = SimilarEventsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Event ID to find similar events for',
        example: 'event-123',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SimilarEventsDto.prototype, "eventId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of similar events to return',
        example: 5,
        minimum: 1,
        maximum: 50,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(50),
    __metadata("design:type", Number)
], SimilarEventsDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum similarity threshold',
        example: 0.7,
        minimum: 0,
        maximum: 1,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(1),
    __metadata("design:type", Number)
], SimilarEventsDto.prototype, "threshold", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include metadata in results',
        example: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], SimilarEventsDto.prototype, "includeMetadata", void 0);
class RecommendationsDto {
    constructor() {
        this.limit = 10;
    }
}
exports.RecommendationsDto = RecommendationsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID to get recommendations for',
        example: 'user-456',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RecommendationsDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of recommendations to return',
        example: 10,
        minimum: 1,
        maximum: 50,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(50),
    __metadata("design:type", Number)
], RecommendationsDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by event categories',
        example: ['music', 'sports'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], RecommendationsDto.prototype, "categories", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Price range filter',
        example: { min: 20, max: 200 },
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], RecommendationsDto.prototype, "priceRange", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Date range filter',
        example: { start: '2024-01-01', end: '2024-12-31' },
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], RecommendationsDto.prototype, "dateRange", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Location filter',
        example: 'New York, NY',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RecommendationsDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Event IDs to exclude from recommendations',
        example: ['event-123', 'event-456'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], RecommendationsDto.prototype, "excludeEventIds", void 0);
class SearchResultDto {
}
exports.SearchResultDto = SearchResultDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Result ID',
        example: 'event-123',
    }),
    __metadata("design:type", String)
], SearchResultDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Similarity score',
        example: 0.95,
    }),
    __metadata("design:type", Number)
], SearchResultDto.prototype, "score", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Result metadata',
    }),
    __metadata("design:type", Object)
], SearchResultDto.prototype, "metadata", void 0);
class RecommendationResultDto {
}
exports.RecommendationResultDto = RecommendationResultDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Event ID',
        example: 'event-123',
    }),
    __metadata("design:type", String)
], RecommendationResultDto.prototype, "eventId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recommendation score',
        example: 0.92,
    }),
    __metadata("design:type", Number)
], RecommendationResultDto.prototype, "score", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Reason for recommendation',
        example: 'Perfect match for your preferences',
    }),
    __metadata("design:type", String)
], RecommendationResultDto.prototype, "reason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Confidence level',
        example: 0.88,
    }),
    __metadata("design:type", Number)
], RecommendationResultDto.prototype, "confidence", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Event metadata',
    }),
    __metadata("design:type", Object)
], RecommendationResultDto.prototype, "metadata", void 0);
var UserBehaviorAction;
(function (UserBehaviorAction) {
    UserBehaviorAction["VIEW"] = "view";
    UserBehaviorAction["SEARCH"] = "search";
    UserBehaviorAction["CLICK"] = "click";
    UserBehaviorAction["PURCHASE"] = "purchase";
    UserBehaviorAction["SHARE"] = "share";
})(UserBehaviorAction || (exports.UserBehaviorAction = UserBehaviorAction = {}));
class UserBehaviorDto {
}
exports.UserBehaviorDto = UserBehaviorDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User action timestamp',
        example: '2024-01-01T12:00:00Z',
    }),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Date)
], UserBehaviorDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of user action',
        enum: UserBehaviorAction,
        example: UserBehaviorAction.VIEW,
    }),
    (0, class_validator_1.IsEnum)(UserBehaviorAction),
    __metadata("design:type", String)
], UserBehaviorDto.prototype, "action", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Event ID if action is related to an event',
        example: 'event-123',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserBehaviorDto.prototype, "eventId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search query if action is search',
        example: 'rock concert',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserBehaviorDto.prototype, "query", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Duration of action in milliseconds',
        example: 30000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UserBehaviorDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional metadata',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], UserBehaviorDto.prototype, "metadata", void 0);
class FraudDetectionDto {
}
exports.FraudDetectionDto = FraudDetectionDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID',
        example: 'user-456',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FraudDetectionDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Event ID',
        example: 'event-123',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FraudDetectionDto.prototype, "eventId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Purchase amount',
        example: 150.00,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FraudDetectionDto.prototype, "purchaseAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User behavior history',
        type: [UserBehaviorDto],
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_transformer_1.Type)(() => UserBehaviorDto),
    __metadata("design:type", Array)
], FraudDetectionDto.prototype, "userBehavior", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Device information',
        example: { userAgent: 'Mozilla/5.0...', ip: '***********' },
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], FraudDetectionDto.prototype, "deviceInfo", void 0);
var ContentType;
(function (ContentType) {
    ContentType["REVIEW"] = "review";
    ContentType["COMMENT"] = "comment";
    ContentType["DESCRIPTION"] = "description";
})(ContentType || (exports.ContentType = ContentType = {}));
class ContentModerationDto {
}
exports.ContentModerationDto = ContentModerationDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Content to moderate',
        example: 'This event was amazing!',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContentModerationDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of content',
        enum: ContentType,
        example: ContentType.REVIEW,
    }),
    (0, class_validator_1.IsEnum)(ContentType),
    __metadata("design:type", String)
], ContentModerationDto.prototype, "contentType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID who created the content',
        example: 'user-456',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContentModerationDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Event ID if content is related to an event',
        example: 'event-123',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContentModerationDto.prototype, "eventId", void 0);
class FraudDetectionResultDto {
}
exports.FraudDetectionResultDto = FraudDetectionResultDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Risk score (0-100)',
        example: 25,
    }),
    __metadata("design:type", Number)
], FraudDetectionResultDto.prototype, "riskScore", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Risk level',
        example: 'LOW',
    }),
    __metadata("design:type", String)
], FraudDetectionResultDto.prototype, "riskLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Detected risk factors',
        example: ['unusual_purchase_amount', 'new_device'],
    }),
    __metadata("design:type", Array)
], FraudDetectionResultDto.prototype, "riskFactors", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recommended action',
        example: 'ALLOW',
    }),
    __metadata("design:type", String)
], FraudDetectionResultDto.prototype, "recommendedAction", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Confidence in the assessment',
        example: 0.85,
    }),
    __metadata("design:type", Number)
], FraudDetectionResultDto.prototype, "confidence", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional details',
    }),
    __metadata("design:type", Object)
], FraudDetectionResultDto.prototype, "details", void 0);
class ContentModerationResultDto {
}
exports.ContentModerationResultDto = ContentModerationResultDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether content is appropriate',
        example: true,
    }),
    __metadata("design:type", Boolean)
], ContentModerationResultDto.prototype, "isAppropriate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Toxicity score (0-1)',
        example: 0.15,
    }),
    __metadata("design:type", Number)
], ContentModerationResultDto.prototype, "toxicityScore", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Content sentiment',
        example: 'POSITIVE',
    }),
    __metadata("design:type", String)
], ContentModerationResultDto.prototype, "sentiment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Detected categories',
        example: ['positive_feedback', 'event_review'],
    }),
    __metadata("design:type", Array)
], ContentModerationResultDto.prototype, "categories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Confidence in moderation decision',
        example: 0.92,
    }),
    __metadata("design:type", Number)
], ContentModerationResultDto.prototype, "confidence", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Suggested actions',
        example: ['approve'],
    }),
    __metadata("design:type", Array)
], ContentModerationResultDto.prototype, "suggestedActions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional analysis details',
    }),
    __metadata("design:type", Object)
], ContentModerationResultDto.prototype, "details", void 0);
//# sourceMappingURL=search.dto.js.map