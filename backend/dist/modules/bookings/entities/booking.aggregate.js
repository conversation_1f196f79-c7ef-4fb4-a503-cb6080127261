"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BookingAggregate = exports.BookingStatus = void 0;
const aggregate_root_1 = require("../../../common/entities/aggregate-root");
const booking_initiated_event_1 = require("../events/booking-initiated.event");
const seats_reserved_event_1 = require("../events/seats-reserved.event");
const payment_processed_event_1 = require("../events/payment-processed.event");
const booking_confirmed_event_1 = require("../events/booking-confirmed.event");
const booking_cancelled_event_1 = require("../events/booking-cancelled.event");
const booking_expired_event_1 = require("../events/booking-expired.event");
const refund_processed_event_1 = require("../events/refund-processed.event");
var BookingStatus;
(function (BookingStatus) {
    BookingStatus["INITIATED"] = "INITIATED";
    BookingStatus["SEATS_RESERVED"] = "SEATS_RESERVED";
    BookingStatus["PAYMENT_PENDING"] = "PAYMENT_PENDING";
    BookingStatus["PAYMENT_PROCESSING"] = "PAYMENT_PROCESSING";
    BookingStatus["CONFIRMED"] = "CONFIRMED";
    BookingStatus["CANCELLED"] = "CANCELLED";
    BookingStatus["EXPIRED"] = "EXPIRED";
    BookingStatus["REFUNDED"] = "REFUNDED";
})(BookingStatus || (exports.BookingStatus = BookingStatus = {}));
class BookingAggregate extends aggregate_root_1.AggregateRoot {
    constructor(id) {
        super(id);
        this._seatIds = [];
        this._status = BookingStatus.INITIATED;
    }
    static initiate(bookingData) {
        const booking = new BookingAggregate();
        booking.apply(new booking_initiated_event_1.BookingInitiatedEvent(booking.id, bookingData, booking.version + 1));
        return booking;
    }
    reserveSeats(expirationMinutes = 15) {
        if (this._status !== BookingStatus.INITIATED) {
            throw new Error('Can only reserve seats for initiated bookings');
        }
        const expiresAt = new Date();
        expiresAt.setMinutes(expiresAt.getMinutes() + expirationMinutes);
        this.apply(new seats_reserved_event_1.SeatsReservedEvent(this.id, this._eventId, this._seatIds, expiresAt, this.version + 1));
    }
    processPayment(paymentData) {
        if (this._status !== BookingStatus.SEATS_RESERVED && this._status !== BookingStatus.PAYMENT_PENDING) {
            throw new Error('Can only process payment for bookings with reserved seats');
        }
        if (this._reservationExpiresAt && new Date() > this._reservationExpiresAt) {
            throw new Error('Seat reservation has expired');
        }
        this.apply(new payment_processed_event_1.PaymentProcessedEvent(this.id, paymentData, this.version + 1));
    }
    confirm() {
        if (this._status !== BookingStatus.PAYMENT_PROCESSING) {
            throw new Error('Can only confirm bookings with processed payment');
        }
        this.apply(new booking_confirmed_event_1.BookingConfirmedEvent(this.id, new Date(), this.version + 1));
    }
    cancel(reason) {
        if (this._status === BookingStatus.CANCELLED ||
            this._status === BookingStatus.EXPIRED ||
            this._status === BookingStatus.REFUNDED) {
            throw new Error('Booking is already cancelled, expired, or refunded');
        }
        this.apply(new booking_cancelled_event_1.BookingCancelledEvent(this.id, reason, new Date(), this.version + 1));
    }
    expire() {
        if (this._status !== BookingStatus.SEATS_RESERVED && this._status !== BookingStatus.PAYMENT_PENDING) {
            throw new Error('Can only expire bookings with reserved seats');
        }
        this.apply(new booking_expired_event_1.BookingExpiredEvent(this.id, new Date(), this.version + 1));
    }
    processRefund(refundAmount, refundId) {
        if (this._status !== BookingStatus.CONFIRMED) {
            throw new Error('Can only refund confirmed bookings');
        }
        this.apply(new refund_processed_event_1.RefundProcessedEvent(this.id, refundAmount, refundId, new Date(), this.version + 1));
    }
    applyEvent(event) {
        switch (event.constructor.name) {
            case booking_initiated_event_1.BookingInitiatedEvent.name:
                this.onBookingInitiated(event);
                break;
            case seats_reserved_event_1.SeatsReservedEvent.name:
                this.onSeatsReserved(event);
                break;
            case payment_processed_event_1.PaymentProcessedEvent.name:
                this.onPaymentProcessed(event);
                break;
            case booking_confirmed_event_1.BookingConfirmedEvent.name:
                this.onBookingConfirmed(event);
                break;
            case booking_cancelled_event_1.BookingCancelledEvent.name:
                this.onBookingCancelled(event);
                break;
            case booking_expired_event_1.BookingExpiredEvent.name:
                this.onBookingExpired(event);
                break;
            case refund_processed_event_1.RefundProcessedEvent.name:
                this.onRefundProcessed(event);
                break;
        }
    }
    onBookingInitiated(event) {
        this._eventId = event.bookingData.eventId;
        this._customerId = event.bookingData.customerId;
        this._seatIds = event.bookingData.seatIds;
        this._totalAmount = event.bookingData.totalAmount;
        this._currency = event.bookingData.currency;
        this._customerEmail = event.bookingData.customerEmail;
        this._customerPhone = event.bookingData.customerPhone;
        this._status = BookingStatus.INITIATED;
    }
    onSeatsReserved(event) {
        this._status = BookingStatus.SEATS_RESERVED;
        this._reservationExpiresAt = event.expiresAt;
    }
    onPaymentProcessed(event) {
        this._status = BookingStatus.PAYMENT_PROCESSING;
        this._paymentData = event.paymentData;
    }
    onBookingConfirmed(event) {
        this._status = BookingStatus.CONFIRMED;
        this._confirmedAt = event.confirmedAt;
    }
    onBookingCancelled(event) {
        this._status = BookingStatus.CANCELLED;
        this._cancelledAt = event.cancelledAt;
    }
    onBookingExpired(event) {
        this._status = BookingStatus.EXPIRED;
    }
    onRefundProcessed(event) {
        this._status = BookingStatus.REFUNDED;
        this._refundAmount = event.refundAmount;
    }
    get eventId() { return this._eventId; }
    get customerId() { return this._customerId; }
    get seatIds() { return [...this._seatIds]; }
    get totalAmount() { return this._totalAmount; }
    get currency() { return this._currency; }
    get customerEmail() { return this._customerEmail; }
    get customerPhone() { return this._customerPhone; }
    get status() { return this._status; }
    get paymentData() { return this._paymentData; }
    get reservationExpiresAt() { return this._reservationExpiresAt; }
    get confirmedAt() { return this._confirmedAt; }
    get cancelledAt() { return this._cancelledAt; }
    get refundAmount() { return this._refundAmount; }
    get isExpired() {
        return this._reservationExpiresAt ? new Date() > this._reservationExpiresAt : false;
    }
}
exports.BookingAggregate = BookingAggregate;
//# sourceMappingURL=booking.aggregate.js.map