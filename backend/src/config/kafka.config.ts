import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Kafka, Producer, Consumer, KafkaMessage } from 'kafkajs';

@Injectable()
export class KafkaConfig implements OnModuleInit, OnModuleDestroy {
  private kafka: Kafka;
  private producer: Producer;
  private consumer: Consumer;

  constructor(private configService: ConfigService) {
    this.kafka = new Kafka({
      clientId: this.configService.get('KAFKA_CLIENT_ID', 'ticket-booking-backend'),
      brokers: this.configService.get('KAFKA_BROKERS', 'localhost:9092').split(','),
      retry: {
        initialRetryTime: 100,
        retries: 8,
      },
    });

    this.producer = this.kafka.producer({
      maxInFlightRequests: 1,
      idempotent: true,
      transactionTimeout: 30000,
    });

    this.consumer = this.kafka.consumer({
      groupId: this.configService.get('KAFKA_GROUP_ID', 'ticket-booking-group'),
    });
  }

  async onModuleInit() {
    await this.producer.connect();
    await this.consumer.connect();
    console.log('✅ Kafka connected successfully');
  }

  async onModuleDestroy() {
    await this.producer.disconnect();
    await this.consumer.disconnect();
    console.log('🔌 Kafka disconnected');
  }

  async publishEvent(topic: string, message: any): Promise<void> {
    try {
      await this.producer.send({
        topic,
        messages: [
          {
            key: message.aggregateId || message.id,
            value: JSON.stringify(message),
            timestamp: Date.now().toString(),
          },
        ],
      });
    } catch (error) {
      console.error('Failed to publish event to Kafka:', error);
      throw error;
    }
  }

  async subscribe(topic: string, callback: (message: KafkaMessage) => Promise<void>): Promise<void> {
    await this.consumer.subscribe({ topic, fromBeginning: false });
    
    await this.consumer.run({
      eachMessage: async ({ topic, partition, message }) => {
        try {
          await callback(message);
        } catch (error) {
          console.error(`Error processing message from topic ${topic}:`, error);
          // Implement dead letter queue logic here if needed
        }
      },
    });
  }

  async createTopics(topics: string[]): Promise<void> {
    const admin = this.kafka.admin();
    await admin.connect();
    
    try {
      await admin.createTopics({
        topics: topics.map(topic => ({
          topic,
          numPartitions: 3,
          replicationFactor: 1,
        })),
      });
      console.log(`✅ Kafka topics created: ${topics.join(', ')}`);
    } catch (error) {
      console.error('Failed to create Kafka topics:', error);
    } finally {
      await admin.disconnect();
    }
  }
}
