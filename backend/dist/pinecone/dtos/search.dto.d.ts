export declare class SearchEventsDto {
    query: string;
    topK?: number;
    includeMetadata?: boolean;
    filter?: Record<string, any>;
    namespace?: string;
}
export declare class SimilarEventsDto {
    eventId: string;
    limit?: number;
    threshold?: number;
    includeMetadata?: boolean;
}
export declare class RecommendationsDto {
    userId: string;
    limit?: number;
    categories?: string[];
    priceRange?: {
        min: number;
        max: number;
    };
    dateRange?: {
        start: string;
        end: string;
    };
    location?: string;
    excludeEventIds?: string[];
}
export declare class SearchResultDto {
    id: string;
    score: number;
    metadata?: any;
}
export declare class RecommendationResultDto {
    eventId: string;
    score: number;
    reason: string;
    confidence: number;
    metadata?: any;
}
export declare enum UserBehaviorAction {
    VIEW = "view",
    SEARCH = "search",
    CLICK = "click",
    PURCHASE = "purchase",
    SHARE = "share"
}
export declare class UserBehaviorDto {
    timestamp: Date;
    action: UserBehaviorAction;
    eventId?: string;
    query?: string;
    duration?: number;
    metadata?: Record<string, any>;
}
export declare class FraudDetectionDto {
    userId: string;
    eventId: string;
    purchaseAmount: number;
    userBehavior: UserBehaviorDto[];
    deviceInfo?: Record<string, any>;
}
export declare enum ContentType {
    REVIEW = "review",
    COMMENT = "comment",
    DESCRIPTION = "description"
}
export declare class ContentModerationDto {
    content: string;
    contentType: ContentType;
    userId: string;
    eventId?: string;
}
export declare class FraudDetectionResultDto {
    riskScore: number;
    riskLevel: string;
    riskFactors: string[];
    recommendedAction: string;
    confidence: number;
    details?: any;
}
export declare class ContentModerationResultDto {
    isAppropriate: boolean;
    toxicityScore: number;
    sentiment: string;
    categories: string[];
    confidence: number;
    suggestedActions?: string[];
    details?: any;
}
