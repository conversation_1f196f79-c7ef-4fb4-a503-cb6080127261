import { Command } from '../../../common/commands/base.command';
export interface PaymentDetails {
    amount: number;
    currency: string;
    paymentMethodId: string;
    customerId: string;
    description: string;
    metadata?: Record<string, any>;
}
export declare class ChargePaymentCommand extends Command {
    readonly paymentId: string;
    readonly paymentDetails: PaymentDetails;
    constructor(paymentId: string, paymentDetails: PaymentDetails);
}
