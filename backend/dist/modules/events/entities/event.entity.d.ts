export declare enum EventStatus {
    DRAFT = "DRAFT",
    PUBLISHED = "PUBLISHED",
    CANCELLED = "CANCELLED",
    COMPLETED = "COMPLETED"
}
export declare enum EventCategory {
    CONCERT = "CONCERT",
    SPORTS = "SPORTS",
    THEATER = "THEATER",
    CONFERENCE = "CONFERENCE",
    FESTIVAL = "FESTIVAL",
    OTHER = "OTHER"
}
export declare class EventEntity {
    id: string;
    title: string;
    description: string;
    category: EventCategory;
    startDate: Date;
    endDate: Date;
    location: string;
    venue: string;
    address: string;
    latitude: number;
    longitude: number;
    totalSeats: number;
    availableSeats: number;
    basePrice: number;
    status: EventStatus;
    organizerId: string;
    externalId: string;
    externalSource: string;
    metadata: any;
    tags: string[];
    imageUrl: string;
    createdAt: Date;
    updatedAt: Date;
}
