import formatDistance from "./_lib/formatDistance/index.js";
import formatLong from "./_lib/formatLong/index.js";
import formatRelative from "./_lib/formatRelative/index.js";
import localize from "./_lib/localize/index.js";
import match from "./_lib/match/index.js";
/**
 * @type {Locale}
 * @category Locales
 * @summary Turkish locale.
 * @language Turkish
 * @iso-639-2 tur
 * <AUTHOR> [@alpcanaydin]{@link https://github.com/alpcanaydin}
 * <AUTHOR> [@berkaey]{@link https://github.com/berkaey}
 * <AUTHOR> <PERSON>t [@bulutfatih]{@link https://github.com/bulutfatih}
 * <AUTHOR> [@dbtek]{@link https://github.com/dbtek}
 * <AUTHOR> [@ikayar]{@link https://github.com/ikayar}
 *
 *
 */
var locale = {
  code: 'tr',
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1
  }
};
export default locale;