import { ICommand } from '@nestjs/cqrs';
import { Observable } from 'rxjs';
import { BaseSaga } from '../../../common/sagas/base.saga';
export declare class SyncToExternalAPICommand {
    readonly eventId: string;
    readonly apiProvider: string;
    readonly action: 'create' | 'update' | 'delete';
    constructor(eventId: string, apiProvider: string, action: 'create' | 'update' | 'delete');
}
export declare class UpdateSearchIndexCommand {
    readonly eventId: string;
    readonly action: 'index' | 'update' | 'delete';
    constructor(eventId: string, action: 'index' | 'update' | 'delete');
}
export declare class GenerateEventReportCommand {
    readonly eventId: string;
    readonly reportType: 'sales' | 'attendance' | 'analytics';
    constructor(eventId: string, reportType: 'sales' | 'attendance' | 'analytics');
}
export declare class EventManagementSaga extends BaseSaga {
    eventCreated: (events$: Observable<any>) => Observable<ICommand>;
    eventPublished: (events$: Observable<any>) => Observable<ICommand>;
    eventCancelled: (events$: Observable<any>) => Observable<ICommand>;
    seatReserved: (events$: Observable<any>) => Observable<ICommand>;
    eventCompleted: (events$: Observable<any>) => Observable<ICommand>;
    handle(event: any): Observable<ICommand>;
}
