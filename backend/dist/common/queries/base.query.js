"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Query = exports.BaseQuery = void 0;
class BaseQuery {
    constructor() {
        this.queryId = this.generateQueryId();
        this.timestamp = new Date();
    }
    generateQueryId() {
        return `qry-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
}
exports.BaseQuery = BaseQuery;
class Query extends BaseQuery {
    constructor() {
        super();
    }
}
exports.Query = Query;
//# sourceMappingURL=base.query.js.map