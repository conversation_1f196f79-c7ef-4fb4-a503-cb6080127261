import { PineconeService } from '../services/pinecone.service';
import { SearchEventsDto, SimilarEventsDto, RecommendationsDto, SearchResultDto, RecommendationResultDto, FraudDetectionDto, ContentModerationDto, FraudDetectionResultDto, ContentModerationResultDto } from '../dtos/search.dto';
import { AnalyticsQueryDto, PricingOptimizationDto, PricingOptimizationResultDto, AnalyticsResultDto } from '../dtos/analytics.dto';
export declare class PineconeController {
    private readonly pineconeService;
    constructor(pineconeService: PineconeService);
    searchEvents(searchDto: SearchEventsDto): Promise<SearchResultDto[]>;
    findSimilarEvents(similarDto: SimilarEventsDto): Promise<SearchResultDto[]>;
    getRecommendations(recommendationsDto: RecommendationsDto): Promise<RecommendationResultDto[]>;
    performAnalytics(analyticsDto: AnalyticsQueryDto): Promise<AnalyticsResultDto>;
    detectFraud(fraudDto: FraudDetectionDto): Promise<FraudDetectionResultDto>;
    moderateContent(moderationDto: ContentModerationDto): Promise<ContentModerationResultDto>;
    optimizePricing(pricingDto: PricingOptimizationDto): Promise<PricingOptimizationResultDto>;
    healthCheck(): Promise<{
        status: string;
        services: any;
    }>;
    getIndexStats(indexName: string): Promise<any>;
    generateTextEmbedding(text: string): Promise<{
        embedding: number[];
        dimensions: number;
    }>;
    batchUpsertEvents(events: any[]): Promise<{
        processed: number;
        errors: any[];
    }>;
    batchUpsertUsers(users: any[]): Promise<{
        processed: number;
        errors: any[];
    }>;
}
