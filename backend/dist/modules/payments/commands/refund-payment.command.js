"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RefundPaymentCommand = void 0;
const base_command_1 = require("../../../common/commands/base.command");
class RefundPaymentCommand extends base_command_1.Command {
    constructor(paymentId, refundAmount, reason) {
        super();
        this.paymentId = paymentId;
        this.refundAmount = refundAmount;
        this.reason = reason;
    }
}
exports.RefundPaymentCommand = RefundPaymentCommand;
//# sourceMappingURL=refund-payment.command.js.map