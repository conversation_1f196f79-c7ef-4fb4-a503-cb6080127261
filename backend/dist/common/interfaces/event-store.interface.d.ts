import { BaseEvent } from '../events/base.event';
export interface EventStore {
    saveEvents(aggregateId: string, events: BaseEvent[], expectedVersion: number): Promise<void>;
    getEvents(aggregateId: string, fromVersion?: number): Promise<BaseEvent[]>;
    getAllEvents(fromTimestamp?: Date): Promise<BaseEvent[]>;
    getEventsByType(eventType: string, fromTimestamp?: Date): Promise<BaseEvent[]>;
}
export interface EventStoreRecord {
    id: string;
    aggregateId: string;
    eventId: string;
    eventType: string;
    eventData: string;
    eventMetadata: string;
    version: number;
    timestamp: Date;
}
export interface Snapshot {
    aggregateId: string;
    aggregateType: string;
    data: string;
    version: number;
    timestamp: Date;
}
export interface SnapshotStore {
    saveSnapshot(snapshot: Snapshot): Promise<void>;
    getSnapshot(aggregateId: string): Promise<Snapshot | null>;
}
