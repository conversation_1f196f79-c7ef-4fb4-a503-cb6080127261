# Outputs for EKS Kubernetes Deployment

# Cluster Information
output "cluster_id" {
  description = "EKS cluster ID"
  value       = module.eks.cluster_id
}

output "cluster_arn" {
  description = "EKS cluster ARN"
  value       = module.eks.cluster_arn
}

output "cluster_name" {
  description = "EKS cluster name"
  value       = module.eks.cluster_name
}

output "cluster_endpoint" {
  description = "Endpoint for EKS control plane"
  value       = module.eks.cluster_endpoint
}

output "cluster_version" {
  description = "The Kubernetes version for the EKS cluster"
  value       = module.eks.cluster_version
}

output "cluster_platform_version" {
  description = "Platform version for the EKS cluster"
  value       = module.eks.cluster_platform_version
}

output "cluster_status" {
  description = "Status of the EKS cluster. One of `CREATING`, `ACTIVE`, `DELETING`, `FAILED`"
  value       = module.eks.cluster_status
}

# Node Groups
output "eks_managed_node_groups" {
  description = "Map of attribute maps for all EKS managed node groups created"
  value       = module.eks.eks_managed_node_groups
}

output "node_groups" {
  description = "EKS node groups"
  value = {
    worker_group_1 = {
      name         = "${var.project_name}-${var.environment}-worker-group-1"
      capacity     = "1-2 nodes"
      instance_type = var.worker_instance_type
    }
    worker_group_2 = {
      name         = "${var.project_name}-${var.environment}-worker-group-2"
      capacity     = "1-2 nodes"
      instance_type = var.worker_instance_type
    }
    worker_group_3 = {
      name         = "${var.project_name}-${var.environment}-worker-group-3"
      capacity     = "1-2 nodes"
      instance_type = var.worker_instance_type
    }
  }
}

# VPC Information
output "vpc_id" {
  description = "ID of the VPC where the cluster and its nodes will be provisioned"
  value       = module.vpc.vpc_id
}

output "vpc_cidr_block" {
  description = "The CIDR block of the VPC"
  value       = module.vpc.vpc_cidr_block
}

output "private_subnets" {
  description = "List of IDs of private subnets"
  value       = module.vpc.private_subnets
}

output "public_subnets" {
  description = "List of IDs of public subnets"
  value       = module.vpc.public_subnets
}

# Security
output "cluster_security_group_id" {
  description = "Security group ID attached to the EKS cluster"
  value       = module.eks.cluster_security_group_id
}

output "node_security_group_id" {
  description = "ID of the node shared security group"
  value       = module.eks.node_security_group_id
}

# OIDC
output "cluster_oidc_issuer_url" {
  description = "The URL on the EKS cluster for the OpenID Connect identity provider"
  value       = module.eks.cluster_oidc_issuer_url
}

output "oidc_provider_arn" {
  description = "The ARN of the OIDC Provider if enabled"
  value       = module.eks.oidc_provider_arn
}

# Kubectl Configuration
output "kubectl_config" {
  description = "kubectl config as generated by the module"
  value = {
    cluster_name = module.eks.cluster_name
    endpoint     = module.eks.cluster_endpoint
    region       = var.aws_region
    
    # Command to update kubeconfig
    update_kubeconfig_command = "aws eks update-kubeconfig --region ${var.aws_region} --name ${module.eks.cluster_name}"
  }
}

# Application URLs (will be available after ingress deployment)
output "application_urls" {
  description = "Application URLs (available after ingress deployment)"
  value = {
    note = "URLs will be available after deploying ingress controller and applications"
    frontend_service = "kubectl get svc frontend-service -n ticket-booking"
    backend_service  = "kubectl get svc backend-service -n ticket-booking"
    ingress_info    = "kubectl get ingress -n ticket-booking"
  }
}

# Monitoring
output "monitoring_info" {
  description = "Monitoring stack information"
  value = {
    prometheus_url = "kubectl port-forward svc/prometheus-server 9090:80 -n monitoring"
    grafana_url    = "kubectl port-forward svc/grafana 3000:80 -n monitoring"
    grafana_password = "kubectl get secret grafana -n monitoring -o jsonpath='{.data.admin-password}' | base64 -d"
  }
}

# Scaling Information
output "scaling_info" {
  description = "Auto-scaling configuration"
  value = {
    cluster_autoscaler = "Installed via Helm"
    hpa_enabled       = "Horizontal Pod Autoscaler ready"
    node_groups       = "3 worker node groups with auto-scaling"
    total_capacity    = "3-6 worker nodes (1-2 per group)"
  }
}

# Cost Information
output "cost_optimization" {
  description = "Cost optimization features enabled"
  value = {
    spot_instances    = var.capacity_type == "SPOT" ? "Enabled" : "Disabled"
    single_nat       = var.single_nat_gateway ? "Enabled" : "Disabled"
    node_groups      = "3 groups for workload distribution"
    auto_scaling     = "Enabled for cost efficiency"
  }
}
