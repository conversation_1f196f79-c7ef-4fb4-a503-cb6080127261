import { IEvent } from '@nestjs/cqrs';
export declare abstract class BaseEvent implements IEvent {
    readonly aggregateId: string;
    readonly eventId: string;
    readonly eventType: string;
    readonly occurredOn: Date;
    readonly version: number;
    constructor(aggregateId: string, version?: number);
    private generateEventId;
}
export declare abstract class DomainEvent extends BaseEvent {
    constructor(aggregateId: string, version?: number);
}
export declare abstract class IntegrationEvent extends BaseEvent {
    constructor(aggregateId: string, version?: number);
}
