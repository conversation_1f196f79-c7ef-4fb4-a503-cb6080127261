"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SagaOrchestratorService = void 0;
const common_1 = require("@nestjs/common");
const cqrs_1 = require("@nestjs/cqrs");
const base_saga_1 = require("./base.saga");
const ticket_booking_saga_1 = require("../../modules/bookings/sagas/ticket-booking.saga");
const payment_processing_saga_1 = require("../../modules/payments/sagas/payment-processing.saga");
const event_management_saga_1 = require("../../modules/events/sagas/event-management.saga");
const notification_orchestration_saga_1 = require("../../modules/notifications/sagas/notification-orchestration.saga");
const kafka_config_1 = require("../../config/kafka.config");
const redis_config_1 = require("../../config/redis.config");
let SagaOrchestratorService = class SagaOrchestratorService {
    constructor(eventBus, commandBus, kafkaConfig, redisConfig, ticketBookingSaga, paymentProcessingSaga, eventManagementSaga, notificationOrchestrationSaga) {
        this.eventBus = eventBus;
        this.commandBus = commandBus;
        this.kafkaConfig = kafkaConfig;
        this.redisConfig = redisConfig;
        this.ticketBookingSaga = ticketBookingSaga;
        this.paymentProcessingSaga = paymentProcessingSaga;
        this.eventManagementSaga = eventManagementSaga;
        this.notificationOrchestrationSaga = notificationOrchestrationSaga;
        this.activeSagas = new Map();
        this.sagaSubscriptions = new Map();
        this.sagaInstances = new Map();
    }
    async onModuleInit() {
        await this.initializeSagaMonitoring();
        await this.recoverInProgressSagas();
        console.log('🎭 Saga Orchestrator initialized');
    }
    async startSaga(sagaType, event, metadata) {
        const sagaId = this.generateSagaId();
        let saga;
        switch (sagaType) {
            case 'TicketBookingSaga':
                saga = new ticket_booking_saga_1.TicketBookingSaga(this.commandBus);
                break;
            case 'PaymentProcessingSaga':
                saga = new payment_processing_saga_1.PaymentProcessingSaga(this.commandBus);
                break;
            case 'EventManagementSaga':
                saga = new event_management_saga_1.EventManagementSaga(this.commandBus);
                break;
            case 'NotificationOrchestrationSaga':
                saga = new notification_orchestration_saga_1.NotificationOrchestrationSaga(this.commandBus);
                break;
            default:
                throw new Error(`Unknown saga type: ${sagaType}`);
        }
        this.activeSagas.set(sagaId, saga);
        this.sagaInstances.set(sagaId, {
            id: sagaId,
            type: sagaType,
            status: base_saga_1.SagaStatus.STARTED,
            startedAt: new Date(),
            progress: { completed: 0, total: 0, percentage: 0 },
            metadata
        });
        const subscription = saga.handle(event).subscribe({
            next: (command) => {
                this.commandBus.execute(command);
            },
            error: (error) => {
                console.error(`Saga ${sagaId} failed:`, error);
                this.handleSagaFailure(sagaId, error);
            },
            complete: () => {
                console.log(`Saga ${sagaId} completed`);
                this.handleSagaCompletion(sagaId);
            }
        });
        this.sagaSubscriptions.set(sagaId, subscription);
        await this.persistSagaState(sagaId, saga);
        console.log(`🚀 Started ${sagaType} saga with ID: ${sagaId}`);
        return sagaId;
    }
    getSagaStatus(sagaId) {
        const instance = this.sagaInstances.get(sagaId);
        if (!instance)
            return null;
        const saga = this.activeSagas.get(sagaId);
        if (saga) {
            instance.status = saga.currentStatus;
            instance.progress = saga.progress;
        }
        return instance;
    }
    getActiveSagas() {
        return Array.from(this.sagaInstances.values())
            .filter(instance => instance.status === base_saga_1.SagaStatus.STARTED ||
            instance.status === base_saga_1.SagaStatus.IN_PROGRESS);
    }
    async cancelSaga(sagaId, reason) {
        const saga = this.activeSagas.get(sagaId);
        const subscription = this.sagaSubscriptions.get(sagaId);
        if (saga && subscription) {
            await saga['compensate']();
            subscription.unsubscribe();
            this.activeSagas.delete(sagaId);
            this.sagaSubscriptions.delete(sagaId);
            const instance = this.sagaInstances.get(sagaId);
            if (instance) {
                instance.status = base_saga_1.SagaStatus.CANCELLED;
                instance.completedAt = new Date();
                instance.metadata = { ...instance.metadata, cancellationReason: reason };
            }
            await this.persistSagaState(sagaId, null);
            console.log(`❌ Cancelled saga ${sagaId}: ${reason}`);
        }
    }
    async retrySaga(sagaId) {
        const instance = this.sagaInstances.get(sagaId);
        if (!instance || instance.status !== base_saga_1.SagaStatus.FAILED) {
            throw new Error('Saga not found or not in failed state');
        }
        const sagaState = await this.restoreSagaState(sagaId);
        if (sagaState) {
            await this.startSaga(instance.type, sagaState.lastEvent, {
                ...instance.metadata,
                isRetry: true,
                originalSagaId: sagaId
            });
        }
    }
    async handleSagaCompletion(sagaId) {
        const instance = this.sagaInstances.get(sagaId);
        if (instance) {
            instance.status = base_saga_1.SagaStatus.COMPLETED;
            instance.completedAt = new Date();
            instance.progress = { completed: instance.progress.total, total: instance.progress.total, percentage: 100 };
        }
        const subscription = this.sagaSubscriptions.get(sagaId);
        if (subscription) {
            subscription.unsubscribe();
            this.sagaSubscriptions.delete(sagaId);
        }
        this.activeSagas.delete(sagaId);
        await this.persistSagaState(sagaId, null);
        await this.kafkaConfig.publishEvent('saga-events', {
            type: 'SagaCompleted',
            sagaId,
            completedAt: new Date(),
            sagaType: instance?.type
        });
    }
    async handleSagaFailure(sagaId, error) {
        const instance = this.sagaInstances.get(sagaId);
        if (instance) {
            instance.status = base_saga_1.SagaStatus.FAILED;
            instance.completedAt = new Date();
            instance.metadata = { ...instance.metadata, error: error.message };
        }
        const saga = this.activeSagas.get(sagaId);
        if (saga) {
            try {
                await saga['compensate']();
            }
            catch (compensationError) {
                console.error(`Compensation failed for saga ${sagaId}:`, compensationError);
            }
        }
        const subscription = this.sagaSubscriptions.get(sagaId);
        if (subscription) {
            subscription.unsubscribe();
            this.sagaSubscriptions.delete(sagaId);
        }
        this.activeSagas.delete(sagaId);
        await this.persistSagaState(sagaId, null);
        await this.kafkaConfig.publishEvent('saga-events', {
            type: 'SagaFailed',
            sagaId,
            error: error.message,
            failedAt: new Date(),
            sagaType: instance?.type
        });
    }
    async persistSagaState(sagaId, saga) {
        const key = `saga:${sagaId}`;
        if (saga) {
            const state = {
                id: saga.id,
                status: saga.currentStatus,
                steps: saga['steps'],
                currentStepIndex: saga['currentStepIndex'],
                completedSteps: saga['completedSteps'],
                failedSteps: saga['failedSteps'],
                lastUpdated: new Date()
            };
            await this.redisConfig.set(key, JSON.stringify(state), 86400);
        }
        else {
            await this.redisConfig.del(key);
        }
    }
    async restoreSagaState(sagaId) {
        const key = `saga:${sagaId}`;
        const stateJson = await this.redisConfig.get(key);
        return stateJson ? JSON.parse(stateJson) : null;
    }
    async initializeSagaMonitoring() {
        setInterval(async () => {
            await this.performHealthCheck();
        }, 30000);
        setInterval(async () => {
            await this.checkSagaTimeouts();
        }, 60000);
    }
    async recoverInProgressSagas() {
        console.log('🔄 Recovering in-progress sagas...');
    }
    async performHealthCheck() {
        const activeSagas = this.getActiveSagas();
        for (const saga of activeSagas) {
            const timeSinceStart = new Date().getTime() - saga.startedAt.getTime();
            const maxSagaDuration = 30 * 60 * 1000;
            if (timeSinceStart > maxSagaDuration) {
                console.warn(`⚠️ Saga ${saga.id} has been running for ${timeSinceStart / 1000}s`);
            }
        }
    }
    async checkSagaTimeouts() {
    }
    generateSagaId() {
        return `saga-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
};
exports.SagaOrchestratorService = SagaOrchestratorService;
exports.SagaOrchestratorService = SagaOrchestratorService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [cqrs_1.EventBus,
        cqrs_1.CommandBus,
        kafka_config_1.KafkaConfig,
        redis_config_1.RedisConfig,
        ticket_booking_saga_1.TicketBookingSaga,
        payment_processing_saga_1.PaymentProcessingSaga,
        event_management_saga_1.EventManagementSaga,
        notification_orchestration_saga_1.NotificationOrchestrationSaga])
], SagaOrchestratorService);
//# sourceMappingURL=saga-orchestrator.service.js.map