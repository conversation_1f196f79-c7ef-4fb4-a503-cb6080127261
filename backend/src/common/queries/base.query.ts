import { IQuery } from '@nestjs/cqrs';

export abstract class BaseQuery implements IQuery {
  public readonly queryId: string;
  public readonly timestamp: Date;

  constructor() {
    this.queryId = this.generateQueryId();
    this.timestamp = new Date();
  }

  private generateQueryId(): string {
    return `qry-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

export abstract class Query extends BaseQuery {
  constructor() {
    super();
  }
}
