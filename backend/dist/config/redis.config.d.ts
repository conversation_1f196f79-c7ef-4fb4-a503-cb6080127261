import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';
export declare class RedisConfig {
    private configService;
    private readonly redisClient;
    constructor(configService: ConfigService);
    getClient(): Redis;
    set(key: string, value: string, ttl?: number): Promise<void>;
    get(key: string): Promise<string | null>;
    del(key: string): Promise<void>;
    exists(key: string): Promise<boolean>;
    setHash(key: string, field: string, value: string): Promise<void>;
    getHash(key: string, field: string): Promise<string | null>;
    getAllHash(key: string): Promise<Record<string, string>>;
}
