"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SnapshotEntity = exports.EventStoreEntity = void 0;
const typeorm_1 = require("typeorm");
let EventStoreEntity = class EventStoreEntity {
};
exports.EventStoreEntity = EventStoreEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], EventStoreEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'aggregate_id' }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], EventStoreEntity.prototype, "aggregateId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'event_id', unique: true }),
    __metadata("design:type", String)
], EventStoreEntity.prototype, "eventId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'event_type' }),
    __metadata("design:type", String)
], EventStoreEntity.prototype, "eventType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'event_data', type: 'jsonb' }),
    __metadata("design:type", Object)
], EventStoreEntity.prototype, "eventData", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'event_metadata', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], EventStoreEntity.prototype, "eventMetadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer' }),
    __metadata("design:type", Number)
], EventStoreEntity.prototype, "version", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'timestamp' }),
    __metadata("design:type", Date)
], EventStoreEntity.prototype, "timestamp", void 0);
exports.EventStoreEntity = EventStoreEntity = __decorate([
    (0, typeorm_1.Entity)('event_store'),
    (0, typeorm_1.Index)(['aggregateId', 'version']),
    (0, typeorm_1.Index)(['eventType', 'timestamp'])
], EventStoreEntity);
let SnapshotEntity = class SnapshotEntity {
};
exports.SnapshotEntity = SnapshotEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], SnapshotEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'aggregate_id', unique: true }),
    __metadata("design:type", String)
], SnapshotEntity.prototype, "aggregateId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'aggregate_type' }),
    __metadata("design:type", String)
], SnapshotEntity.prototype, "aggregateType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb' }),
    __metadata("design:type", Object)
], SnapshotEntity.prototype, "data", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer' }),
    __metadata("design:type", Number)
], SnapshotEntity.prototype, "version", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], SnapshotEntity.prototype, "timestamp", void 0);
exports.SnapshotEntity = SnapshotEntity = __decorate([
    (0, typeorm_1.Entity)('snapshots'),
    (0, typeorm_1.Index)(['aggregateId'])
], SnapshotEntity);
//# sourceMappingURL=event-store.entity.js.map