variable "name_prefix" {
  description = "Name prefix for resources"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID"
  type        = string
}

variable "private_subnet_ids" {
  description = "Private subnet IDs for Kafka"
  type        = list(string)
}

variable "kafka_security_group_id" {
  description = "Security group ID for Kafka"
  type        = string
}

variable "instance_type" {
  description = "MSK instance type"
  type        = string
  default     = "kafka.t3.small"
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}
