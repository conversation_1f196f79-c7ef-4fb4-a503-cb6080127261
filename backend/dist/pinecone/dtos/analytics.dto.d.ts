export declare enum AnalyticsType {
    USER_CLUSTERING = "user_clustering",
    EVENT_PERFORMANCE = "event_performance",
    TREND_ANALYSIS = "trend_analysis",
    MARKET_INSIGHTS = "market_insights"
}
export declare class AnalyticsQueryDto {
    type: AnalyticsType;
    startDate: string;
    endDate: string;
    filters?: Record<string, any>;
    groupBy?: string[];
}
export declare class FraudDetectionDto {
    userId: string;
    eventId: string;
    purchaseAmount: number;
    userBehavior: UserBehaviorDto[];
    deviceInfo?: Record<string, any>;
}
export declare class UserBehaviorDto {
    action: string;
    eventId?: string;
    query?: string;
    timestamp: string;
    duration?: number;
    metadata?: Record<string, any>;
}
export declare class ContentModerationDto {
    content: string;
    contentType: string;
    userId?: string;
    eventId?: string;
}
export declare class PricingOptimizationDto {
    eventId: string;
    currentPrice: number;
    marketConditions: MarketConditionsDto;
    demandSignals: DemandSignalDto[];
}
export declare class MarketConditionsDto {
    competitorPrices: number[];
    seasonality: number;
    economicIndicators: Record<string, number>;
    weatherForecast?: string;
}
export declare class DemandSignalDto {
    timestamp: string;
    searchVolume: number;
    viewCount: number;
    wishlistAdds: number;
    socialMentions: number;
}
export declare class FraudDetectionResultDto {
    riskScore: number;
    riskLevel: string;
    reasons: string[];
    recommendations: string[];
}
export declare class ContentModerationResultDto {
    isAppropriate: boolean;
    confidence: number;
    categories: string[];
    sentiment: string;
    toxicityScore: number;
}
export declare class PricingOptimizationResultDto {
    recommendedPrice: number;
    priceRange: {
        min: number;
        max: number;
    };
    confidence: number;
    reasoning: string[];
    expectedDemand: number;
    revenueProjection: number;
}
export declare class AnalyticsResultDto {
    type: string;
    data: any[];
    insights: string[];
    recommendations: string[];
    confidence: number;
}
