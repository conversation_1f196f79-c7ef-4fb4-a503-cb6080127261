import { Injectable } from '@nestjs/common';
import { Saga, ICommand, ofType } from '@nestjs/cqrs';
import { Observable } from 'rxjs';
import { map, delay } from 'rxjs/operators';

import { BaseSaga, SagaStep } from '../../../common/sagas/base.saga';
import { BookingInitiatedEvent } from '../events/booking-initiated.event';
import { SeatsReservedEvent } from '../events/seats-reserved.event';
import { PaymentProcessedEvent } from '../events/payment-processed.event';
import { BookingConfirmedEvent } from '../events/booking-confirmed.event';
import { BookingCancelledEvent } from '../events/booking-cancelled.event';
import { BookingExpiredEvent } from '../events/booking-expired.event';

import { ReserveSeatsCommand } from '../commands/reserve-seats.command';
import { ProcessPaymentCommand } from '../commands/process-payment.command';
import { ConfirmBookingCommand } from '../commands/confirm-booking.command';
import { CancelBookingCommand } from '../commands/cancel-booking.command';
import { SendNotificationCommand, NotificationType } from '../commands/send-notification.command';

@Injectable()
export class TicketBookingSaga extends BaseSaga {
  
  @Saga()
  bookingInitiated = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType(BookingInitiatedEvent),
      map((event: BookingInitiatedEvent) => {
        console.log(`🎫 Booking Saga: Booking initiated for ${event.aggregateId}`);
        
        // Step 1: Reserve seats
        this.addStep({
          stepId: 'reserve-seats',
          command: new ReserveSeatsCommand(
            event.aggregateId,
            event.bookingData.eventId,
            event.bookingData.seatIds,
            15 // 15 minutes expiration
          ),
          compensationCommand: new CancelBookingCommand(
            event.aggregateId,
            'Seat reservation failed'
          ),
          maxRetries: 3
        });

        this.startSaga();
        return this.steps[0].command;
      })
    );
  };

  @Saga()
  seatsReserved = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType(SeatsReservedEvent),
      map((event: SeatsReservedEvent) => {
        console.log(`🪑 Booking Saga: Seats reserved for booking ${event.aggregateId}`);
        
        // Step 2: Send reservation confirmation notification
        this.addStep({
          stepId: 'send-reservation-notification',
          command: new SendNotificationCommand(
            event.aggregateId, // Will be resolved to customer email
            NotificationType.EMAIL,
            'Seats Reserved - Complete Your Booking',
            'Your seats have been reserved. Please complete payment within 15 minutes.',
            {
              bookingId: event.aggregateId,
              eventId: event.eventId,
              seatIds: event.seatIds,
              expiresAt: event.expiresAt
            }
          )
        });

        return this.steps[this.steps.length - 1].command;
      })
    );
  };

  @Saga()
  paymentProcessed = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType(PaymentProcessedEvent),
      map((event: PaymentProcessedEvent) => {
        console.log(`💳 Booking Saga: Payment processed for booking ${event.aggregateId}`);
        
        // Step 3: Confirm booking
        this.addStep({
          stepId: 'confirm-booking',
          command: new ConfirmBookingCommand(event.aggregateId),
          compensationCommand: new CancelBookingCommand(
            event.aggregateId,
            'Booking confirmation failed'
          ),
          maxRetries: 3
        });

        return this.steps[this.steps.length - 1].command;
      })
    );
  };

  @Saga()
  bookingConfirmed = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType(BookingConfirmedEvent),
      map((event: BookingConfirmedEvent) => {
        console.log(`✅ Booking Saga: Booking confirmed ${event.aggregateId}`);
        
        // Step 4: Send confirmation notification
        this.addStep({
          stepId: 'send-confirmation-notification',
          command: new SendNotificationCommand(
            event.aggregateId,
            NotificationType.EMAIL,
            'Booking Confirmed - Your Tickets Are Ready!',
            'Your booking has been confirmed. Your tickets are attached.',
            {
              bookingId: event.aggregateId,
              confirmedAt: event.confirmedAt
            }
          )
        });

        // Step 5: Generate and send tickets
        this.addStep({
          stepId: 'generate-tickets',
          command: new SendNotificationCommand(
            event.aggregateId,
            NotificationType.EMAIL,
            'Your Digital Tickets',
            'Please find your digital tickets attached.',
            {
              bookingId: event.aggregateId,
              generateTickets: true
            }
          )
        });

        console.log(`🎉 Booking Saga: Completed successfully for ${event.aggregateId}`);
        return this.steps[this.steps.length - 2].command; // Return first notification command
      })
    );
  };

  @Saga()
  bookingCancelled = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType(BookingCancelledEvent),
      map((event: BookingCancelledEvent) => {
        console.log(`❌ Booking Saga: Booking cancelled ${event.aggregateId}`);
        
        // Send cancellation notification
        return new SendNotificationCommand(
          event.aggregateId,
          NotificationType.EMAIL,
          'Booking Cancelled',
          `Your booking has been cancelled. ${event.reason || ''}`,
          {
            bookingId: event.aggregateId,
            reason: event.reason,
            cancelledAt: event.cancelledAt
          }
        );
      })
    );
  };

  @Saga()
  bookingExpired = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType(BookingExpiredEvent),
      delay(1000), // Small delay to ensure proper ordering
      map((event: BookingExpiredEvent) => {
        console.log(`⏰ Booking Saga: Booking expired ${event.aggregateId}`);
        
        // Send expiration notification
        return new SendNotificationCommand(
          event.aggregateId,
          NotificationType.EMAIL,
          'Booking Expired',
          'Your seat reservation has expired. Please try booking again.',
          {
            bookingId: event.aggregateId,
            expiredAt: event.expiredAt
          }
        );
      })
    );
  };

  // Override handle method to provide custom saga orchestration
  handle(event: any): Observable<ICommand> {
    switch (event.constructor.name) {
      case BookingInitiatedEvent.name:
        return this.bookingInitiated(new Observable(observer => observer.next(event)));
      case SeatsReservedEvent.name:
        return this.seatsReserved(new Observable(observer => observer.next(event)));
      case PaymentProcessedEvent.name:
        return this.paymentProcessed(new Observable(observer => observer.next(event)));
      case BookingConfirmedEvent.name:
        return this.bookingConfirmed(new Observable(observer => observer.next(event)));
      case BookingCancelledEvent.name:
        return this.bookingCancelled(new Observable(observer => observer.next(event)));
      case BookingExpiredEvent.name:
        return this.bookingExpired(new Observable(observer => observer.next(event)));
      default:
        return new Observable(observer => observer.complete());
    }
  }
}
