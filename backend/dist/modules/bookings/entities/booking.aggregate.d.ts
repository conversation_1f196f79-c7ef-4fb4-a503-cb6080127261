import { AggregateRoot } from '../../../common/entities/aggregate-root';
import { BaseEvent } from '../../../common/events/base.event';
export declare enum BookingStatus {
    INITIATED = "INITIATED",
    SEATS_RESERVED = "SEATS_RESERVED",
    PAYMENT_PENDING = "PAYMENT_PENDING",
    PAYMENT_PROCESSING = "PAYMENT_PROCESSING",
    CONFIRMED = "CONFIRMED",
    CANCELLED = "CANCELLED",
    EXPIRED = "EXPIRED",
    REFUNDED = "REFUNDED"
}
export interface BookingData {
    eventId: string;
    customerId: string;
    seatIds: string[];
    totalAmount: number;
    currency: string;
    customerEmail: string;
    customerPhone?: string;
}
export interface PaymentData {
    paymentId: string;
    paymentMethod: string;
    amount: number;
    currency: string;
    transactionId?: string;
}
export declare class BookingAggregate extends AggregateRoot {
    private _eventId;
    private _customerId;
    private _seatIds;
    private _totalAmount;
    private _currency;
    private _customerEmail;
    private _customerPhone?;
    private _status;
    private _paymentData?;
    private _reservationExpiresAt?;
    private _confirmedAt?;
    private _cancelledAt?;
    private _refundAmount?;
    constructor(id?: string);
    static initiate(bookingData: BookingData): BookingAggregate;
    reserveSeats(expirationMinutes?: number): void;
    processPayment(paymentData: PaymentData): void;
    confirm(): void;
    cancel(reason?: string): void;
    expire(): void;
    processRefund(refundAmount: number, refundId: string): void;
    protected applyEvent(event: BaseEvent): void;
    private onBookingInitiated;
    private onSeatsReserved;
    private onPaymentProcessed;
    private onBookingConfirmed;
    private onBookingCancelled;
    private onBookingExpired;
    private onRefundProcessed;
    get eventId(): string;
    get customerId(): string;
    get seatIds(): string[];
    get totalAmount(): number;
    get currency(): string;
    get customerEmail(): string;
    get customerPhone(): string | undefined;
    get status(): BookingStatus;
    get paymentData(): PaymentData | undefined;
    get reservationExpiresAt(): Date | undefined;
    get confirmedAt(): Date | undefined;
    get cancelledAt(): Date | undefined;
    get refundAmount(): number | undefined;
    get isExpired(): boolean;
}
