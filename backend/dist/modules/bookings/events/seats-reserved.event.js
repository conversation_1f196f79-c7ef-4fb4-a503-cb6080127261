"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeatsReservedEvent = void 0;
const base_event_1 = require("../../../common/events/base.event");
class SeatsReservedEvent extends base_event_1.DomainEvent {
    constructor(aggregateId, eventId, seatIds, expiresAt, version = 1) {
        super(aggregateId, version);
        this.aggregateId = aggregateId;
        this.eventId = eventId;
        this.seatIds = seatIds;
        this.expiresAt = expiresAt;
    }
}
exports.SeatsReservedEvent = SeatsReservedEvent;
//# sourceMappingURL=seats-reserved.event.js.map