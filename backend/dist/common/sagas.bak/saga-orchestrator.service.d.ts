import { OnModuleInit } from '@nestjs/common';
import { EventBus, CommandBus } from '@nestjs/cqrs';
import { SagaStatus } from './base.saga';
import { TicketBookingSaga } from '../../modules/bookings/sagas/ticket-booking.saga';
import { PaymentProcessingSaga } from '../../modules/payments/sagas/payment-processing.saga';
import { EventManagementSaga } from '../../modules/events/sagas/event-management.saga';
import { NotificationOrchestrationSaga } from '../../modules/notifications/sagas/notification-orchestration.saga';
import { KafkaConfig } from '../../config/kafka.config';
import { RedisConfig } from '../../config/redis.config';
export interface SagaInstance {
    id: string;
    type: string;
    status: SagaStatus;
    startedAt: Date;
    completedAt?: Date;
    progress: {
        completed: number;
        total: number;
        percentage: number;
    };
    metadata?: any;
}
export declare class SagaOrchestratorService implements OnModuleInit {
    private eventBus;
    private commandBus;
    private kafkaConfig;
    private redisConfig;
    private ticketBookingSaga;
    private paymentProcessingSaga;
    private eventManagementSaga;
    private notificationOrchestrationSaga;
    private activeSagas;
    private sagaSubscriptions;
    private sagaInstances;
    constructor(eventBus: EventBus, commandBus: CommandBus, kafkaConfig: KafkaConfig, redisConfig: RedisConfig, ticketBookingSaga: TicketBookingSaga, paymentProcessingSaga: PaymentProcessingSaga, eventManagementSaga: EventManagementSaga, notificationOrchestrationSaga: NotificationOrchestrationSaga);
    onModuleInit(): Promise<void>;
    startSaga(sagaType: string, event: any, metadata?: any): Promise<string>;
    getSagaStatus(sagaId: string): SagaInstance | null;
    getActiveSagas(): SagaInstance[];
    cancelSaga(sagaId: string, reason?: string): Promise<void>;
    retrySaga(sagaId: string): Promise<void>;
    private handleSagaCompletion;
    private handleSagaFailure;
    private persistSagaState;
    private restoreSagaState;
    private initializeSagaMonitoring;
    private recoverInProgressSagas;
    private performHealthCheck;
    private checkSagaTimeouts;
    private generateSagaId;
}
