{"version": 3, "file": "database.config.js", "sourceRoot": "", "sources": ["../../src/config/database.config.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,2CAA+C;AAE/C,qCAAwD;AAGjD,IAAM,cAAc,GAApB,MAAM,cAAc;IACzB,YAAoB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;IAAG,CAAC;IAEpD,oBAAoB;QAClB,OAAO;YACL,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,WAAW,CAAC;YACpD,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC;YAC7C,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC;YAC3D,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC;YAC3D,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,gBAAgB,CAAC;YAC7D,QAAQ,EAAE,CAAC,SAAS,GAAG,0BAA0B,CAAC;YAClD,UAAU,EAAE,CAAC,SAAS,GAAG,2BAA2B,CAAC;YACrD,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,aAAa;YACjE,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,aAAa;YAC7D,GAAG,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,YAAY,CAAC,CAAC,CAAC,EAAE,kBAAkB,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK;SACjG,CAAC;IACJ,CAAC;CACF,CAAA;AAlBY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;qCAEwB,sBAAa;GADrC,cAAc,CAkB1B;AAGD,MAAM,iBAAiB,GAAsB;IAC3C,IAAI,EAAE,UAAU;IAChB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,WAAW;IACxC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI;IAC3C,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,UAAU;IAC/C,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,UAAU;IAC/C,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,gBAAgB;IACjD,QAAQ,EAAE,CAAC,SAAS,GAAG,0BAA0B,CAAC;IAClD,UAAU,EAAE,CAAC,SAAS,GAAG,2BAA2B,CAAC;IACrD,WAAW,EAAE,KAAK;CACnB,CAAC;AAEF,kBAAe,IAAI,oBAAU,CAAC,iBAAiB,CAAC,CAAC"}