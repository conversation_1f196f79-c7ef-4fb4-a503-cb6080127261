"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InitiateBookingCommand = void 0;
const base_command_1 = require("../../../common/commands/base.command");
class InitiateBookingCommand extends base_command_1.Command {
    constructor(bookingData) {
        super();
        this.bookingData = bookingData;
    }
}
exports.InitiateBookingCommand = InitiateBookingCommand;
//# sourceMappingURL=initiate-booking.command.js.map