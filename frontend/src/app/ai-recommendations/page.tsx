'use client'

import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { SparklesIcon, UserIcon, HeartIcon } from '@heroicons/react/24/outline'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { EventGrid } from '@/components/events/event-grid'
import { aiAPI } from '@/lib/api'
import { useAuthStore } from '@/lib/store'
import toast from 'react-hot-toast'

export default function AIRecommendationsPage() {
  const { user, isAuthenticated } = useAuthStore()
  const [recommendations, setRecommendations] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [personalizedEvents, setPersonalizedEvents] = useState([])

  useEffect(() => {
    if (isAuthenticated && user) {
      fetchRecommendations()
    }
  }, [isAuthenticated, user])

  const fetchRecommendations = async () => {
    if (!user) return
    
    setIsLoading(true)
    try {
      const response = await aiAPI.getRecommendations(user.id, 10)
      setRecommendations(response.data)
      
      // Mark events as recommended for display
      const eventsWithRecommendation = response.data.map((event: any) => ({
        ...event,
        isRecommended: true
      }))
      setPersonalizedEvents(eventsWithRecommendation)
    } catch (error: any) {
      console.error('Failed to fetch recommendations:', error)
      toast.error('AI recommendations are currently unavailable')
      // Show some sample recommended events
      setPersonalizedEvents([
        {
          id: '1',
          title: 'Jazz Night at Blue Note',
          description: 'Smooth jazz evening with world-class musicians',
          category: 'music',
          venue: 'Blue Note NYC',
          date: '2024-12-15T20:00:00Z',
          price: 85,
          totalSeats: 200,
          availableSeats: 150,
          isRecommended: true
        },
        {
          id: '2',
          title: 'Comedy Central Live',
          description: 'Stand-up comedy featuring rising stars',
          category: 'comedy',
          venue: 'Comedy Cellar',
          date: '2024-12-20T21:00:00Z',
          price: 45,
          totalSeats: 150,
          availableSeats: 120,
          isRecommended: true
        }
      ])
    } finally {
      setIsLoading(false)
    }
  }

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <SparklesIcon className="h-16 w-16 mx-auto text-primary mb-6" />
            <h1 className="text-3xl font-bold mb-4">AI-Powered Recommendations</h1>
            <p className="text-muted-foreground mb-8">
              Sign in to get personalized event recommendations based on your preferences and behavior.
            </p>
            <Button size="lg" asChild>
              <a href="/login">Sign In to Get Recommendations</a>
            </Button>
          </motion.div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <div className="flex items-center gap-3 mb-4">
          <SparklesIcon className="h-8 w-8 text-primary" />
          <h1 className="text-3xl lg:text-4xl font-bold">AI Recommendations</h1>
        </div>
        <p className="text-muted-foreground text-lg">
          Personalized event suggestions powered by artificial intelligence
        </p>
      </motion.div>

      {/* User Profile Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="mb-8"
      >
        <Card className="bg-gradient-to-r from-primary/10 to-purple-500/10">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserIcon className="h-5 w-5" />
              Welcome back, {user?.firstName}!
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">
                  {personalizedEvents.length}
                </div>
                <div className="text-sm text-muted-foreground">
                  Recommended Events
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">
                  <HeartIcon className="h-6 w-6 mx-auto" />
                </div>
                <div className="text-sm text-muted-foreground">
                  Based on Your Preferences
                </div>
              </div>
              <div className="text-center">
                <Button 
                  variant="outline" 
                  onClick={fetchRecommendations}
                  disabled={isLoading}
                >
                  {isLoading ? 'Refreshing...' : 'Refresh Recommendations'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* AI Features */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="mb-8"
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">🎯 Smart Matching</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Our AI analyzes your past bookings and preferences to find events you'll love.
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">🔍 Semantic Search</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Search using natural language and find events that match your intent.
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">📊 Trend Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Discover trending events and popular choices in your area.
              </p>
            </CardContent>
          </Card>
        </div>
      </motion.div>

      {/* Recommended Events */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <div className="mb-6">
          <h2 className="text-2xl font-bold mb-2">Recommended for You</h2>
          <p className="text-muted-foreground">
            Events selected specifically based on your interests and behavior
          </p>
        </div>

        <EventGrid events={personalizedEvents} isLoading={isLoading} />

        {!isLoading && personalizedEvents.length === 0 && (
          <div className="text-center py-12">
            <SparklesIcon className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No recommendations yet</h3>
            <p className="text-muted-foreground mb-4">
              Browse some events to help our AI learn your preferences!
            </p>
            <Button asChild>
              <a href="/events">Browse Events</a>
            </Button>
          </div>
        )}
      </motion.div>
    </div>
  )
}
