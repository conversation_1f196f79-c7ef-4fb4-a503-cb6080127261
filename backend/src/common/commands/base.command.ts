import { ICommand } from '@nestjs/cqrs';

export abstract class BaseCommand implements ICommand {
  public readonly commandId: string;
  public readonly timestamp: Date;

  constructor() {
    this.commandId = this.generateCommandId();
    this.timestamp = new Date();
  }

  private generateCommandId(): string {
    return `cmd-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

export abstract class Command extends BaseCommand {
  constructor() {
    super();
  }
}
