import { Injectable } from '@nestjs/common';
import { Saga, ICommand, ofType } from '@nestjs/cqrs';
import { Observable } from 'rxjs';
import { map, delay } from 'rxjs/operators';

import { BaseSaga } from '../../../common/sagas/base.saga';
import { EventCreatedEvent } from '../events/event-created.event';
import { EventPublishedEvent } from '../events/event-published.event';
import { EventCancelledEvent } from '../events/event-cancelled.event';
import { SeatReservedEvent } from '../events/seat-reserved.event';

import { SendNotificationCommand, NotificationType } from '../../bookings/commands/send-notification.command';

// External integration commands
export class SyncToExternalAPICommand {
  constructor(
    public readonly eventId: string,
    public readonly apiProvider: string,
    public readonly action: 'create' | 'update' | 'delete'
  ) {}
}

export class UpdateSearchIndexCommand {
  constructor(
    public readonly eventId: string,
    public readonly action: 'index' | 'update' | 'delete'
  ) {}
}

export class GenerateEventReportCommand {
  constructor(
    public readonly eventId: string,
    public readonly reportType: 'sales' | 'attendance' | 'analytics'
  ) {}
}

@Injectable()
export class EventManagementSaga extends BaseSaga {

  @Saga()
  eventCreated = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType(EventCreatedEvent),
      map((event: EventCreatedEvent) => {
        console.log(`🎪 Event Saga: Event created ${event.aggregateId}`);
        
        // Step 1: Sync to external APIs (Ticketmaster, Eventbrite)
        this.addStep({
          stepId: 'sync-to-external-apis',
          command: new SyncToExternalAPICommand(
            event.aggregateId,
            'ticketmaster',
            'create'
          ),
          maxRetries: 3
        });

        // Step 2: Update search index for discoverability
        this.addStep({
          stepId: 'update-search-index',
          command: new UpdateSearchIndexCommand(
            event.aggregateId,
            'index'
          ),
          maxRetries: 2
        });

        // Step 3: Notify event organizer
        this.addStep({
          stepId: 'notify-organizer-creation',
          command: new SendNotificationCommand(
            event.eventData.organizerId || 'system',
            NotificationType.EMAIL,
            'Event Created Successfully',
            'Your event has been created and is ready for review.',
            {
              eventId: event.aggregateId,
              eventTitle: event.eventData.title,
              eventDate: event.eventData.startDate
            }
          )
        });

        this.startSaga();
        return this.steps[0].command;
      })
    );
  };

  @Saga()
  eventPublished = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType(EventPublishedEvent),
      map((event: EventPublishedEvent) => {
        console.log(`📢 Event Saga: Event published ${event.aggregateId}`);
        
        // Step 1: Update external APIs with published status
        this.addStep({
          stepId: 'sync-published-status',
          command: new SyncToExternalAPICommand(
            event.aggregateId,
            'all',
            'update'
          ),
          maxRetries: 3
        });

        // Step 2: Update search index with published status
        this.addStep({
          stepId: 'update-search-published',
          command: new UpdateSearchIndexCommand(
            event.aggregateId,
            'update'
          ),
          maxRetries: 2
        });

        // Step 3: Send marketing notifications
        this.addStep({
          stepId: 'send-marketing-notifications',
          command: new SendNotificationCommand(
            'marketing-team',
            NotificationType.EMAIL,
            'New Event Published',
            'A new event is now available for booking.',
            {
              eventId: event.aggregateId,
              publishedAt: event.occurredOn
            }
          )
        });

        // Step 4: Notify subscribers/followers
        this.addStep({
          stepId: 'notify-subscribers',
          command: new SendNotificationCommand(
            'subscribers',
            NotificationType.PUSH,
            'New Event Available',
            'A new event matching your interests is now available!',
            {
              eventId: event.aggregateId
            }
          )
        });

        return this.steps[0].command;
      })
    );
  };

  @Saga()
  eventCancelled = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType(EventCancelledEvent),
      map((event: EventCancelledEvent) => {
        console.log(`❌ Event Saga: Event cancelled ${event.aggregateId}`);
        
        // Step 1: Remove from external APIs
        this.addStep({
          stepId: 'remove-from-external-apis',
          command: new SyncToExternalAPICommand(
            event.aggregateId,
            'all',
            'delete'
          ),
          maxRetries: 3
        });

        // Step 2: Remove from search index
        this.addStep({
          stepId: 'remove-from-search',
          command: new UpdateSearchIndexCommand(
            event.aggregateId,
            'delete'
          ),
          maxRetries: 2
        });

        // Step 3: Notify all ticket holders
        this.addStep({
          stepId: 'notify-ticket-holders',
          command: new SendNotificationCommand(
            'all-ticket-holders',
            NotificationType.EMAIL,
            'Event Cancelled - Refund Information',
            'We regret to inform you that this event has been cancelled. Refunds will be processed automatically.',
            {
              eventId: event.aggregateId,
              cancelledAt: event.occurredOn
            }
          )
        });

        // Step 4: Generate cancellation report
        this.addStep({
          stepId: 'generate-cancellation-report',
          command: new GenerateEventReportCommand(
            event.aggregateId,
            'sales'
          )
        });

        return this.steps[0].command;
      })
    );
  };

  @Saga()
  seatReserved = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType(SeatReservedEvent),
      delay(500), // Small delay to batch seat reservations
      map((event: SeatReservedEvent) => {
        console.log(`🪑 Event Saga: Seats reserved for event ${event.aggregateId}`);
        
        // Update real-time availability in external systems
        return new SyncToExternalAPICommand(
          event.aggregateId,
          'all',
          'update'
        );
      })
    );
  };

  // Event completion workflow (triggered by scheduled job)
  @Saga()
  eventCompleted = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType('EventCompletedEvent'), // This would be triggered by a scheduled job
      map((event: any) => {
        console.log(`🏁 Event Saga: Event completed ${event.eventId}`);
        
        // Step 1: Generate final attendance report
        this.addStep({
          stepId: 'generate-attendance-report',
          command: new GenerateEventReportCommand(
            event.eventId,
            'attendance'
          )
        });

        // Step 2: Generate analytics report
        this.addStep({
          stepId: 'generate-analytics-report',
          command: new GenerateEventReportCommand(
            event.eventId,
            'analytics'
          )
        });

        // Step 3: Send feedback request to attendees
        this.addStep({
          stepId: 'send-feedback-request',
          command: new SendNotificationCommand(
            'attendees',
            NotificationType.EMAIL,
            'How was your experience?',
            'We would love to hear your feedback about the event.',
            {
              eventId: event.eventId,
              feedbackUrl: `https://feedback.example.com/${event.eventId}`
            }
          )
        });

        // Step 4: Archive event data
        this.addStep({
          stepId: 'archive-event-data',
          command: new SyncToExternalAPICommand(
            event.eventId,
            'archive',
            'create'
          )
        });

        return this.steps[0].command;
      })
    );
  };

  // Override handle method for custom orchestration
  handle(event: any): Observable<ICommand> {
    switch (event.constructor.name) {
      case EventCreatedEvent.name:
        return this.eventCreated(new Observable(observer => observer.next(event)));
      case EventPublishedEvent.name:
        return this.eventPublished(new Observable(observer => observer.next(event)));
      case EventCancelledEvent.name:
        return this.eventCancelled(new Observable(observer => observer.next(event)));
      case SeatReservedEvent.name:
        return this.seatReserved(new Observable(observer => observer.next(event)));
      case 'EventCompletedEvent':
        return this.eventCompleted(new Observable(observer => observer.next(event)));
      default:
        return new Observable(observer => observer.complete());
    }
  }
}
