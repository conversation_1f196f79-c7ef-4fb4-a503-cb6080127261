import { DomainEvent } from '../../../common/events/base.event';
export declare class PaymentSucceededEvent extends DomainEvent {
    readonly aggregateId: string;
    readonly bookingId: string;
    readonly transactionId: string;
    readonly amount: number;
    readonly currency: string;
    constructor(aggregateId: string, bookingId: string, transactionId: string, amount: number, currency: string, version?: number);
}
