#!/bin/bash

# Complete Local Development Setup Script
# This script sets up everything needed to run the ticket booking system locally

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🎫 Ticket Booking System - Local Development Setup${NC}"
echo -e "${BLUE}=================================================${NC}"

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    echo -e "${RED}❌ Please run this script from the project root directory${NC}"
    exit 1
fi

# Step 1: Check prerequisites
echo -e "\n${BLUE}📋 Step 1: Checking prerequisites...${NC}"

# Check Docker
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed. Please install Docker first.${NC}"
    echo -e "${YELLOW}   Visit: https://docs.docker.com/get-docker/${NC}"
    exit 1
fi

# Check Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose is not installed. Please install Docker Compose first.${NC}"
    exit 1
fi

# Check Node.js
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js is not installed. Please install Node.js 18+ first.${NC}"
    echo -e "${YELLOW}   Visit: https://nodejs.org/${NC}"
    exit 1
fi

# Check npm
if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ npm is not installed. Please install npm first.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ All prerequisites are installed${NC}"

# Step 2: Setup environment files
echo -e "\n${BLUE}📝 Step 2: Setting up environment files...${NC}"

# Backend environment
if [ ! -f "backend/.env" ]; then
    echo -e "${YELLOW}Creating backend/.env from template...${NC}"
    cp backend/.env.example backend/.env
    echo -e "${GREEN}✅ Backend .env file created${NC}"
else
    echo -e "${GREEN}✅ Backend .env file already exists${NC}"
fi

# Frontend environment
if [ ! -f "frontend/.env.local" ]; then
    echo -e "${YELLOW}Creating frontend/.env.local...${NC}"
    cat > frontend/.env.local << 'EOF'
# Frontend Environment Variables
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_WS_URL=ws://localhost:3001
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
EOF
    echo -e "${GREEN}✅ Frontend .env.local file created${NC}"
else
    echo -e "${GREEN}✅ Frontend .env.local file already exists${NC}"
fi

# Step 3: Install dependencies
echo -e "\n${BLUE}📦 Step 3: Installing dependencies...${NC}"

echo -e "${YELLOW}Installing backend dependencies...${NC}"
cd backend
npm install
echo -e "${GREEN}✅ Backend dependencies installed${NC}"

echo -e "${YELLOW}Installing frontend dependencies...${NC}"
cd ../frontend
npm install
echo -e "${GREEN}✅ Frontend dependencies installed${NC}"

cd ..

# Step 4: Start infrastructure services
echo -e "\n${BLUE}🐳 Step 4: Starting infrastructure services...${NC}"

echo -e "${YELLOW}Starting PostgreSQL, Redis, and Kafka...${NC}"
docker-compose up -d postgres redis zookeeper kafka

echo -e "${YELLOW}Waiting for services to be ready...${NC}"
sleep 30

# Check if services are running
if docker-compose ps | grep -q "Up"; then
    echo -e "${GREEN}✅ Infrastructure services are running${NC}"
else
    echo -e "${RED}❌ Some services failed to start. Check docker-compose logs${NC}"
    docker-compose logs
    exit 1
fi

# Step 5: Setup database
echo -e "\n${BLUE}🗄️  Step 5: Setting up database...${NC}"

# Wait for PostgreSQL to be ready
echo -e "${YELLOW}Waiting for PostgreSQL to be ready...${NC}"
until docker exec ticket-booking-postgres pg_isready -U postgres; do
    echo -e "${YELLOW}Waiting for PostgreSQL...${NC}"
    sleep 2
done

echo -e "${GREEN}✅ PostgreSQL is ready${NC}"

# Create database tables (basic setup since we don't have TypeORM migrations yet)
echo -e "${YELLOW}Creating database schema...${NC}"
docker exec -i ticket-booking-postgres psql -U postgres -d ticket_booking << 'EOF'
-- Create basic tables for testing

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    role VARCHAR(50) DEFAULT 'USER',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Events table
CREATE TABLE IF NOT EXISTS events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    venue VARCHAR(255) NOT NULL,
    date TIMESTAMP NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    total_seats INTEGER NOT NULL,
    available_seats INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bookings table
CREATE TABLE IF NOT EXISTS bookings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    event_id UUID REFERENCES events(id),
    ticket_count INTEGER NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'PENDING',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample data
INSERT INTO events (title, description, category, venue, date, price, total_seats, available_seats) VALUES
('Rock Concert - The Rolling Stones', 'Legendary rock band performing their greatest hits', 'music', 'Madison Square Garden', '2024-12-31 20:00:00', 150.00, 1000, 1000),
('Jazz Festival', 'Annual jazz festival featuring world-class musicians', 'music', 'Blue Note', '2024-11-15 19:00:00', 75.00, 500, 500),
('Broadway Musical - Hamilton', 'Award-winning musical about Alexander Hamilton', 'theater', 'Richard Rodgers Theatre', '2024-10-20 20:00:00', 200.00, 800, 800),
('Comedy Show - Dave Chappelle', 'Stand-up comedy by the legendary comedian', 'comedy', 'Comedy Cellar', '2024-09-25 21:00:00', 100.00, 300, 300),
('NBA Game - Lakers vs Knicks', 'Exciting basketball game', 'sports', 'Madison Square Garden', '2024-11-10 19:30:00', 120.00, 2000, 2000)
ON CONFLICT DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_events_category ON events(category);
CREATE INDEX IF NOT EXISTS idx_events_date ON events(date);
CREATE INDEX IF NOT EXISTS idx_bookings_user_id ON bookings(user_id);
CREATE INDEX IF NOT EXISTS idx_bookings_event_id ON bookings(event_id);

EOF

echo -e "${GREEN}✅ Database schema and sample data created${NC}"

# Step 6: Test services
echo -e "\n${BLUE}🧪 Step 6: Testing services...${NC}"

# Test PostgreSQL
echo -e "${YELLOW}Testing PostgreSQL connection...${NC}"
if docker exec ticket-booking-postgres psql -U postgres -d ticket_booking -c "SELECT COUNT(*) FROM events;" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ PostgreSQL is working${NC}"
else
    echo -e "${RED}❌ PostgreSQL test failed${NC}"
fi

# Test Redis
echo -e "${YELLOW}Testing Redis connection...${NC}"
if docker exec ticket-booking-redis redis-cli ping | grep -q "PONG"; then
    echo -e "${GREEN}✅ Redis is working${NC}"
else
    echo -e "${RED}❌ Redis test failed${NC}"
fi

# Test Kafka
echo -e "${YELLOW}Testing Kafka connection...${NC}"
if docker exec ticket-booking-kafka kafka-topics --bootstrap-server localhost:9092 --list > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Kafka is working${NC}"
else
    echo -e "${RED}❌ Kafka test failed${NC}"
fi

# Step 7: Create startup scripts
echo -e "\n${BLUE}📜 Step 7: Creating startup scripts...${NC}"

# Create start-backend script
cat > scripts/start-backend.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting Backend Server..."
cd backend
npm run start:dev
EOF

# Create start-frontend script
cat > scripts/start-frontend.sh << 'EOF'
#!/bin/bash
echo "🌐 Starting Frontend Server..."
cd frontend
npm run dev
EOF

# Create stop-services script
cat > scripts/stop-services.sh << 'EOF'
#!/bin/bash
echo "🛑 Stopping all services..."
docker-compose down
echo "✅ All services stopped"
EOF

# Create restart-services script
cat > scripts/restart-services.sh << 'EOF'
#!/bin/bash
echo "🔄 Restarting infrastructure services..."
docker-compose down
docker-compose up -d postgres redis zookeeper kafka
echo "✅ Services restarted"
EOF

# Make scripts executable
chmod +x scripts/start-backend.sh
chmod +x scripts/start-frontend.sh
chmod +x scripts/stop-services.sh
chmod +x scripts/restart-services.sh

echo -e "${GREEN}✅ Startup scripts created${NC}"

# Step 8: Final instructions
echo -e "\n${GREEN}🎉 Setup completed successfully!${NC}"
echo -e "\n${BLUE}📋 What's been set up:${NC}"
echo -e "${GREEN}✅ PostgreSQL database with sample events${NC}"
echo -e "${GREEN}✅ Redis cache server${NC}"
echo -e "${GREEN}✅ Apache Kafka message broker${NC}"
echo -e "${GREEN}✅ Environment files configured${NC}"
echo -e "${GREEN}✅ Dependencies installed${NC}"
echo -e "${GREEN}✅ Helper scripts created${NC}"

echo -e "\n${BLUE}🚀 To start the application:${NC}"
echo -e "\n${YELLOW}Terminal 1 - Start Backend:${NC}"
echo -e "${GREEN}./scripts/start-backend.sh${NC}"
echo -e "${GREEN}# OR manually: cd backend && npm run start:dev${NC}"

echo -e "\n${YELLOW}Terminal 2 - Start Frontend:${NC}"
echo -e "${GREEN}./scripts/start-frontend.sh${NC}"
echo -e "${GREEN}# OR manually: cd frontend && npm run dev${NC}"

echo -e "\n${BLUE}🌐 Access URLs:${NC}"
echo -e "${GREEN}• Frontend: http://localhost:3000${NC}"
echo -e "${GREEN}• Backend API: http://localhost:3001${NC}"
echo -e "${GREEN}• API Docs: http://localhost:3001/api/docs${NC}"
echo -e "${GREEN}• Kafka UI: http://localhost:8080${NC}"
echo -e "${GREEN}• Redis Commander: http://localhost:8081${NC}"

echo -e "\n${BLUE}🧪 Test the setup:${NC}"
echo -e "${GREEN}curl http://localhost:3001/api/v1/health${NC}"
echo -e "${GREEN}curl http://localhost:3001/api/v1/events${NC}"

echo -e "\n${BLUE}🛑 To stop everything:${NC}"
echo -e "${GREEN}./scripts/stop-services.sh${NC}"

echo -e "\n${YELLOW}⚠️  Note: If you want to use AI features, you'll need to:${NC}"
echo -e "${YELLOW}1. Get Pinecone API key from https://www.pinecone.io/${NC}"
echo -e "${YELLOW}2. Get OpenAI API key from https://platform.openai.com/${NC}"
echo -e "${YELLOW}3. Update backend/.env with your API keys${NC}"
echo -e "${YELLOW}4. Run: ./scripts/setup-pinecone.sh${NC}"

echo -e "\n${GREEN}Happy coding! 🎫🚀${NC}"
