#!/bin/bash

# Test script to verify local setup is working
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Testing Local Development Setup${NC}"
echo -e "${BLUE}=================================${NC}"

# Test 1: Check if services are running
echo -e "\n${BLUE}📋 Test 1: Checking Docker services...${NC}"

if docker-compose ps | grep -q "Up"; then
    echo -e "${GREEN}✅ Docker services are running${NC}"
else
    echo -e "${RED}❌ Docker services are not running${NC}"
    echo -e "${YELLOW}Run: docker-compose up -d postgres redis zookeeper kafka${NC}"
    exit 1
fi

# Test 2: Check PostgreSQL
echo -e "\n${BLUE}🗄️  Test 2: Testing PostgreSQL...${NC}"

if docker exec ticket-booking-postgres pg_isready -U postgres > /dev/null 2>&1; then
    echo -e "${GREEN}✅ PostgreSQL is ready${NC}"
    
    # Check if sample data exists
    EVENT_COUNT=$(docker exec ticket-booking-postgres psql -U postgres -d ticket_booking -t -c "SELECT COUNT(*) FROM events;" 2>/dev/null | xargs)
    if [ "$EVENT_COUNT" -gt 0 ]; then
        echo -e "${GREEN}✅ Sample data exists ($EVENT_COUNT events)${NC}"
    else
        echo -e "${YELLOW}⚠️  No sample data found${NC}"
    fi
else
    echo -e "${RED}❌ PostgreSQL is not ready${NC}"
fi

# Test 3: Check Redis
echo -e "\n${BLUE}🔴 Test 3: Testing Redis...${NC}"

if docker exec ticket-booking-redis redis-cli ping | grep -q "PONG"; then
    echo -e "${GREEN}✅ Redis is working${NC}"
else
    echo -e "${RED}❌ Redis is not working${NC}"
fi

# Test 4: Check Kafka
echo -e "\n${BLUE}📨 Test 4: Testing Kafka...${NC}"

if docker exec ticket-booking-kafka kafka-topics --bootstrap-server localhost:9092 --list > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Kafka is working${NC}"
else
    echo -e "${RED}❌ Kafka is not working${NC}"
fi

# Test 5: Check if backend can start (if running)
echo -e "\n${BLUE}🚀 Test 5: Testing Backend API...${NC}"

if curl -s http://localhost:3001/api/v1/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Backend API is responding${NC}"
    
    # Test events endpoint
    if curl -s http://localhost:3001/api/v1/events > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Events API is working${NC}"
    else
        echo -e "${YELLOW}⚠️  Events API not responding (backend may not be fully started)${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  Backend API not responding (not started yet)${NC}"
    echo -e "${YELLOW}   Start with: cd backend && npm run start:dev${NC}"
fi

# Test 6: Check if frontend can start (if running)
echo -e "\n${BLUE}🌐 Test 6: Testing Frontend...${NC}"

if curl -s http://localhost:3000 > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Frontend is responding${NC}"
else
    echo -e "${YELLOW}⚠️  Frontend not responding (not started yet)${NC}"
    echo -e "${YELLOW}   Start with: cd frontend && npm run dev${NC}"
fi

# Test 7: Check environment files
echo -e "\n${BLUE}📝 Test 7: Checking environment files...${NC}"

if [ -f "backend/.env" ]; then
    echo -e "${GREEN}✅ Backend .env file exists${NC}"
else
    echo -e "${RED}❌ Backend .env file missing${NC}"
fi

if [ -f "frontend/.env.local" ]; then
    echo -e "${GREEN}✅ Frontend .env.local file exists${NC}"
else
    echo -e "${RED}❌ Frontend .env.local file missing${NC}"
fi

# Test 8: Check dependencies
echo -e "\n${BLUE}📦 Test 8: Checking dependencies...${NC}"

if [ -d "backend/node_modules" ]; then
    echo -e "${GREEN}✅ Backend dependencies installed${NC}"
else
    echo -e "${RED}❌ Backend dependencies not installed${NC}"
    echo -e "${YELLOW}   Run: cd backend && npm install${NC}"
fi

if [ -d "frontend/node_modules" ]; then
    echo -e "${GREEN}✅ Frontend dependencies installed${NC}"
else
    echo -e "${RED}❌ Frontend dependencies not installed${NC}"
    echo -e "${YELLOW}   Run: cd frontend && npm install${NC}"
fi

# Summary
echo -e "\n${BLUE}📊 Test Summary${NC}"
echo -e "${BLUE}===============${NC}"

# Count services
SERVICES_UP=$(docker-compose ps | grep "Up" | wc -l)
echo -e "${GREEN}✅ Docker services running: $SERVICES_UP/4${NC}"

# Check critical files
FILES_OK=0
[ -f "backend/.env" ] && ((FILES_OK++))
[ -f "frontend/.env.local" ] && ((FILES_OK++))
[ -d "backend/node_modules" ] && ((FILES_OK++))
[ -d "frontend/node_modules" ] && ((FILES_OK++))

echo -e "${GREEN}✅ Setup files ready: $FILES_OK/4${NC}"

# Final recommendations
echo -e "\n${BLUE}🎯 Next Steps:${NC}"

if [ "$SERVICES_UP" -eq 4 ] && [ "$FILES_OK" -eq 4 ]; then
    echo -e "${GREEN}🎉 Everything looks good! You can start the applications:${NC}"
    echo -e "\n${YELLOW}Terminal 1:${NC} ${GREEN}cd backend && npm run start:dev${NC}"
    echo -e "${YELLOW}Terminal 2:${NC} ${GREEN}cd frontend && npm run dev${NC}"
    echo -e "\n${YELLOW}Then visit:${NC}"
    echo -e "${GREEN}• Frontend: http://localhost:3000${NC}"
    echo -e "${GREEN}• Backend API: http://localhost:3001${NC}"
    echo -e "${GREEN}• API Docs: http://localhost:3001/api/docs${NC}"
else
    echo -e "${YELLOW}⚠️  Some issues found. Please run the setup script first:${NC}"
    echo -e "${GREEN}./scripts/setup-local-dev.sh${NC}"
fi

echo -e "\n${BLUE}🔧 Useful commands:${NC}"
echo -e "${GREEN}• Test API: curl http://localhost:3001/api/v1/events${NC}"
echo -e "${GREEN}• View logs: docker-compose logs [service-name]${NC}"
echo -e "${GREEN}• Restart services: docker-compose restart${NC}"
echo -e "${GREEN}• Stop all: docker-compose down${NC}"
