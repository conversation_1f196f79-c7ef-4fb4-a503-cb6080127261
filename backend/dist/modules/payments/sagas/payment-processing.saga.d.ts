import { ICommand } from '@nestjs/cqrs';
import { Observable } from 'rxjs';
import { BaseSaga } from '../../../common/sagas/base.saga';
export declare class PaymentProcessingSaga extends BaseSaga {
    paymentInitiated: (events$: Observable<any>) => Observable<ICommand>;
    paymentSucceeded: (events$: Observable<any>) => Observable<ICommand>;
    paymentFailed: (events$: Observable<any>) => Observable<ICommand>;
    refundInitiated: (events$: Observable<any>) => Observable<ICommand>;
    handle(event: any): Observable<ICommand>;
}
