# 🚀 Local Development Setup

## 🎯 **Quick Start (5 Minutes)**

### **1. One-Command Setup**
```bash
# Run the complete setup script
./scripts/setup-local-dev.sh
```

This script will:
- ✅ Check prerequisites (Docker, Node.js, npm)
- ✅ Create environment files
- ✅ Install all dependencies
- ✅ Start infrastructure services (PostgreSQL, Redis, Kafka)
- ✅ Create database schema and sample data
- ✅ Test all connections

### **2. Start the Applications**

**Terminal 1 - Backend:**
```bash
cd backend
npm run start:dev
```

**Terminal 2 - Frontend:**
```bash
cd frontend
npm run dev
```

### **3. Access the Applications**
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **API Documentation**: http://localhost:3001/api/docs

## 🧪 **Test Everything Works**

```bash
# Test the setup
./scripts/test-local-setup.sh

# Test the API
./scripts/test-api.sh

# Quick API test
curl http://localhost:3001/api/v1/events
```

## 📋 **Manual Setup (If Needed)**

### **Prerequisites**
- Docker & Docker Compose
- Node.js 18+
- npm

### **Step-by-Step Setup**

1. **Clone and Install Dependencies**
```bash
# Backend
cd backend
npm install

# Frontend
cd ../frontend
npm install
cd ..
```

2. **Environment Files**
```bash
# Backend environment
cp backend/.env.example backend/.env

# Frontend environment
cat > frontend/.env.local << 'EOF'
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_WS_URL=ws://localhost:3001
EOF
```

3. **Start Infrastructure**
```bash
# Start PostgreSQL, Redis, Kafka
docker-compose up -d postgres redis zookeeper kafka

# Wait for services to be ready
sleep 30
```

4. **Setup Database**
```bash
# Create tables and sample data
docker exec -i ticket-booking-postgres psql -U postgres -d ticket_booking << 'EOF'
-- Users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    role VARCHAR(50) DEFAULT 'USER',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Events table
CREATE TABLE IF NOT EXISTS events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    venue VARCHAR(255) NOT NULL,
    date TIMESTAMP NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    total_seats INTEGER NOT NULL,
    available_seats INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Sample events
INSERT INTO events (title, description, category, venue, date, price, total_seats, available_seats) VALUES
('Rock Concert', 'Amazing rock concert', 'music', 'Madison Square Garden', '2024-12-31 20:00:00', 150.00, 1000, 1000),
('Jazz Festival', 'Annual jazz festival', 'music', 'Blue Note', '2024-11-15 19:00:00', 75.00, 500, 500)
ON CONFLICT DO NOTHING;
EOF
```

## 🔧 **Available Scripts**

### **Setup Scripts**
```bash
./scripts/setup-local-dev.sh     # Complete setup
./scripts/setup-pinecone.sh      # AI features setup (optional)
```

### **Testing Scripts**
```bash
./scripts/test-local-setup.sh    # Test infrastructure
./scripts/test-api.sh            # Test API endpoints
```

### **Utility Scripts**
```bash
./scripts/start-backend.sh       # Start backend
./scripts/start-frontend.sh      # Start frontend
./scripts/stop-services.sh       # Stop all services
./scripts/restart-services.sh    # Restart infrastructure
```

## 🐳 **Docker Services**

### **Running Services**
```bash
# Check status
docker-compose ps

# View logs
docker-compose logs postgres
docker-compose logs redis
docker-compose logs kafka

# Restart a service
docker-compose restart postgres
```

### **Service Details**
- **PostgreSQL**: localhost:5432 (user: postgres, db: ticket_booking)
- **Redis**: localhost:6379
- **Kafka**: localhost:9092
- **Zookeeper**: localhost:2181

## 🧪 **Testing the API**

### **Health Checks**
```bash
# General health
curl http://localhost:3001/api/v1/health

# Database health
curl http://localhost:3001/api/v1/health/db

# Redis health
curl http://localhost:3001/api/v1/health/redis
```

### **Events API**
```bash
# Get all events
curl http://localhost:3001/api/v1/events

# Search events
curl "http://localhost:3001/api/v1/events/search?query=concert"

# Create event (requires authentication)
curl -X POST http://localhost:3001/api/v1/events \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "title": "Test Event",
    "description": "A test event",
    "category": "music",
    "venue": "Test Venue",
    "date": "2024-12-31T20:00:00Z",
    "price": 50,
    "totalSeats": 100
  }'
```

### **Authentication**
```bash
# Register user
curl -X POST http://localhost:3001/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Test",
    "lastName": "User"
  }'

# Login
curl -X POST http://localhost:3001/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

## 🤖 **AI Features (Optional)**

### **Setup Pinecone**
1. Get API keys:
   - Pinecone: https://www.pinecone.io/
   - OpenAI: https://platform.openai.com/

2. Update `backend/.env`:
```bash
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=us-west1-gcp
OPENAI_API_KEY=your-openai-api-key
AI_RECOMMENDATIONS_ENABLED=true
```

3. Setup Pinecone indexes:
```bash
./scripts/setup-pinecone.sh
```

### **Test AI Features**
```bash
# Get JWT token first (from login response)
TOKEN="your-jwt-token"

# Semantic search
curl -X POST http://localhost:3001/api/v1/ai/search/events \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"query": "rock music concert", "topK": 5}'

# Get recommendations
curl -X POST http://localhost:3001/api/v1/ai/recommendations \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"userId": "user-id", "limit": 10}'
```

## 🛠️ **Development Workflow**

### **Making Changes**
1. **Backend changes**: Auto-reload with `npm run start:dev`
2. **Frontend changes**: Auto-reload with `npm run dev`
3. **Database changes**: Update schema in setup script
4. **Environment changes**: Restart applications

### **Adding New Features**
1. Create feature branch
2. Add backend API endpoints
3. Add frontend components
4. Test with scripts
5. Update documentation

## 🚨 **Troubleshooting**

### **Common Issues**

**Services won't start:**
```bash
# Check Docker
docker --version
docker-compose --version

# Restart Docker
# On macOS: Restart Docker Desktop
# On Linux: sudo systemctl restart docker
```

**Database connection errors:**
```bash
# Check PostgreSQL
docker exec ticket-booking-postgres pg_isready -U postgres

# Reset database
docker-compose down -v
docker-compose up -d postgres
./scripts/setup-local-dev.sh
```

**Port conflicts:**
```bash
# Check what's using ports
lsof -i :3000  # Frontend
lsof -i :3001  # Backend
lsof -i :5432  # PostgreSQL

# Kill processes if needed
kill -9 PID
```

**Dependencies issues:**
```bash
# Clean install
rm -rf backend/node_modules frontend/node_modules
cd backend && npm install
cd ../frontend && npm install
```

### **Reset Everything**
```bash
# Nuclear option - reset everything
docker-compose down -v
rm -rf backend/node_modules frontend/node_modules
rm backend/.env frontend/.env.local

# Then run setup again
./scripts/setup-local-dev.sh
```

## 📊 **Performance Tips**

### **Development Optimization**
- Use `npm run start:dev` for hot reload
- Keep Docker services running between sessions
- Use `docker-compose restart` instead of `down/up`

### **Resource Usage**
- PostgreSQL: ~100MB RAM
- Redis: ~50MB RAM
- Kafka: ~200MB RAM
- Backend: ~100MB RAM
- Frontend: ~50MB RAM

## 🎯 **Next Steps**

1. **Explore the API**: Visit http://localhost:3001/api/docs
2. **Test the Frontend**: Visit http://localhost:3000
3. **Add AI Features**: Setup Pinecone for intelligent search
4. **Deploy to Cloud**: Use AWS/Kubernetes deployment scripts
5. **Add More Features**: Extend the booking system

## 📞 **Getting Help**

- **API Issues**: Check `./scripts/test-api.sh` output
- **Setup Issues**: Run `./scripts/test-local-setup.sh`
- **Docker Issues**: Check `docker-compose logs`
- **Database Issues**: Check PostgreSQL logs

Happy coding! 🎫🚀
