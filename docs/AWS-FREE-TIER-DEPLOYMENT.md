# AWS Free Tier Deployment Guide

## 🎯 Overview

This guide helps you deploy the Online Ticket Booking System to AWS while staying within **Free Tier limits**. The architecture is optimized to minimize costs while maintaining core functionality.

## 💰 Free Tier Optimizations

### ✅ What's Included (FREE)
- **ECS Fargate**: 750 hours/month (covers our containers)
- **RDS PostgreSQL**: 750 hours/month of db.t3.micro + 20GB storage
- **ElastiCache Redis**: 750 hours/month of cache.t2.micro
- **Application Load Balancer**: 750 hours/month + 15 LCU hours
- **VPC & Subnets**: Always free
- **CloudWatch**: 10 custom metrics, 5GB log ingestion
- **ECR**: 500MB storage/month
- **S3**: 5GB storage, 20K GET, 2K PUT requests

### ⚠️ What Costs Money
- **NAT Gateway**: ~$45/month (biggest cost!)
- **Data Transfer**: After 100GB/month
- **Additional storage**: Beyond free tier limits

## 🚀 Quick Deployment

### Prerequisites
```bash
# Install required tools
brew install awscli terraform docker  # macOS
# or
sudo apt-get install awscli terraform docker  # Ubuntu

# Configure AWS credentials
aws configure
```

### One-Command Deployment
```bash
# Make script executable and run
chmod +x scripts/deploy-free-tier.sh
./scripts/deploy-free-tier.sh
```

## 📋 Manual Deployment Steps

### 1. Setup AWS Infrastructure

```bash
cd infrastructure/terraform

# Initialize Terraform
terraform init

# Plan deployment (review costs)
terraform plan -var-file=environments/free-tier.tfvars

# Deploy infrastructure
terraform apply -var-file=environments/free-tier.tfvars
```

### 2. Build and Push Docker Images

```bash
# Get AWS account ID
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)

# Login to ECR
aws ecr get-login-password --region us-east-1 | \
docker login --username AWS --password-stdin \
${AWS_ACCOUNT_ID}.dkr.ecr.us-east-1.amazonaws.com

# Build and push backend
cd backend
docker build -t ticket-booking-backend:latest --target production .
docker tag ticket-booking-backend:latest \
${AWS_ACCOUNT_ID}.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-backend:latest
docker push ${AWS_ACCOUNT_ID}.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-backend:latest

# Build and push frontend
cd ../frontend
docker build -t ticket-booking-frontend:latest --target production .
docker tag ticket-booking-frontend:latest \
${AWS_ACCOUNT_ID}.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-frontend:latest
docker push ${AWS_ACCOUNT_ID}.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-frontend:latest
```

### 3. Update Environment Variables

```bash
# Update the tfvars file with your ECR URLs
sed -i "s/your-account/${AWS_ACCOUNT_ID}/g" environments/free-tier.tfvars
```

### 4. Deploy Services

```bash
# Apply Terraform configuration
terraform apply -var-file=environments/free-tier.tfvars
```

## 🏗️ Architecture (Free Tier Optimized)

```
┌─────────────────┐    ┌──────────────────┐
│   Internet      │    │   Application    │
│   Gateway       │────│   Load Balancer  │
└─────────────────┘    └──────────────────┘
                                │
                    ┌───────────┼───────────┐
                    │                       │
            ┌───────▼────────┐    ┌────────▼────────┐
            │   Frontend     │    │    Backend      │
            │   (ECS)        │    │    (ECS)        │
            │   256 CPU      │    │    256 CPU      │
            │   512 MB       │    │    512 MB       │
            └────────────────┘    └─────────────────┘
                                           │
                    ┌──────────────────────┼──────────────────────┐
                    │                      │                      │
            ┌───────▼────────┐    ┌────────▼────────┐    ┌───────▼────────┐
            │   PostgreSQL   │    │     Redis       │    │     Kafka      │
            │   (RDS)        │    │  (ElastiCache)   │    │  (Container)   │
            │   db.t3.micro  │    │  cache.t2.micro │    │   256 CPU      │
            └────────────────┘    └─────────────────┘    └────────────────┘
```

## 💡 Cost Optimization Tips

### 1. Remove NAT Gateway (Development Only)
```hcl
# In modules/vpc/main.tf, comment out NAT Gateway
# This saves ~$45/month but removes internet access for private subnets
```

### 2. Use Spot Instances (Advanced)
```hcl
# In ECS service, add capacity provider strategy
capacity_provider_strategy {
  capacity_provider = "FARGATE_SPOT"
  weight           = 100
}
```

### 3. Schedule Resources (Development)
```bash
# Stop services during non-working hours
aws ecs update-service --cluster ticket-booking-dev-cluster \
  --service backend --desired-count 0

# Start services when needed
aws ecs update-service --cluster ticket-booking-dev-cluster \
  --service backend --desired-count 1
```

## 📊 Monitoring Costs

### Set Up Billing Alerts
```bash
# Create billing alarm
aws cloudwatch put-metric-alarm \
  --alarm-name "BillingAlarm" \
  --alarm-description "Billing alarm" \
  --metric-name EstimatedCharges \
  --namespace AWS/Billing \
  --statistic Maximum \
  --period 86400 \
  --threshold 10.0 \
  --comparison-operator GreaterThanThreshold \
  --dimensions Name=Currency,Value=USD
```

### Daily Cost Check
```bash
# Check current month costs
aws ce get-cost-and-usage \
  --time-period Start=2024-01-01,End=2024-01-31 \
  --granularity MONTHLY \
  --metrics BlendedCost
```

## 🔧 Configuration Files

### Free Tier Variables (`environments/free-tier.tfvars`)
```hcl
# Optimized for AWS Free Tier
aws_region = "us-east-1"
environment = "dev"

# Single instance of each service
backend_desired_count = 1
frontend_desired_count = 1

# Minimal resource allocation
backend_cpu = 256
backend_memory = 512
frontend_cpu = 256
frontend_memory = 512

# Free tier database
db_instance_class = "db.t3.micro"
redis_node_type = "cache.t2.micro"

# Use containerized Kafka (MSK is not free)
use_msk = false
```

## 🚨 Important Warnings

1. **NAT Gateway Cost**: The biggest expense (~$45/month)
2. **Data Transfer**: Monitor outbound data transfer
3. **Storage**: Keep within 20GB for RDS
4. **Monitoring**: Set up billing alerts immediately
5. **Cleanup**: Always destroy resources when not needed

## 🧹 Cleanup

```bash
# Destroy all resources
terraform destroy -var-file=environments/free-tier.tfvars

# Delete ECR repositories
aws ecr delete-repository --repository-name ticket-booking-backend --force
aws ecr delete-repository --repository-name ticket-booking-frontend --force

# Delete S3 bucket (if empty)
aws s3 rb s3://ticket-booking-terraform-state-${AWS_ACCOUNT_ID}
```

## 📞 Support

If you encounter issues:
1. Check AWS Free Tier usage in billing dashboard
2. Review CloudWatch logs for application errors
3. Verify security group configurations
4. Check ECS service health

## 🎯 Next Steps

1. Set up custom domain with Route53
2. Add SSL certificate with ACM
3. Implement CI/CD with GitHub Actions
4. Add monitoring with CloudWatch dashboards
5. Set up automated backups
