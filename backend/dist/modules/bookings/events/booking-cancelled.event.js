"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BookingCancelledEvent = void 0;
const base_event_1 = require("../../../common/events/base.event");
class BookingCancelledEvent extends base_event_1.DomainEvent {
    constructor(aggregateId, reason, cancelledAt, version = 1) {
        super(aggregateId, version);
        this.aggregateId = aggregateId;
        this.reason = reason;
        this.cancelledAt = cancelledAt;
    }
}
exports.BookingCancelledEvent = BookingCancelledEvent;
//# sourceMappingURL=booking-cancelled.event.js.map