"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventCreatedEvent = void 0;
const base_event_1 = require("../../../common/events/base.event");
class EventCreatedEvent extends base_event_1.DomainEvent {
    constructor(aggregateId, eventData, version = 1) {
        super(aggregateId, version);
        this.aggregateId = aggregateId;
        this.eventData = eventData;
    }
}
exports.EventCreatedEvent = EventCreatedEvent;
//# sourceMappingURL=event-created.event.js.map