"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PineconeService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PineconeService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const pinecone_1 = require("@pinecone-database/pinecone");
const openai_1 = require("openai");
let PineconeService = PineconeService_1 = class PineconeService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(PineconeService_1.name);
        this.initializePinecone();
        this.initializeOpenAI();
    }
    async initializePinecone() {
        try {
            const apiKey = this.configService.get('PINECONE_API_KEY');
            const environment = this.configService.get('PINECONE_ENVIRONMENT');
            if (!apiKey || !environment) {
                this.logger.warn('Pinecone configuration is missing - AI features will be disabled');
                return;
            }
            this.pinecone = new pinecone_1.Pinecone({
                apiKey,
                environment,
            });
            this.eventsIndex = this.pinecone.index('events-index');
            this.usersIndex = this.pinecone.index('users-index');
            this.contentIndex = this.pinecone.index('content-index');
            this.logger.log('Pinecone initialized successfully');
        }
        catch (error) {
            this.logger.error('Failed to initialize Pinecone', error);
            this.logger.warn('AI features will be disabled');
        }
    }
    initializeOpenAI() {
        try {
            const apiKey = this.configService.get('OPENAI_API_KEY');
            if (!apiKey) {
                this.logger.warn('OpenAI API key is missing - AI features will be disabled');
                return;
            }
            this.openai = new openai_1.OpenAI({
                apiKey,
            });
            this.logger.log('OpenAI initialized successfully');
        }
        catch (error) {
            this.logger.error('Failed to initialize OpenAI', error);
            this.logger.warn('AI features will be disabled');
        }
    }
    async generateTextEmbedding(text) {
        try {
            const response = await this.openai.embeddings.create({
                model: 'text-embedding-ada-002',
                input: text,
            });
            return response.data[0].embedding;
        }
        catch (error) {
            this.logger.error('Failed to generate text embedding', error);
            throw error;
        }
    }
    async generateEventEmbedding(event) {
        const combinedText = [
            event.title,
            event.description,
            event.category,
            event.venue,
            event.artist,
            ...event.tags,
        ].join(' ');
        return this.generateTextEmbedding(combinedText);
    }
    async generateUserEmbedding(user) {
        const profileText = [
            ...user.preferences,
            ...user.behaviorHistory.map(b => `${b.action} ${b.eventId || b.query || ''}`),
            user.demographics.location || '',
            ...(user.demographics.interests || []),
        ].join(' ');
        return this.generateTextEmbedding(profileText);
    }
    async upsertEvent(event) {
        try {
            const embedding = await this.generateEventEmbedding(event);
            const upsertRequest = {
                id: event.eventId,
                values: embedding,
                metadata: {
                    id: event.eventId,
                    eventId: event.eventId,
                    type: 'event',
                    title: event.title,
                    description: event.description,
                    category: event.category,
                    venue: event.venue,
                    date: event.date.toISOString(),
                    price: event.price,
                    tags: event.tags,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
            };
            await this.eventsIndex.upsert([upsertRequest]);
            this.logger.log(`Event ${event.eventId} upserted to Pinecone`);
        }
        catch (error) {
            this.logger.error(`Failed to upsert event ${event.eventId}`, error);
            throw error;
        }
    }
    async searchSimilarEvents(query, options = {}) {
        try {
            const queryEmbedding = await this.generateTextEmbedding(query);
            const searchRequest = {
                vector: queryEmbedding,
                topK: options.topK || 10,
                includeMetadata: options.includeMetadata !== false,
                filter: options.filter,
                namespace: options.namespace,
            };
            const results = await this.eventsIndex.query(searchRequest);
            return results.matches.map(match => ({
                id: match.id,
                score: match.score,
                metadata: match.metadata,
            }));
        }
        catch (error) {
            this.logger.error('Failed to search similar events', error);
            throw error;
        }
    }
    async findSimilarEvents(request) {
        try {
            const eventVector = await this.eventsIndex.fetch([request.eventId]);
            if (!eventVector.vectors[request.eventId]) {
                throw new Error(`Event ${request.eventId} not found in index`);
            }
            const searchRequest = {
                vector: eventVector.vectors[request.eventId].values,
                topK: (request.limit || 10) + 1,
                includeMetadata: request.includeMetadata !== false,
                filter: { eventId: { $ne: request.eventId } },
            };
            const results = await this.eventsIndex.query(searchRequest);
            return results.matches
                .filter(match => match.score >= (request.threshold || 0.7))
                .slice(0, request.limit || 10)
                .map(match => ({
                id: match.id,
                score: match.score,
                metadata: match.metadata,
            }));
        }
        catch (error) {
            this.logger.error('Failed to find similar events', error);
            throw error;
        }
    }
    async upsertUser(user) {
        try {
            const embedding = await this.generateUserEmbedding(user);
            const upsertRequest = {
                id: user.userId,
                values: embedding,
                metadata: {
                    id: user.userId,
                    userId: user.userId,
                    type: 'user',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                },
            };
            await this.usersIndex.upsert([upsertRequest]);
            this.logger.log(`User ${user.userId} upserted to Pinecone`);
        }
        catch (error) {
            this.logger.error(`Failed to upsert user ${user.userId}`, error);
            throw error;
        }
    }
    async getPersonalizedRecommendations(request) {
        try {
            const userVector = await this.usersIndex.fetch([request.userId]);
            if (!userVector.vectors[request.userId]) {
                return this.getPopularEvents(request.limit || 10);
            }
            const filter = {};
            if (request.categories?.length) {
                filter.category = { $in: request.categories };
            }
            if (request.priceRange) {
                filter.price = {
                    $gte: request.priceRange.min,
                    $lte: request.priceRange.max,
                };
            }
            if (request.excludeEventIds?.length) {
                filter.eventId = { $nin: request.excludeEventIds };
            }
            const searchRequest = {
                vector: userVector.vectors[request.userId].values,
                topK: request.limit || 10,
                includeMetadata: true,
                filter: Object.keys(filter).length > 0 ? filter : undefined,
            };
            const results = await this.eventsIndex.query(searchRequest);
            return results.matches.map(match => ({
                eventId: match.metadata.eventId,
                score: match.score,
                reason: this.generateRecommendationReason(match.score, match.metadata),
                confidence: match.score,
                metadata: match.metadata,
            }));
        }
        catch (error) {
            this.logger.error('Failed to get personalized recommendations', error);
            throw error;
        }
    }
    async getPopularEvents(limit) {
        return [];
    }
    generateRecommendationReason(score, metadata) {
        if (score > 0.9)
            return 'Perfect match for your preferences';
        if (score > 0.8)
            return 'Highly recommended based on your history';
        if (score > 0.7)
            return 'Good match for your interests';
        if (score > 0.6)
            return 'You might enjoy this event';
        return 'Suggested based on similar users';
    }
    async detectFraud(request) {
        try {
            const behaviorText = request.userBehavior
                .map(b => `${b.action} ${b.timestamp.getTime()} ${b.duration || 0}`)
                .join(' ');
            const behaviorEmbedding = await this.generateTextEmbedding(`${behaviorText} amount:${request.purchaseAmount} event:${request.eventId}`);
            const searchRequest = {
                vector: behaviorEmbedding,
                topK: 100,
                includeMetadata: true,
                filter: { type: 'fraud_pattern' },
            };
            const results = await this.contentIndex.query(searchRequest);
            let riskScore = 0;
            const reasons = [];
            if (results.matches.length > 0) {
                const avgSimilarity = results.matches.reduce((sum, match) => sum + match.score, 0) / results.matches.length;
                riskScore = avgSimilarity * 100;
                if (avgSimilarity > 0.8)
                    reasons.push('Behavior matches known fraud patterns');
                if (avgSimilarity > 0.6)
                    reasons.push('Suspicious activity detected');
            }
            if (request.purchaseAmount > 1000) {
                riskScore += 20;
                reasons.push('High-value transaction');
            }
            const riskLevel = riskScore > 70 ? 'high' : riskScore > 40 ? 'medium' : 'low';
            return {
                riskScore,
                riskLevel,
                riskFactors: reasons,
                recommendedAction: this.generateFraudAction(riskLevel),
                confidence: 0.85,
                details: {
                    recommendations: this.generateFraudRecommendations(riskLevel),
                },
            };
        }
        catch (error) {
            this.logger.error('Failed to detect fraud', error);
            throw error;
        }
    }
    generateFraudAction(riskLevel) {
        switch (riskLevel) {
            case 'high':
                return 'BLOCK';
            case 'medium':
                return 'REVIEW';
            case 'low':
                return 'ALLOW';
            default:
                return 'REVIEW';
        }
    }
    generateFraudRecommendations(riskLevel) {
        switch (riskLevel) {
            case 'high':
                return ['Block transaction', 'Require additional verification', 'Manual review'];
            case 'medium':
                return ['Additional verification', 'Monitor closely', 'Limit transaction amount'];
            case 'low':
                return ['Standard processing', 'Log for analysis'];
            default:
                return [];
        }
    }
    async moderateContent(request) {
        try {
            const contentEmbedding = await this.generateTextEmbedding(request.content);
            const searchRequest = {
                vector: contentEmbedding,
                topK: 50,
                includeMetadata: true,
                filter: { type: 'inappropriate_content' },
            };
            const results = await this.contentIndex.query(searchRequest);
            let toxicityScore = 0;
            const categories = [];
            if (results.matches.length > 0) {
                const maxSimilarity = Math.max(...results.matches.map(m => m.score));
                toxicityScore = maxSimilarity * 100;
                results.matches.forEach(match => {
                    if (match.metadata?.category && !categories.includes(match.metadata.category)) {
                        categories.push(match.metadata.category);
                    }
                });
            }
            const sentiment = this.analyzeSentiment(request.content);
            return {
                isAppropriate: toxicityScore < 50,
                confidence: Math.abs(toxicityScore - 50) / 50,
                categories,
                sentiment,
                toxicityScore,
            };
        }
        catch (error) {
            this.logger.error('Failed to moderate content', error);
            throw error;
        }
    }
    analyzeSentiment(content) {
        const positiveWords = ['good', 'great', 'excellent', 'amazing', 'love', 'fantastic'];
        const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'horrible', 'worst'];
        const words = content.toLowerCase().split(/\s+/);
        const positiveCount = words.filter(word => positiveWords.includes(word)).length;
        const negativeCount = words.filter(word => negativeWords.includes(word)).length;
        if (positiveCount > negativeCount)
            return 'positive';
        if (negativeCount > positiveCount)
            return 'negative';
        return 'neutral';
    }
    async performAnalytics(query) {
        try {
            switch (query.type) {
                case 'user_clustering':
                    return this.performUserClustering(query);
                case 'event_performance':
                    return this.analyzeEventPerformance(query);
                case 'trend_analysis':
                    return this.analyzeTrends(query);
                case 'market_insights':
                    return this.generateMarketInsights(query);
                default:
                    throw new Error(`Unknown analytics type: ${query.type}`);
            }
        }
        catch (error) {
            this.logger.error('Failed to perform analytics', error);
            throw error;
        }
    }
    async performUserClustering(query) {
        return {
            type: 'user_clustering',
            data: [],
            insights: ['User segments identified', 'Behavior patterns analyzed'],
            recommendations: ['Target specific segments', 'Personalize marketing'],
            confidence: 0.85,
        };
    }
    async analyzeEventPerformance(query) {
        return {
            type: 'event_performance',
            data: [],
            insights: ['Top performing events identified', 'Success factors analyzed'],
            recommendations: ['Replicate successful patterns', 'Optimize underperforming events'],
            confidence: 0.78,
        };
    }
    async analyzeTrends(query) {
        return {
            type: 'trend_analysis',
            data: [],
            insights: ['Emerging trends detected', 'Seasonal patterns identified'],
            recommendations: ['Capitalize on trends', 'Plan seasonal campaigns'],
            confidence: 0.72,
        };
    }
    async generateMarketInsights(query) {
        return {
            type: 'market_insights',
            data: [],
            insights: ['Market opportunities identified', 'Competitive analysis completed'],
            recommendations: ['Enter new markets', 'Differentiate offerings'],
            confidence: 0.68,
        };
    }
    async optimizePricing(request) {
        try {
            const marketText = [
                `current_price:${request.currentPrice}`,
                `competitor_avg:${request.marketConditions.competitorPrices.reduce((a, b) => a + b, 0) / request.marketConditions.competitorPrices.length}`,
                `seasonality:${request.marketConditions.seasonality}`,
                `demand:${request.demandSignals.reduce((sum, signal) => sum + signal.searchVolume, 0)}`,
            ].join(' ');
            const marketEmbedding = await this.generateTextEmbedding(marketText);
            const searchRequest = {
                vector: marketEmbedding,
                topK: 20,
                includeMetadata: true,
                filter: { type: 'pricing_data' },
            };
            const results = await this.contentIndex.query(searchRequest);
            let recommendedPrice = request.currentPrice;
            const reasoning = [];
            if (results.matches.length > 0) {
                const avgOptimalPrice = results.matches.reduce((sum, match) => {
                    return sum + (match.metadata?.optimal_price || request.currentPrice);
                }, 0) / results.matches.length;
                recommendedPrice = avgOptimalPrice;
                reasoning.push('Based on similar market conditions');
            }
            const totalDemand = request.demandSignals.reduce((sum, signal) => sum + signal.searchVolume, 0);
            if (totalDemand > 1000) {
                recommendedPrice *= 1.1;
                reasoning.push('High demand detected');
            }
            else if (totalDemand < 100) {
                recommendedPrice *= 0.9;
                reasoning.push('Low demand, price reduction recommended');
            }
            return {
                recommendedPrice: Math.round(recommendedPrice * 100) / 100,
                priceRange: {
                    min: Math.round(recommendedPrice * 0.8 * 100) / 100,
                    max: Math.round(recommendedPrice * 1.2 * 100) / 100,
                },
                confidence: 0.75,
                reasoning,
                expectedDemand: totalDemand * 1.2,
                revenueProjection: recommendedPrice * totalDemand * 1.2,
            };
        }
        catch (error) {
            this.logger.error('Failed to optimize pricing', error);
            throw error;
        }
    }
    async deleteVector(indexName, id) {
        try {
            const index = this.pinecone.index(indexName);
            await index.deleteOne(id);
            this.logger.log(`Vector ${id} deleted from ${indexName}`);
        }
        catch (error) {
            this.logger.error(`Failed to delete vector ${id} from ${indexName}`, error);
            throw error;
        }
    }
    async getIndexStats(indexName) {
        try {
            const index = this.pinecone.index(indexName);
            return await index.describeIndexStats();
        }
        catch (error) {
            this.logger.error(`Failed to get stats for ${indexName}`, error);
            throw error;
        }
    }
};
exports.PineconeService = PineconeService;
exports.PineconeService = PineconeService = PineconeService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], PineconeService);
//# sourceMappingURL=pinecone.service.js.map