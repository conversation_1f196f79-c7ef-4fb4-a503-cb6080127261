import { ConfigService } from '@nestjs/config';
import { SearchQuery, SearchResult, EventEmbedding, UserEmbedding, RecommendationRequest, RecommendationResult, SimilarityRequest, FraudDetectionRequest, FraudDetectionResult, ContentModerationRequest, ContentModerationResult, AnalyticsQuery, AnalyticsResult, PricingOptimizationRequest, PricingOptimizationResult } from '../interfaces/pinecone.interface';
export declare class PineconeService {
    private configService;
    private readonly logger;
    private pinecone;
    private openai;
    private eventsIndex;
    private usersIndex;
    private contentIndex;
    constructor(configService: ConfigService);
    private initializePinecone;
    private initializeOpenAI;
    generateTextEmbedding(text: string): Promise<number[]>;
    generateEventEmbedding(event: EventEmbedding): Promise<number[]>;
    generateUserEmbedding(user: UserEmbedding): Promise<number[]>;
    upsertEvent(event: EventEmbedding): Promise<void>;
    searchSimilarEvents(query: string, options?: SearchQuery): Promise<SearchResult[]>;
    findSimilarEvents(request: SimilarityRequest): Promise<SearchResult[]>;
    upsertUser(user: UserEmbedding): Promise<void>;
    getPersonalizedRecommendations(request: RecommendationRequest): Promise<RecommendationResult[]>;
    private getPopularEvents;
    private generateRecommendationReason;
    detectFraud(request: FraudDetectionRequest): Promise<FraudDetectionResult>;
    private generateFraudRecommendations;
    moderateContent(request: ContentModerationRequest): Promise<ContentModerationResult>;
    private analyzeSentiment;
    performAnalytics(query: AnalyticsQuery): Promise<AnalyticsResult>;
    private performUserClustering;
    private analyzeEventPerformance;
    private analyzeTrends;
    private generateMarketInsights;
    optimizePricing(request: PricingOptimizationRequest): Promise<PricingOptimizationResult>;
    deleteVector(indexName: string, id: string): Promise<void>;
    getIndexStats(indexName: string): Promise<any>;
}
