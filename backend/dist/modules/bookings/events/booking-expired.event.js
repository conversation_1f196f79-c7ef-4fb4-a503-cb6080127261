"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BookingExpiredEvent = void 0;
const base_event_1 = require("../../../common/events/base.event");
class BookingExpiredEvent extends base_event_1.DomainEvent {
    constructor(aggregateId, expiredAt, version = 1) {
        super(aggregateId, version);
        this.aggregateId = aggregateId;
        this.expiredAt = expiredAt;
    }
}
exports.BookingExpiredEvent = BookingExpiredEvent;
//# sourceMappingURL=booking-expired.event.js.map