"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReserveSeatsCommand = void 0;
const base_command_1 = require("../../../common/commands/base.command");
class ReserveSeatsCommand extends base_command_1.Command {
    constructor(bookingId, eventId, seatIds, expirationMinutes = 15) {
        super();
        this.bookingId = bookingId;
        this.eventId = eventId;
        this.seatIds = seatIds;
        this.expirationMinutes = expirationMinutes;
    }
}
exports.ReserveSeatsCommand = ReserveSeatsCommand;
//# sourceMappingURL=reserve-seats.command.js.map