#!/bin/bash

# Pinecone Setup Script for Ticket Booking System
# This script sets up Pinecone indexes and initial configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧠 Setting up Pinecone Vector Database${NC}"
echo -e "${BLUE}====================================${NC}"

# Check prerequisites
echo -e "${BLUE}📋 Checking prerequisites...${NC}"

# Check if required environment variables are set
if [ -z "$PINECONE_API_KEY" ]; then
    echo -e "${RED}❌ PINECONE_API_KEY environment variable is not set${NC}"
    echo -e "${YELLOW}Please set your Pinecone API key:${NC}"
    echo -e "${YELLOW}export PINECONE_API_KEY='your-api-key'${NC}"
    exit 1
fi

if [ -z "$OPENAI_API_KEY" ]; then
    echo -e "${RED}❌ OPENAI_API_KEY environment variable is not set${NC}"
    echo -e "${YELLOW}Please set your OpenAI API key:${NC}"
    echo -e "${YELLOW}export OPENAI_API_KEY='your-api-key'${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Environment variables configured${NC}"

# Check if Node.js and npm are installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js is not installed${NC}"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ npm is not installed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Node.js and npm are available${NC}"

# Install Pinecone CLI (optional)
echo -e "${BLUE}📦 Installing Pinecone dependencies...${NC}"

cd backend

# Install Pinecone and OpenAI packages
npm install @pinecone-database/pinecone openai

echo -e "${GREEN}✅ Dependencies installed${NC}"

# Create Pinecone setup script
cat > setup-pinecone-indexes.js << 'EOF'
const { Pinecone } = require('@pinecone-database/pinecone');

async function setupPineconeIndexes() {
  console.log('🔧 Setting up Pinecone indexes...');
  
  const pinecone = new Pinecone({
    apiKey: process.env.PINECONE_API_KEY,
    environment: process.env.PINECONE_ENVIRONMENT || 'us-west1-gcp',
  });

  const indexes = [
    {
      name: 'events-index',
      dimension: 1536, // OpenAI text-embedding-ada-002 dimensions
      metric: 'cosine',
      spec: {
        pod: {
          environment: process.env.PINECONE_ENVIRONMENT || 'us-west1-gcp',
          podType: 'p1.x1',
        }
      }
    },
    {
      name: 'users-index',
      dimension: 512, // Custom user embeddings
      metric: 'cosine',
      spec: {
        pod: {
          environment: process.env.PINECONE_ENVIRONMENT || 'us-west1-gcp',
          podType: 'p1.x1',
        }
      }
    },
    {
      name: 'content-index',
      dimension: 768, // BERT embeddings for content moderation
      metric: 'cosine',
      spec: {
        pod: {
          environment: process.env.PINECONE_ENVIRONMENT || 'us-west1-gcp',
          podType: 'p1.x1',
        }
      }
    }
  ];

  for (const indexConfig of indexes) {
    try {
      console.log(`Creating index: ${indexConfig.name}`);
      
      // Check if index already exists
      const existingIndexes = await pinecone.listIndexes();
      const indexExists = existingIndexes.indexes?.some(index => index.name === indexConfig.name);
      
      if (indexExists) {
        console.log(`✅ Index ${indexConfig.name} already exists`);
        continue;
      }

      // Create the index
      await pinecone.createIndex({
        name: indexConfig.name,
        dimension: indexConfig.dimension,
        metric: indexConfig.metric,
        spec: indexConfig.spec,
      });

      console.log(`✅ Index ${indexConfig.name} created successfully`);
      
      // Wait for index to be ready
      console.log(`⏳ Waiting for index ${indexConfig.name} to be ready...`);
      
      let isReady = false;
      let attempts = 0;
      const maxAttempts = 30;
      
      while (!isReady && attempts < maxAttempts) {
        try {
          const indexStats = await pinecone.index(indexConfig.name).describeIndexStats();
          isReady = true;
          console.log(`✅ Index ${indexConfig.name} is ready`);
        } catch (error) {
          attempts++;
          console.log(`⏳ Attempt ${attempts}/${maxAttempts} - waiting for index to be ready...`);
          await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
        }
      }
      
      if (!isReady) {
        console.log(`⚠️  Index ${indexConfig.name} may not be fully ready yet`);
      }
      
    } catch (error) {
      console.error(`❌ Failed to create index ${indexConfig.name}:`, error.message);
    }
  }

  console.log('🎉 Pinecone setup completed!');
}

// Run the setup
setupPineconeIndexes().catch(console.error);
EOF

echo -e "${BLUE}🔧 Creating Pinecone indexes...${NC}"

# Set environment variables for the script
export PINECONE_ENVIRONMENT=${PINECONE_ENVIRONMENT:-"us-west1-gcp"}

# Run the Pinecone setup script
node setup-pinecone-indexes.js

# Clean up the temporary script
rm setup-pinecone-indexes.js

echo -e "${GREEN}✅ Pinecone indexes created${NC}"

# Create sample data insertion script
echo -e "${BLUE}📝 Creating sample data insertion script...${NC}"

cat > insert-sample-data.js << 'EOF'
const { Pinecone } = require('@pinecone-database/pinecone');
const { OpenAI } = require('openai');

async function insertSampleData() {
  console.log('📝 Inserting sample data...');
  
  const pinecone = new Pinecone({
    apiKey: process.env.PINECONE_API_KEY,
    environment: process.env.PINECONE_ENVIRONMENT || 'us-west1-gcp',
  });

  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });

  const eventsIndex = pinecone.index('events-index');

  // Sample events data
  const sampleEvents = [
    {
      id: 'event-1',
      title: 'Rock Concert - The Rolling Stones',
      description: 'Legendary rock band performing their greatest hits at Madison Square Garden',
      category: 'music',
      venue: 'Madison Square Garden',
      artist: 'The Rolling Stones',
      price: 150,
      tags: ['rock', 'classic', 'legendary'],
    },
    {
      id: 'event-2',
      title: 'Jazz Festival - Miles Davis Tribute',
      description: 'Annual jazz festival featuring tribute to Miles Davis with world-class musicians',
      category: 'music',
      venue: 'Blue Note',
      artist: 'Various Artists',
      price: 75,
      tags: ['jazz', 'tribute', 'festival'],
    },
    {
      id: 'event-3',
      title: 'Broadway Musical - Hamilton',
      description: 'Award-winning musical about Alexander Hamilton and the founding fathers',
      category: 'theater',
      venue: 'Richard Rodgers Theatre',
      artist: 'Lin-Manuel Miranda',
      price: 200,
      tags: ['broadway', 'musical', 'history'],
    },
    {
      id: 'event-4',
      title: 'Comedy Show - Dave Chappelle',
      description: 'Stand-up comedy show by the legendary comedian Dave Chappelle',
      category: 'comedy',
      venue: 'Comedy Cellar',
      artist: 'Dave Chappelle',
      price: 100,
      tags: ['comedy', 'stand-up', 'entertainment'],
    },
    {
      id: 'event-5',
      title: 'NBA Game - Lakers vs Knicks',
      description: 'Exciting basketball game between Los Angeles Lakers and New York Knicks',
      category: 'sports',
      venue: 'Madison Square Garden',
      artist: 'NBA',
      price: 120,
      tags: ['basketball', 'nba', 'sports'],
    },
  ];

  for (const event of sampleEvents) {
    try {
      console.log(`Processing event: ${event.title}`);
      
      // Generate embedding for the event
      const combinedText = [
        event.title,
        event.description,
        event.category,
        event.venue,
        event.artist,
        ...event.tags,
      ].join(' ');

      const embeddingResponse = await openai.embeddings.create({
        model: 'text-embedding-ada-002',
        input: combinedText,
      });

      const embedding = embeddingResponse.data[0].embedding;

      // Upsert to Pinecone
      await eventsIndex.upsert([
        {
          id: event.id,
          values: embedding,
          metadata: {
            eventId: event.id,
            type: 'event',
            title: event.title,
            description: event.description,
            category: event.category,
            venue: event.venue,
            artist: event.artist,
            price: event.price,
            tags: event.tags,
            createdAt: new Date().toISOString(),
          },
        },
      ]);

      console.log(`✅ Event ${event.id} inserted successfully`);
      
    } catch (error) {
      console.error(`❌ Failed to insert event ${event.id}:`, error.message);
    }
  }

  console.log('🎉 Sample data insertion completed!');
}

// Run the insertion
insertSampleData().catch(console.error);
EOF

echo -e "${BLUE}📝 Inserting sample data...${NC}"

# Run the sample data insertion
node insert-sample-data.js

# Clean up the temporary script
rm insert-sample-data.js

echo -e "${GREEN}✅ Sample data inserted${NC}"

# Create test script
echo -e "${BLUE}🧪 Creating test script...${NC}"

cat > test-pinecone.js << 'EOF'
const { Pinecone } = require('@pinecone-database/pinecone');
const { OpenAI } = require('openai');

async function testPinecone() {
  console.log('🧪 Testing Pinecone integration...');
  
  const pinecone = new Pinecone({
    apiKey: process.env.PINECONE_API_KEY,
    environment: process.env.PINECONE_ENVIRONMENT || 'us-west1-gcp',
  });

  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });

  const eventsIndex = pinecone.index('events-index');

  try {
    // Test 1: Check index stats
    console.log('📊 Checking index statistics...');
    const stats = await eventsIndex.describeIndexStats();
    console.log(`✅ Index has ${stats.totalVectorCount} vectors`);

    // Test 2: Perform a search
    console.log('🔍 Testing semantic search...');
    const queryText = 'rock music concert';
    
    const embeddingResponse = await openai.embeddings.create({
      model: 'text-embedding-ada-002',
      input: queryText,
    });

    const queryEmbedding = embeddingResponse.data[0].embedding;

    const searchResults = await eventsIndex.query({
      vector: queryEmbedding,
      topK: 3,
      includeMetadata: true,
    });

    console.log(`✅ Search for "${queryText}" returned ${searchResults.matches.length} results:`);
    
    searchResults.matches.forEach((match, index) => {
      console.log(`  ${index + 1}. ${match.metadata.title} (score: ${match.score.toFixed(3)})`);
    });

    console.log('🎉 All tests passed! Pinecone is working correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testPinecone().catch(console.error);
EOF

echo -e "${BLUE}🧪 Running tests...${NC}"

# Run the test
node test-pinecone.js

# Clean up the temporary script
rm test-pinecone.js

cd ..

echo -e "${GREEN}🎉 Pinecone setup completed successfully!${NC}"
echo -e "${BLUE}📋 Summary:${NC}"
echo -e "${GREEN}✅ Pinecone indexes created (events, users, content)${NC}"
echo -e "${GREEN}✅ Sample data inserted${NC}"
echo -e "${GREEN}✅ Integration tested${NC}"

echo -e "\n${BLUE}🔧 Next steps:${NC}"
echo -e "${YELLOW}1. Update your .env file with Pinecone credentials${NC}"
echo -e "${YELLOW}2. Start your NestJS application${NC}"
echo -e "${YELLOW}3. Test the AI endpoints at /api/v1/ai/*${NC}"

echo -e "\n${BLUE}📚 Available AI features:${NC}"
echo -e "${GREEN}• Semantic event search${NC}"
echo -e "${GREEN}• Personalized recommendations${NC}"
echo -e "${GREEN}• Fraud detection${NC}"
echo -e "${GREEN}• Content moderation${NC}"
echo -e "${GREEN}• Pricing optimization${NC}"
echo -e "${GREEN}• Advanced analytics${NC}"

echo -e "\n${BLUE}💰 Cost optimization tips:${NC}"
echo -e "${YELLOW}• Use p1.x1 pods for development (cheapest)${NC}"
echo -e "${YELLOW}• Monitor vector count to stay within limits${NC}"
echo -e "${YELLOW}• Use batch operations for efficiency${NC}"
echo -e "${YELLOW}• Consider dimensionality reduction for large datasets${NC}"
