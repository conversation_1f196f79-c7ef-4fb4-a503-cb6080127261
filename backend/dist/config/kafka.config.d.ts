import { OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { KafkaMessage } from 'kafkajs';
export declare class KafkaConfig implements OnModuleInit, OnModuleDestroy {
    private configService;
    private kafka;
    private producer;
    private consumer;
    constructor(configService: ConfigService);
    onModuleInit(): Promise<void>;
    onModuleDestroy(): Promise<void>;
    publishEvent(topic: string, message: any): Promise<void>;
    subscribe(topic: string, callback: (message: KafkaMessage) => Promise<void>): Promise<void>;
    createTopics(topics: string[]): Promise<void>;
}
