"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BookingInitiatedEvent = void 0;
const base_event_1 = require("../../../common/events/base.event");
class BookingInitiatedEvent extends base_event_1.DomainEvent {
    constructor(aggregateId, bookingData, version = 1) {
        super(aggregateId, version);
        this.aggregateId = aggregateId;
        this.bookingData = bookingData;
    }
}
exports.BookingInitiatedEvent = BookingInitiatedEvent;
//# sourceMappingURL=booking-initiated.event.js.map