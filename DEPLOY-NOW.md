# 🚀 Deploy to AWS Right Now - Step by Step

## ⚡ Quick Start (5 Minutes)

### **Prerequisites Check**
```bash
# 1. Install AWS CLI (if not installed)
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# 2. Install Terraform (if not installed)
wget https://releases.hashicorp.com/terraform/1.6.6/terraform_1.6.6_linux_amd64.zip
unzip terraform_1.6.6_linux_amd64.zip
sudo mv terraform /usr/local/bin/

# 3. Install Docker (if not installed)
sudo apt-get update
sudo apt-get install docker.io
sudo usermod -aG docker $USER
```

### **Step 1: Configure AWS Credentials**
```bash
# Configure your AWS credentials
aws configure

# Enter your:
# AWS Access Key ID: [Your Access Key]
# AWS Secret Access Key: [Your Secret Key]  
# Default region name: us-east-1
# Default output format: json

# Test connection
aws sts get-caller-identity
```

### **Step 2: Clone and Navigate**
```bash
# You should already be in the project directory
cd /Users/<USER>/Documents/augment-projects/onlineTicketBookingSystem

# Verify files exist
ls -la scripts/
ls -la infrastructure/terraform/
```

### **Step 3: Choose Deployment Option**

#### **Option A: Ultra Free Tier (Recommended - $0/month)**
```bash
# Deploy without NAT Gateway (completely free)
cd infrastructure/terraform

# Initialize Terraform
terraform init

# Update with your AWS account
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
sed -i.bak "s/your-account/${AWS_ACCOUNT_ID}/g" environments/ultra-free-tier.tfvars

# Plan deployment
terraform plan -var-file=environments/ultra-free-tier.tfvars

# Deploy (type 'yes' when prompted)
terraform apply -var-file=environments/ultra-free-tier.tfvars
```

#### **Option B: Automated Deployment**
```bash
# One command deployment (includes ECR setup)
chmod +x scripts/deploy-free-tier.sh
./scripts/deploy-free-tier.sh
```

### **Step 4: Build and Push Containers**
```bash
# Get your AWS account ID
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
echo "Your AWS Account ID: $AWS_ACCOUNT_ID"

# Create ECR repositories
aws ecr create-repository --repository-name ticket-booking-backend --region us-east-1
aws ecr create-repository --repository-name ticket-booking-frontend --region us-east-1

# Login to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.us-east-1.amazonaws.com

# Build and push backend
cd backend
docker build -t ticket-booking-backend:latest --target production .
docker tag ticket-booking-backend:latest ${AWS_ACCOUNT_ID}.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-backend:latest
docker push ${AWS_ACCOUNT_ID}.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-backend:latest

# Build and push frontend  
cd ../frontend
docker build -t ticket-booking-frontend:latest --target production .
docker tag ticket-booking-frontend:latest ${AWS_ACCOUNT_ID}.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-frontend:latest
docker push ${AWS_ACCOUNT_ID}.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-frontend:latest

cd ..
```

### **Step 5: Update Terraform with Container URLs**
```bash
cd infrastructure/terraform

# Update tfvars with your ECR URLs
sed -i.bak "s/your-account/${AWS_ACCOUNT_ID}/g" environments/ultra-free-tier.tfvars

# Apply the updated configuration
terraform apply -var-file=environments/ultra-free-tier.tfvars
```

### **Step 6: Get Your Application URL**
```bash
# Get the load balancer URL
ALB_DNS=$(terraform output -raw alb_dns_name)
echo "🎉 Your application is available at: http://${ALB_DNS}"
echo "🔍 API health check: http://${ALB_DNS}/api/v1/health"
```

## 🔧 **Troubleshooting**

### **Common Issues:**

#### **1. AWS Credentials Error**
```bash
# If you get credential errors
aws configure list
aws sts get-caller-identity
```

#### **2. Docker Permission Error**
```bash
# If docker commands fail
sudo usermod -aG docker $USER
newgrp docker
```

#### **3. Terraform State Error**
```bash
# If terraform init fails
rm -rf .terraform
terraform init
```

#### **4. Container Build Fails**
```bash
# Check if Docker is running
sudo systemctl start docker

# Check available space
df -h
```

### **5. Service Not Starting**
```bash
# Check ECS service status
aws ecs describe-services --cluster ticket-booking-dev-cluster --services backend frontend

# Check logs
aws logs tail /ecs/ticket-booking-dev-backend --follow
```

## 💰 **Monitor Costs**
```bash
# Run cost monitoring
./scripts/monitor-costs.sh

# Set up billing alert
aws cloudwatch put-metric-alarm \
  --alarm-name "BillingAlarm" \
  --alarm-description "Billing alarm" \
  --metric-name EstimatedCharges \
  --namespace AWS/Billing \
  --statistic Maximum \
  --period 86400 \
  --threshold 10.0 \
  --comparison-operator GreaterThanThreshold \
  --dimensions Name=Currency,Value=USD
```

## 🧹 **Cleanup (When Done Testing)**
```bash
# Destroy all resources
cd infrastructure/terraform
terraform destroy -var-file=environments/ultra-free-tier.tfvars

# Delete ECR repositories
aws ecr delete-repository --repository-name ticket-booking-backend --force
aws ecr delete-repository --repository-name ticket-booking-frontend --force
```

## ✅ **Expected Results**

After successful deployment:
- ✅ **Frontend**: Next.js app running on ECS
- ✅ **Backend**: NestJS API with Saga patterns
- ✅ **Database**: PostgreSQL with event sourcing
- ✅ **Cache**: Redis for sessions and rate limiting
- ✅ **Queue**: Kafka for event processing
- ✅ **Load Balancer**: Routes traffic to services

**Total Time**: 10-15 minutes  
**Cost**: $0-5/month (ultra free tier)  
**URLs**: 
- Frontend: `http://[ALB-DNS]`
- API: `http://[ALB-DNS]/api/v1/health`

## 🆘 **Need Help?**

If you encounter any issues:

1. **Check AWS Free Tier limits** in your billing dashboard
2. **Verify region** is set to `us-east-1`
3. **Check security groups** allow traffic on ports 80, 3000, 3001
4. **Review CloudWatch logs** for application errors
5. **Run cost monitor** to check for unexpected charges

**Ready to deploy? Start with Step 1! 🚀**
