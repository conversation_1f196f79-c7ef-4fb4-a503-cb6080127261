"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentSucceededEvent = void 0;
const base_event_1 = require("../../../common/events/base.event");
class PaymentSucceededEvent extends base_event_1.DomainEvent {
    constructor(aggregateId, bookingId, transactionId, amount, currency, version = 1) {
        super(aggregateId, version);
        this.aggregateId = aggregateId;
        this.bookingId = bookingId;
        this.transactionId = transactionId;
        this.amount = amount;
        this.currency = currency;
    }
}
exports.PaymentSucceededEvent = PaymentSucceededEvent;
//# sourceMappingURL=payment-succeeded.event.js.map