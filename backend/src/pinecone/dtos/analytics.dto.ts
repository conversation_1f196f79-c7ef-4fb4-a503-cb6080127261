import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON>y, IsEnum, IsObject, IsDateString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum AnalyticsType {
  USER_CLUSTERING = 'user_clustering',
  EVENT_PERFORMANCE = 'event_performance',
  TREND_ANALYSIS = 'trend_analysis',
  MARKET_INSIGHTS = 'market_insights',
}

export class AnalyticsQueryDto {
  @ApiProperty({
    description: 'Type of analytics to perform',
    enum: AnalyticsType,
    example: AnalyticsType.USER_CLUSTERING,
  })
  @IsEnum(AnalyticsType)
  type: AnalyticsType;

  @ApiProperty({
    description: 'Start date for analysis',
    example: '2024-01-01T00:00:00Z',
  })
  @IsDateString()
  startDate: string;

  @ApiProperty({
    description: 'End date for analysis',
    example: '2024-12-31T23:59:59Z',
  })
  @IsDateString()
  endDate: string;

  @ApiPropertyOptional({
    description: 'Additional filters for analysis',
    example: { category: 'music', venue: 'Madison Square Garden' },
  })
  @IsOptional()
  @IsObject()
  filters?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Group results by specific fields',
    example: ['category', 'venue'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  groupBy?: string[];
}

export class FraudDetectionDto {
  @ApiProperty({
    description: 'User ID to analyze',
    example: 'user-123',
  })
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'Event ID being purchased',
    example: 'event-456',
  })
  @IsString()
  eventId: string;

  @ApiProperty({
    description: 'Purchase amount',
    example: 150.00,
  })
  @IsNumber()
  purchaseAmount: number;

  @ApiProperty({
    description: 'User behavior data',
    example: [
      {
        action: 'view',
        eventId: 'event-456',
        timestamp: '2024-01-15T10:30:00Z',
        duration: 45000,
      },
    ],
  })
  @IsArray()
  userBehavior: UserBehaviorDto[];

  @ApiPropertyOptional({
    description: 'Device information',
    example: { userAgent: 'Mozilla/5.0...', ip: '***********' },
  })
  @IsOptional()
  @IsObject()
  deviceInfo?: Record<string, any>;
}

export class UserBehaviorDto {
  @ApiProperty({
    description: 'User action type',
    example: 'view',
  })
  @IsString()
  action: string;

  @ApiPropertyOptional({
    description: 'Event ID (if applicable)',
    example: 'event-456',
  })
  @IsOptional()
  @IsString()
  eventId?: string;

  @ApiPropertyOptional({
    description: 'Search query (if applicable)',
    example: 'rock concerts NYC',
  })
  @IsOptional()
  @IsString()
  query?: string;

  @ApiProperty({
    description: 'Timestamp of action',
    example: '2024-01-15T10:30:00Z',
  })
  @IsDateString()
  timestamp: string;

  @ApiPropertyOptional({
    description: 'Duration of action in milliseconds',
    example: 45000,
  })
  @IsOptional()
  @IsNumber()
  duration?: number;

  @ApiPropertyOptional({
    description: 'Additional metadata',
    example: { page: 'event-details', referrer: 'search' },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class ContentModerationDto {
  @ApiProperty({
    description: 'Content to moderate',
    example: 'This event was absolutely amazing! Great performance.',
  })
  @IsString()
  content: string;

  @ApiProperty({
    description: 'Type of content',
    example: 'review',
  })
  @IsString()
  contentType: string;

  @ApiPropertyOptional({
    description: 'User ID who created the content',
    example: 'user-789',
  })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiPropertyOptional({
    description: 'Event ID the content relates to',
    example: 'event-456',
  })
  @IsOptional()
  @IsString()
  eventId?: string;
}

export class PricingOptimizationDto {
  @ApiProperty({
    description: 'Event ID to optimize pricing for',
    example: 'event-789',
  })
  @IsString()
  eventId: string;

  @ApiProperty({
    description: 'Current ticket price',
    example: 75.00,
  })
  @IsNumber()
  currentPrice: number;

  @ApiProperty({
    description: 'Market conditions data',
  })
  @IsObject()
  marketConditions: MarketConditionsDto;

  @ApiProperty({
    description: 'Demand signals data',
  })
  @IsArray()
  demandSignals: DemandSignalDto[];
}

export class MarketConditionsDto {
  @ApiProperty({
    description: 'Competitor prices',
    example: [65.00, 80.00, 70.00, 85.00],
  })
  @IsArray()
  @IsNumber({}, { each: true })
  competitorPrices: number[];

  @ApiProperty({
    description: 'Seasonality factor',
    example: 1.2,
  })
  @IsNumber()
  seasonality: number;

  @ApiProperty({
    description: 'Economic indicators',
    example: { inflation: 3.2, unemployment: 4.1, gdp_growth: 2.8 },
  })
  @IsObject()
  economicIndicators: Record<string, number>;

  @ApiPropertyOptional({
    description: 'Weather forecast',
    example: 'sunny',
  })
  @IsOptional()
  @IsString()
  weatherForecast?: string;
}

export class DemandSignalDto {
  @ApiProperty({
    description: 'Timestamp of signal',
    example: '2024-01-15T12:00:00Z',
  })
  @IsDateString()
  timestamp: string;

  @ApiProperty({
    description: 'Search volume',
    example: 150,
  })
  @IsNumber()
  searchVolume: number;

  @ApiProperty({
    description: 'View count',
    example: 1200,
  })
  @IsNumber()
  viewCount: number;

  @ApiProperty({
    description: 'Wishlist additions',
    example: 45,
  })
  @IsNumber()
  wishlistAdds: number;

  @ApiProperty({
    description: 'Social media mentions',
    example: 78,
  })
  @IsNumber()
  socialMentions: number;
}

export class FraudDetectionResultDto {
  @ApiProperty({
    description: 'Risk score (0-100)',
    example: 25,
  })
  riskScore: number;

  @ApiProperty({
    description: 'Risk level',
    example: 'low',
  })
  riskLevel: string;

  @ApiProperty({
    description: 'Reasons for risk assessment',
    example: ['Standard processing', 'No suspicious patterns detected'],
  })
  reasons: string[];

  @ApiProperty({
    description: 'Recommended actions',
    example: ['Standard processing', 'Log for analysis'],
  })
  recommendations: string[];
}

export class ContentModerationResultDto {
  @ApiProperty({
    description: 'Whether content is appropriate',
    example: true,
  })
  isAppropriate: boolean;

  @ApiProperty({
    description: 'Confidence in moderation decision',
    example: 0.92,
  })
  confidence: number;

  @ApiProperty({
    description: 'Content categories detected',
    example: ['positive', 'entertainment'],
  })
  categories: string[];

  @ApiProperty({
    description: 'Sentiment analysis result',
    example: 'positive',
  })
  sentiment: string;

  @ApiProperty({
    description: 'Toxicity score (0-100)',
    example: 5,
  })
  toxicityScore: number;
}

export class PricingOptimizationResultDto {
  @ApiProperty({
    description: 'Recommended price',
    example: 82.50,
  })
  recommendedPrice: number;

  @ApiProperty({
    description: 'Price range recommendation',
    example: { min: 70.00, max: 95.00 },
  })
  priceRange: { min: number; max: number };

  @ApiProperty({
    description: 'Confidence in recommendation',
    example: 0.78,
  })
  confidence: number;

  @ApiProperty({
    description: 'Reasoning for price recommendation',
    example: ['Based on similar market conditions', 'High demand detected'],
  })
  reasoning: string[];

  @ApiProperty({
    description: 'Expected demand at recommended price',
    example: 450,
  })
  expectedDemand: number;

  @ApiProperty({
    description: 'Revenue projection',
    example: 37125.00,
  })
  revenueProjection: number;
}

export class AnalyticsResultDto {
  @ApiProperty({
    description: 'Analytics type',
    example: 'user_clustering',
  })
  type: string;

  @ApiProperty({
    description: 'Analysis data',
    example: [{ segment: 'music_lovers', count: 1250, characteristics: ['frequent_concert_goer', 'high_spender'] }],
  })
  data: any[];

  @ApiProperty({
    description: 'Key insights from analysis',
    example: ['3 distinct user segments identified', 'Music lovers segment shows highest engagement'],
  })
  insights: string[];

  @ApiProperty({
    description: 'Actionable recommendations',
    example: ['Target music lovers with premium events', 'Create loyalty program for high spenders'],
  })
  recommendations: string[];

  @ApiProperty({
    description: 'Confidence in analysis',
    example: 0.85,
  })
  confidence: number;
}
