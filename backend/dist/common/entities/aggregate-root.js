"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AggregateRoot = void 0;
const cqrs_1 = require("@nestjs/cqrs");
class AggregateRoot extends cqrs_1.AggregateRoot {
    constructor(id) {
        super();
        this._version = 0;
        this._uncommittedEvents = [];
        this._id = id || this.generateId();
    }
    get id() {
        return this._id;
    }
    get version() {
        return this._version;
    }
    apply(event) {
        this._uncommittedEvents.push(event);
        this.applyEvent(event);
        this._version++;
    }
    getUncommittedEvents() {
        return [...this._uncommittedEvents];
    }
    markEventsAsCommitted() {
        this._uncommittedEvents = [];
    }
    loadFromHistory(events) {
        events.forEach(event => {
            this.applyEvent(event);
            this._version++;
        });
    }
    generateId() {
        return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
}
exports.AggregateRoot = AggregateRoot;
//# sourceMappingURL=aggregate-root.js.map