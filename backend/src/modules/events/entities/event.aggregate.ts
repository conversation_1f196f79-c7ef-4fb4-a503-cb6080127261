import { AggregateRoot } from '../../../common/entities/aggregate-root';
import { BaseEvent } from '../../../common/events/base.event';
import { EventCreatedEvent } from '../events/event-created.event';
import { EventUpdatedEvent } from '../events/event-updated.event';
import { EventPublishedEvent } from '../events/event-published.event';
import { EventCancelledEvent } from '../events/event-cancelled.event';
import { SeatReservedEvent } from '../events/seat-reserved.event';
import { SeatReleasedEvent } from '../events/seat-released.event';

export enum EventStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  CANCELLED = 'CANCELLED',
  COMPLETED = 'COMPLETED'
}

export enum EventCategory {
  CONCERT = 'CONCERT',
  SPORTS = 'SPORTS',
  THEATER = 'THEATER',
  CONFERENCE = 'CONFERENCE',
  FESTIVAL = 'FESTIVAL',
  OTHER = 'OTHER'
}

export interface EventData {
  title: string;
  description?: string;
  category: EventCategory;
  startDate: Date;
  endDate: Date;
  location: string;
  venue?: string;
  address?: string;
  latitude?: number;
  longitude?: number;
  totalSeats: number;
  basePrice: number;
  organizerId?: string;
  tags?: string[];
  imageUrl?: string;
}

export class EventAggregate extends AggregateRoot {
  private _title: string;
  private _description?: string;
  private _category: EventCategory;
  private _startDate: Date;
  private _endDate: Date;
  private _location: string;
  private _venue?: string;
  private _address?: string;
  private _latitude?: number;
  private _longitude?: number;
  private _totalSeats: number;
  private _availableSeats: number;
  private _basePrice: number;
  private _status: EventStatus;
  private _organizerId?: string;
  private _tags?: string[];
  private _imageUrl?: string;
  private _reservedSeats: Set<string> = new Set();

  constructor(id?: string) {
    super(id);
    this._status = EventStatus.DRAFT;
  }

  static create(eventData: EventData): EventAggregate {
    const event = new EventAggregate();
    event.apply(new EventCreatedEvent(
      event.id,
      eventData,
      event.version + 1
    ));
    return event;
  }

  updateEvent(eventData: Partial<EventData>): void {
    if (this._status === EventStatus.CANCELLED) {
      throw new Error('Cannot update cancelled event');
    }

    this.apply(new EventUpdatedEvent(
      this.id,
      eventData,
      this.version + 1
    ));
  }

  publish(): void {
    if (this._status !== EventStatus.DRAFT) {
      throw new Error('Only draft events can be published');
    }

    if (this._startDate <= new Date()) {
      throw new Error('Cannot publish event that has already started');
    }

    this.apply(new EventPublishedEvent(
      this.id,
      this.version + 1
    ));
  }

  cancel(): void {
    if (this._status === EventStatus.CANCELLED) {
      throw new Error('Event is already cancelled');
    }

    if (this._status === EventStatus.COMPLETED) {
      throw new Error('Cannot cancel completed event');
    }

    this.apply(new EventCancelledEvent(
      this.id,
      this.version + 1
    ));
  }

  reserveSeats(seatIds: string[], customerId: string): void {
    if (this._status !== EventStatus.PUBLISHED) {
      throw new Error('Can only reserve seats for published events');
    }

    if (seatIds.length > this._availableSeats) {
      throw new Error('Not enough available seats');
    }

    // Check if any seats are already reserved
    const alreadyReserved = seatIds.filter(seatId => this._reservedSeats.has(seatId));
    if (alreadyReserved.length > 0) {
      throw new Error(`Seats already reserved: ${alreadyReserved.join(', ')}`);
    }

    this.apply(new SeatReservedEvent(
      this.id,
      seatIds,
      customerId,
      this.version + 1
    ));
  }

  releaseSeats(seatIds: string[]): void {
    const notReserved = seatIds.filter(seatId => !this._reservedSeats.has(seatId));
    if (notReserved.length > 0) {
      throw new Error(`Seats not reserved: ${notReserved.join(', ')}`);
    }

    this.apply(new SeatReleasedEvent(
      this.id,
      seatIds,
      this.version + 1
    ));
  }

  protected applyEvent(event: BaseEvent): void {
    switch (event.constructor.name) {
      case EventCreatedEvent.name:
        this.onEventCreated(event as EventCreatedEvent);
        break;
      case EventUpdatedEvent.name:
        this.onEventUpdated(event as EventUpdatedEvent);
        break;
      case EventPublishedEvent.name:
        this.onEventPublished(event as EventPublishedEvent);
        break;
      case EventCancelledEvent.name:
        this.onEventCancelled(event as EventCancelledEvent);
        break;
      case SeatReservedEvent.name:
        this.onSeatReserved(event as SeatReservedEvent);
        break;
      case SeatReleasedEvent.name:
        this.onSeatReleased(event as SeatReleasedEvent);
        break;
    }
  }

  private onEventCreated(event: EventCreatedEvent): void {
    this._title = event.eventData.title;
    this._description = event.eventData.description;
    this._category = event.eventData.category;
    this._startDate = event.eventData.startDate;
    this._endDate = event.eventData.endDate;
    this._location = event.eventData.location;
    this._venue = event.eventData.venue;
    this._address = event.eventData.address;
    this._latitude = event.eventData.latitude;
    this._longitude = event.eventData.longitude;
    this._totalSeats = event.eventData.totalSeats;
    this._availableSeats = event.eventData.totalSeats;
    this._basePrice = event.eventData.basePrice;
    this._organizerId = event.eventData.organizerId;
    this._tags = event.eventData.tags;
    this._imageUrl = event.eventData.imageUrl;
  }

  private onEventUpdated(event: EventUpdatedEvent): void {
    if (event.eventData.title) this._title = event.eventData.title;
    if (event.eventData.description !== undefined) this._description = event.eventData.description;
    if (event.eventData.category) this._category = event.eventData.category;
    if (event.eventData.startDate) this._startDate = event.eventData.startDate;
    if (event.eventData.endDate) this._endDate = event.eventData.endDate;
    if (event.eventData.location) this._location = event.eventData.location;
    if (event.eventData.venue !== undefined) this._venue = event.eventData.venue;
    if (event.eventData.address !== undefined) this._address = event.eventData.address;
    if (event.eventData.latitude !== undefined) this._latitude = event.eventData.latitude;
    if (event.eventData.longitude !== undefined) this._longitude = event.eventData.longitude;
    if (event.eventData.basePrice) this._basePrice = event.eventData.basePrice;
    if (event.eventData.tags !== undefined) this._tags = event.eventData.tags;
    if (event.eventData.imageUrl !== undefined) this._imageUrl = event.eventData.imageUrl;
  }

  private onEventPublished(event: EventPublishedEvent): void {
    this._status = EventStatus.PUBLISHED;
  }

  private onEventCancelled(event: EventCancelledEvent): void {
    this._status = EventStatus.CANCELLED;
  }

  private onSeatReserved(event: SeatReservedEvent): void {
    event.seatIds.forEach(seatId => this._reservedSeats.add(seatId));
    this._availableSeats -= event.seatIds.length;
  }

  private onSeatReleased(event: SeatReleasedEvent): void {
    event.seatIds.forEach(seatId => this._reservedSeats.delete(seatId));
    this._availableSeats += event.seatIds.length;
  }

  // Getters
  get title(): string { return this._title; }
  get description(): string | undefined { return this._description; }
  get category(): EventCategory { return this._category; }
  get startDate(): Date { return this._startDate; }
  get endDate(): Date { return this._endDate; }
  get location(): string { return this._location; }
  get venue(): string | undefined { return this._venue; }
  get address(): string | undefined { return this._address; }
  get latitude(): number | undefined { return this._latitude; }
  get longitude(): number | undefined { return this._longitude; }
  get totalSeats(): number { return this._totalSeats; }
  get availableSeats(): number { return this._availableSeats; }
  get basePrice(): number { return this._basePrice; }
  get status(): EventStatus { return this._status; }
  get organizerId(): string | undefined { return this._organizerId; }
  get tags(): string[] | undefined { return this._tags; }
  get imageUrl(): string | undefined { return this._imageUrl; }
  get reservedSeats(): string[] { return Array.from(this._reservedSeats); }
}
