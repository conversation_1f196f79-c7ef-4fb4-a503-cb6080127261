#!/bin/bash

# AWS Free Tier Deployment Script for Ticket Booking System
# This script deploys the application optimized for AWS Free Tier

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
AWS_REGION="us-east-1"
PROJECT_NAME="ticket-booking"
ENVIRONMENT="dev"

echo -e "${BLUE}🚀 Starting AWS Free Tier Deployment${NC}"
echo -e "${YELLOW}⚠️  This deployment is optimized for AWS Free Tier limits${NC}"

# Check prerequisites
echo -e "${BLUE}📋 Checking prerequisites...${NC}"

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo -e "${RED}❌ AWS CLI is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if Terraform is installed
if ! command -v terraform &> /dev/null; then
    echo -e "${RED}❌ Terraform is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed. Please install it first.${NC}"
    exit 1
fi

# Check AWS credentials
if ! aws sts get-caller-identity &> /dev/null; then
    echo -e "${RED}❌ AWS credentials not configured. Please run 'aws configure'${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Get AWS Account ID
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
echo -e "${BLUE}🔍 AWS Account ID: ${AWS_ACCOUNT_ID}${NC}"

# Step 1: Create ECR repositories
echo -e "${BLUE}📦 Creating ECR repositories...${NC}"

# Create backend repository
aws ecr describe-repositories --repository-names "${PROJECT_NAME}-backend" --region ${AWS_REGION} 2>/dev/null || \
aws ecr create-repository --repository-name "${PROJECT_NAME}-backend" --region ${AWS_REGION}

# Create frontend repository  
aws ecr describe-repositories --repository-names "${PROJECT_NAME}-frontend" --region ${AWS_REGION} 2>/dev/null || \
aws ecr create-repository --repository-name "${PROJECT_NAME}-frontend" --region ${AWS_REGION}

echo -e "${GREEN}✅ ECR repositories created${NC}"

# Step 2: Build and push Docker images
echo -e "${BLUE}🐳 Building and pushing Docker images...${NC}"

# Login to ECR
aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com

# Build and push backend
echo -e "${BLUE}🔨 Building backend image...${NC}"
cd backend
docker build -t ${PROJECT_NAME}-backend:latest --target production .
docker tag ${PROJECT_NAME}-backend:latest ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PROJECT_NAME}-backend:latest
docker push ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PROJECT_NAME}-backend:latest
cd ..

# Build and push frontend
echo -e "${BLUE}🔨 Building frontend image...${NC}"
cd frontend
docker build -t ${PROJECT_NAME}-frontend:latest --target production .
docker tag ${PROJECT_NAME}-frontend:latest ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PROJECT_NAME}-frontend:latest
docker push ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PROJECT_NAME}-frontend:latest
cd ..

echo -e "${GREEN}✅ Docker images built and pushed${NC}"

# Step 3: Create S3 bucket for Terraform state
echo -e "${BLUE}🪣 Setting up Terraform state bucket...${NC}"

BUCKET_NAME="${PROJECT_NAME}-terraform-state-${AWS_ACCOUNT_ID}"
aws s3 mb s3://${BUCKET_NAME} --region ${AWS_REGION} 2>/dev/null || echo "Bucket already exists"

# Enable versioning
aws s3api put-bucket-versioning --bucket ${BUCKET_NAME} --versioning-configuration Status=Enabled

# Create DynamoDB table for state locking
aws dynamodb describe-table --table-name "${PROJECT_NAME}-terraform-locks" --region ${AWS_REGION} 2>/dev/null || \
aws dynamodb create-table \
    --table-name "${PROJECT_NAME}-terraform-locks" \
    --attribute-definitions AttributeName=LockID,AttributeType=S \
    --key-schema AttributeName=LockID,KeyType=HASH \
    --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5 \
    --region ${AWS_REGION}

echo -e "${GREEN}✅ Terraform state backend configured${NC}"

# Step 4: Update Terraform configuration with actual values
echo -e "${BLUE}⚙️  Updating Terraform configuration...${NC}"

cd infrastructure/terraform

# Update backend configuration
cat > backend.tf << EOF
terraform {
  backend "s3" {
    bucket         = "${BUCKET_NAME}"
    key            = "infrastructure/terraform.tfstate"
    region         = "${AWS_REGION}"
    dynamodb_table = "${PROJECT_NAME}-terraform-locks"
    encrypt        = true
  }
}
EOF

# Update tfvars with actual ECR URLs
sed -i.bak "s|your-account|${AWS_ACCOUNT_ID}|g" environments/free-tier.tfvars
sed -i.bak "s|us-east-1|${AWS_REGION}|g" environments/free-tier.tfvars

echo -e "${GREEN}✅ Terraform configuration updated${NC}"

# Step 5: Deploy infrastructure
echo -e "${BLUE}🏗️  Deploying infrastructure...${NC}"

terraform init
terraform plan -var-file=environments/free-tier.tfvars
terraform apply -var-file=environments/free-tier.tfvars -auto-approve

echo -e "${GREEN}✅ Infrastructure deployed${NC}"

# Step 6: Get outputs
echo -e "${BLUE}📋 Getting deployment information...${NC}"

ALB_DNS=$(terraform output -raw alb_dns_name)
APPLICATION_URL=$(terraform output -raw application_url)

echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
echo -e "${BLUE}📍 Application URL: http://${ALB_DNS}${NC}"
echo -e "${BLUE}📍 API URL: http://${ALB_DNS}/api/v1/health${NC}"

# Step 7: Display cost warnings
echo -e "${YELLOW}💰 AWS Free Tier Cost Warnings:${NC}"
echo -e "${YELLOW}   • NAT Gateway: ~$45/month (NOT FREE)${NC}"
echo -e "${YELLOW}   • Consider removing NAT Gateway for development${NC}"
echo -e "${YELLOW}   • Monitor your AWS billing dashboard${NC}"
echo -e "${YELLOW}   • Set up billing alerts${NC}"

echo -e "${GREEN}✅ Deployment script completed${NC}"
