# Outputs for Terraform configuration

# VPC Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = module.vpc.vpc_id
}

output "public_subnet_ids" {
  description = "IDs of the public subnets"
  value       = module.vpc.public_subnet_ids
}

output "private_subnet_ids" {
  description = "IDs of the private subnets"
  value       = module.vpc.private_subnet_ids
}

# Database Outputs
output "database_endpoint" {
  description = "RDS instance endpoint"
  value       = module.database.endpoint
  sensitive   = true
}

output "database_port" {
  description = "RDS instance port"
  value       = module.database.port
}

# Redis Outputs
output "redis_endpoint" {
  description = "ElastiCache Redis endpoint"
  value       = module.redis.endpoint
  sensitive   = true
}

output "redis_port" {
  description = "ElastiCache Redis port"
  value       = module.redis.port
}

# Kafka Outputs
output "kafka_bootstrap_brokers" {
  description = "MSK bootstrap brokers"
  value       = module.kafka.bootstrap_brokers
  sensitive   = true
}

output "kafka_zookeeper_connect" {
  description = "MSK Zookeeper connection string"
  value       = module.kafka.zookeeper_connect
  sensitive   = true
}

# ECS Outputs
output "ecs_cluster_id" {
  description = "ECS cluster ID"
  value       = module.ecs.cluster_id
}

output "ecs_cluster_name" {
  description = "ECS cluster name"
  value       = module.ecs.cluster_name
}

# Load Balancer Outputs
output "alb_dns_name" {
  description = "Application Load Balancer DNS name"
  value       = module.alb.dns_name
}

output "alb_zone_id" {
  description = "Application Load Balancer zone ID"
  value       = module.alb.zone_id
}

output "alb_arn" {
  description = "Application Load Balancer ARN"
  value       = module.alb.arn
}

# Service Outputs
output "backend_service_name" {
  description = "Backend ECS service name"
  value       = module.backend_service.service_name
}

output "frontend_service_name" {
  description = "Frontend ECS service name"
  value       = module.frontend_service.service_name
}

# DNS Outputs
output "application_url" {
  description = "Application URL"
  value       = "https://${var.domain_name}"
}

output "api_url" {
  description = "API URL"
  value       = "https://api.${var.domain_name}"
}

# Security Group IDs
output "alb_security_group_id" {
  description = "ALB security group ID"
  value       = module.security_groups.alb_security_group_id
}

output "backend_security_group_id" {
  description = "Backend security group ID"
  value       = module.security_groups.backend_security_group_id
}

output "frontend_security_group_id" {
  description = "Frontend security group ID"
  value       = module.security_groups.frontend_security_group_id
}

output "database_security_group_id" {
  description = "Database security group ID"
  value       = module.security_groups.database_security_group_id
}

output "redis_security_group_id" {
  description = "Redis security group ID"
  value       = module.security_groups.redis_security_group_id
}

output "kafka_security_group_id" {
  description = "Kafka security group ID"
  value       = module.security_groups.kafka_security_group_id
}
