# Variables for Terraform configuration

variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "us-east-1"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "ticket-booking"
}

variable "domain_name" {
  description = "Domain name for the application"
  type        = string
  default     = "ticketbooking.example.com"
}

# Networking
variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

# Database
variable "db_instance_class" {
  description = "RDS instance class"
  type        = string
  default     = "db.t3.micro"
}

variable "db_name" {
  description = "Database name"
  type        = string
  default     = "ticket_booking"
}

variable "db_username" {
  description = "Database username"
  type        = string
  default     = "postgres"
}

# Redis
variable "redis_node_type" {
  description = "ElastiCache Redis node type"
  type        = string
  default     = "cache.t3.micro"
}

# Kafka
variable "kafka_instance_type" {
  description = "MSK instance type"
  type        = string
  default     = "kafka.t3.small"
}

# ECS Services
variable "backend_desired_count" {
  description = "Desired number of backend tasks"
  type        = number
  default     = 2
}

variable "frontend_desired_count" {
  description = "Desired number of frontend tasks"
  type        = number
  default     = 2
}

variable "backend_cpu" {
  description = "CPU units for backend service"
  type        = number
  default     = 512
}

variable "backend_memory" {
  description = "Memory for backend service"
  type        = number
  default     = 1024
}

variable "frontend_cpu" {
  description = "CPU units for frontend service"
  type        = number
  default     = 256
}

variable "frontend_memory" {
  description = "Memory for frontend service"
  type        = number
  default     = 512
}

# Container Images
variable "backend_image" {
  description = "Backend container image"
  type        = string
  default     = "your-account.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-backend:latest"
}

variable "frontend_image" {
  description = "Frontend container image"
  type        = string
  default     = "your-account.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-frontend:latest"
}

# SSL Certificate
variable "ssl_certificate_arn" {
  description = "ARN of SSL certificate for HTTPS"
  type        = string
  default     = ""
}

# Secrets
variable "jwt_secret" {
  description = "JWT secret for authentication"
  type        = string
  sensitive   = true
  default     = "your-super-secret-jwt-key-change-this-in-production"
}
