import { Repository } from 'typeorm';
import { EventBus } from '@nestjs/cqrs';
import { EventStore, Snapshot, SnapshotStore } from '../interfaces/event-store.interface';
import { EventStoreEntity, SnapshotEntity } from '../entities/event-store.entity';
import { BaseEvent } from '../events/base.event';
import { KafkaConfig } from '../../config/kafka.config';
export declare class EventStoreService implements EventStore, SnapshotStore {
    private eventStoreRepository;
    private snapshotRepository;
    private eventBus;
    private kafkaConfig;
    constructor(eventStoreRepository: Repository<EventStoreEntity>, snapshotRepository: Repository<SnapshotEntity>, eventBus: EventBus, kafkaConfig: KafkaConfig);
    saveEvents(aggregateId: string, events: BaseEvent[], expectedVersion: number): Promise<void>;
    getEvents(aggregateId: string, fromVersion?: number): Promise<BaseEvent[]>;
    getAllEvents(fromTimestamp?: Date): Promise<BaseEvent[]>;
    getEventsByType(eventType: string, fromTimestamp?: Date): Promise<BaseEvent[]>;
    saveSnapshot(snapshot: Snapshot): Promise<void>;
    getSnapshot(aggregateId: string): Promise<Snapshot | null>;
    private deserializeEvent;
}
