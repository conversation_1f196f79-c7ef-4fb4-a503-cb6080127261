apiVersion: apps/v1
kind: Deployment
metadata:
  name: zookeeper-deployment
  namespace: ticket-booking
  labels:
    app: zookeeper
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zookeeper
  template:
    metadata:
      labels:
        app: zookeeper
    spec:
      containers:
      - name: zookeeper
        image: confluentinc/cp-zookeeper:7.4.0
        ports:
        - containerPort: 2181
          name: zookeeper
        envFrom:
        - configMapRef:
            name: zookeeper-config
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        volumeMounts:
        - name: zookeeper-storage
          mountPath: /var/lib/zookeeper
        livenessProbe:
          exec:
            command:
            - sh
            - -c
            - "echo ruok | nc localhost 2181 | grep imok"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - sh
            - -c
            - "echo ruok | nc localhost 2181 | grep imok"
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: zookeeper-storage
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: zookeeper-service
  namespace: ticket-booking
  labels:
    app: zookeeper
spec:
  selector:
    app: zookeeper
  ports:
  - name: zookeeper
    port: 2181
    targetPort: 2181
    protocol: TCP
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: ticket-booking-zookeeper
  namespace: ticket-booking
  labels:
    app: zookeeper
spec:
  selector:
    app: zookeeper
  ports:
  - name: zookeeper
    port: 2181
    targetPort: 2181
    protocol: TCP
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafka-deployment
  namespace: ticket-booking
  labels:
    app: kafka
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kafka
  template:
    metadata:
      labels:
        app: kafka
    spec:
      containers:
      - name: kafka
        image: confluentinc/cp-kafka:7.4.0
        ports:
        - containerPort: 9092
          name: kafka
        envFrom:
        - configMapRef:
            name: kafka-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: kafka-storage
          mountPath: /var/lib/kafka
        livenessProbe:
          exec:
            command:
            - sh
            - -c
            - "kafka-broker-api-versions --bootstrap-server localhost:9092"
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - sh
            - -c
            - "kafka-broker-api-versions --bootstrap-server localhost:9092"
          initialDelaySeconds: 30
          periodSeconds: 5
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: kafka-storage
        emptyDir: {}
      # Wait for Zookeeper to be ready
      initContainers:
      - name: wait-for-zookeeper
        image: busybox:1.35
        command:
        - sh
        - -c
        - "until nc -z ticket-booking-zookeeper.ticket-booking.svc.cluster.local 2181; do echo waiting for zookeeper; sleep 2; done;"
---
apiVersion: v1
kind: Service
metadata:
  name: kafka-service
  namespace: ticket-booking
  labels:
    app: kafka
spec:
  selector:
    app: kafka
  ports:
  - name: kafka
    port: 9092
    targetPort: 9092
    protocol: TCP
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: ticket-booking-kafka
  namespace: ticket-booking
  labels:
    app: kafka
spec:
  selector:
    app: kafka
  ports:
  - name: kafka
    port: 9092
    targetPort: 9092
    protocol: TCP
  type: ClusterIP
