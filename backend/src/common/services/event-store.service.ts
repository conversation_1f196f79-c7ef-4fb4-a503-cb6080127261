import { Injectable, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventBus } from '@nestjs/cqrs';
import { EventStore, EventStoreRecord, Snapshot, SnapshotStore } from '../interfaces/event-store.interface';
import { EventStoreEntity, SnapshotEntity } from '../entities/event-store.entity';
import { BaseEvent } from '../events/base.event';
import { KafkaConfig } from '../../config/kafka.config';

@Injectable()
export class EventStoreService implements EventStore, SnapshotStore {
  constructor(
    @InjectRepository(EventStoreEntity)
    private eventStoreRepository: Repository<EventStoreEntity>,
    @InjectRepository(SnapshotEntity)
    private snapshotRepository: Repository<SnapshotEntity>,
    private eventBus: EventBus,
    private kafkaConfig: KafkaConfig,
  ) {}

  async saveEvents(aggregateId: string, events: BaseEvent[], expectedVersion: number): Promise<void> {
    const queryRunner = this.eventStoreRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Check for concurrency conflicts
      const lastEvent = await queryRunner.manager
        .createQueryBuilder(EventStoreEntity, 'event')
        .where('event.aggregateId = :aggregateId', { aggregateId })
        .orderBy('event.version', 'DESC')
        .limit(1)
        .getOne();

      const currentVersion = lastEvent ? lastEvent.version : 0;
      if (currentVersion !== expectedVersion) {
        throw new ConflictException(
          `Concurrency conflict. Expected version ${expectedVersion}, but current version is ${currentVersion}`
        );
      }

      // Save events
      for (let i = 0; i < events.length; i++) {
        const event = events[i];
        const eventEntity = new EventStoreEntity();
        eventEntity.aggregateId = aggregateId;
        eventEntity.eventId = event.eventId;
        eventEntity.eventType = event.eventType;
        eventEntity.eventData = event;
        eventEntity.eventMetadata = {
          correlationId: event.eventId,
          causationId: event.eventId,
        };
        eventEntity.version = expectedVersion + i + 1;

        await queryRunner.manager.save(eventEntity);
      }

      await queryRunner.commitTransaction();

      // Publish events to event bus and Kafka
      for (const event of events) {
        this.eventBus.publish(event);
        await this.kafkaConfig.publishEvent('domain-events', event);
      }

    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async getEvents(aggregateId: string, fromVersion?: number): Promise<BaseEvent[]> {
    const query = this.eventStoreRepository
      .createQueryBuilder('event')
      .where('event.aggregateId = :aggregateId', { aggregateId })
      .orderBy('event.version', 'ASC');

    if (fromVersion !== undefined) {
      query.andWhere('event.version > :fromVersion', { fromVersion });
    }

    const eventEntities = await query.getMany();
    return eventEntities.map(entity => this.deserializeEvent(entity));
  }

  async getAllEvents(fromTimestamp?: Date): Promise<BaseEvent[]> {
    const query = this.eventStoreRepository
      .createQueryBuilder('event')
      .orderBy('event.timestamp', 'ASC');

    if (fromTimestamp) {
      query.where('event.timestamp >= :fromTimestamp', { fromTimestamp });
    }

    const eventEntities = await query.getMany();
    return eventEntities.map(entity => this.deserializeEvent(entity));
  }

  async getEventsByType(eventType: string, fromTimestamp?: Date): Promise<BaseEvent[]> {
    const query = this.eventStoreRepository
      .createQueryBuilder('event')
      .where('event.eventType = :eventType', { eventType })
      .orderBy('event.timestamp', 'ASC');

    if (fromTimestamp) {
      query.andWhere('event.timestamp >= :fromTimestamp', { fromTimestamp });
    }

    const eventEntities = await query.getMany();
    return eventEntities.map(entity => this.deserializeEvent(entity));
  }

  async saveSnapshot(snapshot: Snapshot): Promise<void> {
    const existingSnapshot = await this.snapshotRepository.findOne({
      where: { aggregateId: snapshot.aggregateId }
    });

    if (existingSnapshot) {
      existingSnapshot.data = snapshot.data;
      existingSnapshot.version = snapshot.version;
      existingSnapshot.timestamp = snapshot.timestamp;
      await this.snapshotRepository.save(existingSnapshot);
    } else {
      const snapshotEntity = new SnapshotEntity();
      snapshotEntity.aggregateId = snapshot.aggregateId;
      snapshotEntity.aggregateType = snapshot.aggregateType;
      snapshotEntity.data = snapshot.data;
      snapshotEntity.version = snapshot.version;
      snapshotEntity.timestamp = snapshot.timestamp;
      await this.snapshotRepository.save(snapshotEntity);
    }
  }

  async getSnapshot(aggregateId: string): Promise<Snapshot | null> {
    const snapshotEntity = await this.snapshotRepository.findOne({
      where: { aggregateId }
    });

    if (!snapshotEntity) {
      return null;
    }

    return {
      aggregateId: snapshotEntity.aggregateId,
      aggregateType: snapshotEntity.aggregateType,
      data: snapshotEntity.data,
      version: snapshotEntity.version,
      timestamp: snapshotEntity.timestamp,
    };
  }

  private deserializeEvent(entity: EventStoreEntity): BaseEvent {
    // In a real implementation, you would have a proper event deserializer
    // that reconstructs the correct event type based on eventType
    return entity.eventData as BaseEvent;
  }
}
