"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventAggregate = exports.EventCategory = exports.EventStatus = void 0;
const aggregate_root_1 = require("../../../common/entities/aggregate-root");
const event_created_event_1 = require("../events/event-created.event");
const event_updated_event_1 = require("../events/event-updated.event");
const event_published_event_1 = require("../events/event-published.event");
const event_cancelled_event_1 = require("../events/event-cancelled.event");
const seat_reserved_event_1 = require("../events/seat-reserved.event");
const seat_released_event_1 = require("../events/seat-released.event");
var EventStatus;
(function (EventStatus) {
    EventStatus["DRAFT"] = "DRAFT";
    EventStatus["PUBLISHED"] = "PUBLISHED";
    EventStatus["CANCELLED"] = "CANCELLED";
    EventStatus["COMPLETED"] = "COMPLETED";
})(EventStatus || (exports.EventStatus = EventStatus = {}));
var EventCategory;
(function (EventCategory) {
    EventCategory["CONCERT"] = "CONCERT";
    EventCategory["SPORTS"] = "SPORTS";
    EventCategory["THEATER"] = "THEATER";
    EventCategory["CONFERENCE"] = "CONFERENCE";
    EventCategory["FESTIVAL"] = "FESTIVAL";
    EventCategory["OTHER"] = "OTHER";
})(EventCategory || (exports.EventCategory = EventCategory = {}));
class EventAggregate extends aggregate_root_1.AggregateRoot {
    constructor(id) {
        super(id);
        this._reservedSeats = new Set();
        this._status = EventStatus.DRAFT;
    }
    static create(eventData) {
        const event = new EventAggregate();
        event.apply(new event_created_event_1.EventCreatedEvent(event.id, eventData, event.version + 1));
        return event;
    }
    updateEvent(eventData) {
        if (this._status === EventStatus.CANCELLED) {
            throw new Error('Cannot update cancelled event');
        }
        this.apply(new event_updated_event_1.EventUpdatedEvent(this.id, eventData, this.version + 1));
    }
    publish() {
        if (this._status !== EventStatus.DRAFT) {
            throw new Error('Only draft events can be published');
        }
        if (this._startDate <= new Date()) {
            throw new Error('Cannot publish event that has already started');
        }
        this.apply(new event_published_event_1.EventPublishedEvent(this.id, this.version + 1));
    }
    cancel() {
        if (this._status === EventStatus.CANCELLED) {
            throw new Error('Event is already cancelled');
        }
        if (this._status === EventStatus.COMPLETED) {
            throw new Error('Cannot cancel completed event');
        }
        this.apply(new event_cancelled_event_1.EventCancelledEvent(this.id, this.version + 1));
    }
    reserveSeats(seatIds, customerId) {
        if (this._status !== EventStatus.PUBLISHED) {
            throw new Error('Can only reserve seats for published events');
        }
        if (seatIds.length > this._availableSeats) {
            throw new Error('Not enough available seats');
        }
        const alreadyReserved = seatIds.filter(seatId => this._reservedSeats.has(seatId));
        if (alreadyReserved.length > 0) {
            throw new Error(`Seats already reserved: ${alreadyReserved.join(', ')}`);
        }
        this.apply(new seat_reserved_event_1.SeatReservedEvent(this.id, seatIds, customerId, this.version + 1));
    }
    releaseSeats(seatIds) {
        const notReserved = seatIds.filter(seatId => !this._reservedSeats.has(seatId));
        if (notReserved.length > 0) {
            throw new Error(`Seats not reserved: ${notReserved.join(', ')}`);
        }
        this.apply(new seat_released_event_1.SeatReleasedEvent(this.id, seatIds, this.version + 1));
    }
    applyEvent(event) {
        switch (event.constructor.name) {
            case event_created_event_1.EventCreatedEvent.name:
                this.onEventCreated(event);
                break;
            case event_updated_event_1.EventUpdatedEvent.name:
                this.onEventUpdated(event);
                break;
            case event_published_event_1.EventPublishedEvent.name:
                this.onEventPublished(event);
                break;
            case event_cancelled_event_1.EventCancelledEvent.name:
                this.onEventCancelled(event);
                break;
            case seat_reserved_event_1.SeatReservedEvent.name:
                this.onSeatReserved(event);
                break;
            case seat_released_event_1.SeatReleasedEvent.name:
                this.onSeatReleased(event);
                break;
        }
    }
    onEventCreated(event) {
        this._title = event.eventData.title;
        this._description = event.eventData.description;
        this._category = event.eventData.category;
        this._startDate = event.eventData.startDate;
        this._endDate = event.eventData.endDate;
        this._location = event.eventData.location;
        this._venue = event.eventData.venue;
        this._address = event.eventData.address;
        this._latitude = event.eventData.latitude;
        this._longitude = event.eventData.longitude;
        this._totalSeats = event.eventData.totalSeats;
        this._availableSeats = event.eventData.totalSeats;
        this._basePrice = event.eventData.basePrice;
        this._organizerId = event.eventData.organizerId;
        this._tags = event.eventData.tags;
        this._imageUrl = event.eventData.imageUrl;
    }
    onEventUpdated(event) {
        if (event.eventData.title)
            this._title = event.eventData.title;
        if (event.eventData.description !== undefined)
            this._description = event.eventData.description;
        if (event.eventData.category)
            this._category = event.eventData.category;
        if (event.eventData.startDate)
            this._startDate = event.eventData.startDate;
        if (event.eventData.endDate)
            this._endDate = event.eventData.endDate;
        if (event.eventData.location)
            this._location = event.eventData.location;
        if (event.eventData.venue !== undefined)
            this._venue = event.eventData.venue;
        if (event.eventData.address !== undefined)
            this._address = event.eventData.address;
        if (event.eventData.latitude !== undefined)
            this._latitude = event.eventData.latitude;
        if (event.eventData.longitude !== undefined)
            this._longitude = event.eventData.longitude;
        if (event.eventData.basePrice)
            this._basePrice = event.eventData.basePrice;
        if (event.eventData.tags !== undefined)
            this._tags = event.eventData.tags;
        if (event.eventData.imageUrl !== undefined)
            this._imageUrl = event.eventData.imageUrl;
    }
    onEventPublished(event) {
        this._status = EventStatus.PUBLISHED;
    }
    onEventCancelled(event) {
        this._status = EventStatus.CANCELLED;
    }
    onSeatReserved(event) {
        event.seatIds.forEach(seatId => this._reservedSeats.add(seatId));
        this._availableSeats -= event.seatIds.length;
    }
    onSeatReleased(event) {
        event.seatIds.forEach(seatId => this._reservedSeats.delete(seatId));
        this._availableSeats += event.seatIds.length;
    }
    get title() { return this._title; }
    get description() { return this._description; }
    get category() { return this._category; }
    get startDate() { return this._startDate; }
    get endDate() { return this._endDate; }
    get location() { return this._location; }
    get venue() { return this._venue; }
    get address() { return this._address; }
    get latitude() { return this._latitude; }
    get longitude() { return this._longitude; }
    get totalSeats() { return this._totalSeats; }
    get availableSeats() { return this._availableSeats; }
    get basePrice() { return this._basePrice; }
    get status() { return this._status; }
    get organizerId() { return this._organizerId; }
    get tags() { return this._tags; }
    get imageUrl() { return this._imageUrl; }
    get reservedSeats() { return Array.from(this._reservedSeats); }
}
exports.EventAggregate = EventAggregate;
//# sourceMappingURL=event.aggregate.js.map