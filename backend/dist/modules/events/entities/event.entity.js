"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventEntity = exports.EventCategory = exports.EventStatus = void 0;
const typeorm_1 = require("typeorm");
var EventStatus;
(function (EventStatus) {
    EventStatus["DRAFT"] = "DRAFT";
    EventStatus["PUBLISHED"] = "PUBLISHED";
    EventStatus["CANCELLED"] = "CANCELLED";
    EventStatus["COMPLETED"] = "COMPLETED";
})(EventStatus || (exports.EventStatus = EventStatus = {}));
var EventCategory;
(function (EventCategory) {
    EventCategory["CONCERT"] = "CONCERT";
    EventCategory["SPORTS"] = "SPORTS";
    EventCategory["THEATER"] = "THEATER";
    EventCategory["CONFERENCE"] = "CONFERENCE";
    EventCategory["FESTIVAL"] = "FESTIVAL";
    EventCategory["OTHER"] = "OTHER";
})(EventCategory || (exports.EventCategory = EventCategory = {}));
let EventEntity = class EventEntity {
};
exports.EventEntity = EventEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], EventEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], EventEntity.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], EventEntity.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: EventCategory, default: EventCategory.OTHER }),
    __metadata("design:type", String)
], EventEntity.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'start_date', type: 'timestamp' }),
    __metadata("design:type", Date)
], EventEntity.prototype, "startDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'end_date', type: 'timestamp' }),
    __metadata("design:type", Date)
], EventEntity.prototype, "endDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], EventEntity.prototype, "location", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255, nullable: true }),
    __metadata("design:type", String)
], EventEntity.prototype, "venue", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], EventEntity.prototype, "address", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 6, nullable: true }),
    __metadata("design:type", Number)
], EventEntity.prototype, "latitude", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 6, nullable: true }),
    __metadata("design:type", Number)
], EventEntity.prototype, "longitude", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_seats', type: 'integer' }),
    __metadata("design:type", Number)
], EventEntity.prototype, "totalSeats", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'available_seats', type: 'integer' }),
    __metadata("design:type", Number)
], EventEntity.prototype, "availableSeats", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'base_price', type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], EventEntity.prototype, "basePrice", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: EventStatus, default: EventStatus.DRAFT }),
    __metadata("design:type", String)
], EventEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'organizer_id', nullable: true }),
    __metadata("design:type", String)
], EventEntity.prototype, "organizerId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'external_id', nullable: true, unique: true }),
    __metadata("design:type", String)
], EventEntity.prototype, "externalId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'external_source', nullable: true }),
    __metadata("design:type", String)
], EventEntity.prototype, "externalSource", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], EventEntity.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', array: true, nullable: true }),
    __metadata("design:type", Array)
], EventEntity.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'image_url', nullable: true }),
    __metadata("design:type", String)
], EventEntity.prototype, "imageUrl", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], EventEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], EventEntity.prototype, "updatedAt", void 0);
exports.EventEntity = EventEntity = __decorate([
    (0, typeorm_1.Entity)('events'),
    (0, typeorm_1.Index)(['status', 'startDate']),
    (0, typeorm_1.Index)(['category', 'startDate']),
    (0, typeorm_1.Index)(['location', 'startDate'])
], EventEntity);
//# sourceMappingURL=event.entity.js.map