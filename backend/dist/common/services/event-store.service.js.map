{"version": 3, "file": "event-store.service.js", "sourceRoot": "", "sources": ["../../../src/common/services/event-store.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA+D;AAC/D,6CAAmD;AACnD,qCAAqC;AACrC,uCAAwC;AAExC,uEAAkF;AAElF,4DAAwD;AAGjD,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAC5B,YAEU,oBAAkD,EAElD,kBAA8C,EAC9C,QAAkB,EAClB,WAAwB;QAJxB,yBAAoB,GAApB,oBAAoB,CAA8B;QAElD,uBAAkB,GAAlB,kBAAkB,CAA4B;QAC9C,aAAQ,GAAR,QAAQ,CAAU;QAClB,gBAAW,GAAX,WAAW,CAAa;IAC/B,CAAC;IAEJ,KAAK,CAAC,UAAU,CAAC,WAAmB,EAAE,MAAmB,EAAE,eAAuB;QAChF,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;QACrF,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;QAC5B,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAC;QAErC,IAAI,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,OAAO;iBACxC,kBAAkB,CAAC,qCAAgB,EAAE,OAAO,CAAC;iBAC7C,KAAK,CAAC,kCAAkC,EAAE,EAAE,WAAW,EAAE,CAAC;iBAC1D,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC;iBAChC,KAAK,CAAC,CAAC,CAAC;iBACR,MAAM,EAAE,CAAC;YAEZ,MAAM,cAAc,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,IAAI,cAAc,KAAK,eAAe,EAAE,CAAC;gBACvC,MAAM,IAAI,0BAAiB,CACzB,0CAA0C,eAAe,4BAA4B,cAAc,EAAE,CACtG,CAAC;YACJ,CAAC;YAGD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACvC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBACxB,MAAM,WAAW,GAAG,IAAI,qCAAgB,EAAE,CAAC;gBAC3C,WAAW,CAAC,WAAW,GAAG,WAAW,CAAC;gBACtC,WAAW,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;gBACpC,WAAW,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;gBACxC,WAAW,CAAC,SAAS,GAAG,KAAK,CAAC;gBAC9B,WAAW,CAAC,aAAa,GAAG;oBAC1B,aAAa,EAAE,KAAK,CAAC,OAAO;oBAC5B,WAAW,EAAE,KAAK,CAAC,OAAO;iBAC3B,CAAC;gBACF,WAAW,CAAC,OAAO,GAAG,eAAe,GAAG,CAAC,GAAG,CAAC,CAAC;gBAE9C,MAAM,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC9C,CAAC;YAED,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAC;YAGtC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBAC3B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC7B,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YAC9D,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAC;YACxC,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,MAAM,WAAW,CAAC,OAAO,EAAE,CAAC;QAC9B,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,WAAmB,EAAE,WAAoB;QACvD,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB;aACpC,kBAAkB,CAAC,OAAO,CAAC;aAC3B,KAAK,CAAC,kCAAkC,EAAE,EAAE,WAAW,EAAE,CAAC;aAC1D,OAAO,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QAEnC,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;YAC9B,KAAK,CAAC,QAAQ,CAAC,8BAA8B,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;QAClE,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;QAC5C,OAAO,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;IACpE,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,aAAoB;QACrC,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB;aACpC,kBAAkB,CAAC,OAAO,CAAC;aAC3B,OAAO,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;QAErC,IAAI,aAAa,EAAE,CAAC;YAClB,KAAK,CAAC,KAAK,CAAC,mCAAmC,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;QACtE,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;QAC5C,OAAO,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;IACpE,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,SAAiB,EAAE,aAAoB;QAC3D,MAAM,KAAK,GAAG,IAAI,CAAC,oBAAoB;aACpC,kBAAkB,CAAC,OAAO,CAAC;aAC3B,KAAK,CAAC,8BAA8B,EAAE,EAAE,SAAS,EAAE,CAAC;aACpD,OAAO,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;QAErC,IAAI,aAAa,EAAE,CAAC;YAClB,KAAK,CAAC,QAAQ,CAAC,mCAAmC,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;QACzE,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC;QAC5C,OAAO,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;IACpE,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,QAAkB;QACnC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YAC7D,KAAK,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,WAAW,EAAE;SAC7C,CAAC,CAAC;QAEH,IAAI,gBAAgB,EAAE,CAAC;YACrB,gBAAgB,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YACtC,gBAAgB,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;YAC5C,gBAAgB,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;YAChD,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACvD,CAAC;aAAM,CAAC;YACN,MAAM,cAAc,GAAG,IAAI,mCAAc,EAAE,CAAC;YAC5C,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;YAClD,cAAc,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC;YACtD,cAAc,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YACpC,cAAc,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;YAC1C,cAAc,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;YAC9C,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,WAAmB;QACnC,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;YAC3D,KAAK,EAAE,EAAE,WAAW,EAAE;SACvB,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO;YACL,WAAW,EAAE,cAAc,CAAC,WAAW;YACvC,aAAa,EAAE,cAAc,CAAC,aAAa;YAC3C,IAAI,EAAE,cAAc,CAAC,IAAI;YACzB,OAAO,EAAE,cAAc,CAAC,OAAO;YAC/B,SAAS,EAAE,cAAc,CAAC,SAAS;SACpC,CAAC;IACJ,CAAC;IAEO,gBAAgB,CAAC,MAAwB;QAG/C,OAAO,MAAM,CAAC,SAAsB,CAAC;IACvC,CAAC;CACF,CAAA;AArJY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,qCAAgB,CAAC,CAAA;IAElC,WAAA,IAAA,0BAAgB,EAAC,mCAAc,CAAC,CAAA;qCADH,oBAAU;QAEZ,oBAAU;QACpB,eAAQ;QACL,0BAAW;GAPvB,iBAAiB,CAqJ7B"}