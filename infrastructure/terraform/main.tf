# Main Terraform configuration for Online Ticket Booking System
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  
  backend "s3" {
    # Configure this with your own S3 bucket for state storage
    bucket = "ticket-booking-terraform-state"
    key    = "infrastructure/terraform.tfstate"
    region = "us-east-1"
    
    # DynamoDB table for state locking
    dynamodb_table = "ticket-booking-terraform-locks"
    encrypt        = true
  }
}

provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = {
      Project     = "ticket-booking-system"
      Environment = var.environment
      ManagedBy   = "terraform"
    }
  }
}

# Data sources
data "aws_availability_zones" "available" {
  state = "available"
}

data "aws_caller_identity" "current" {}

# Local values
locals {
  name_prefix = "${var.project_name}-${var.environment}"
  
  common_tags = {
    Project     = var.project_name
    Environment = var.environment
    ManagedBy   = "terraform"
  }
}

# VPC and Networking
module "vpc" {
  source = "./modules/vpc"
  
  name_prefix         = local.name_prefix
  vpc_cidr           = var.vpc_cidr
  availability_zones = slice(data.aws_availability_zones.available.names, 0, 3)
  
  tags = local.common_tags
}

# Security Groups
module "security_groups" {
  source = "./modules/security"
  
  name_prefix = local.name_prefix
  vpc_id      = module.vpc.vpc_id
  
  tags = local.common_tags
}

# RDS PostgreSQL Database
module "database" {
  source = "./modules/rds"
  
  name_prefix               = local.name_prefix
  vpc_id                   = module.vpc.vpc_id
  private_subnet_ids       = module.vpc.private_subnet_ids
  database_security_group_id = module.security_groups.database_security_group_id
  
  db_instance_class = var.db_instance_class
  db_name          = var.db_name
  db_username      = var.db_username
  
  tags = local.common_tags
}

# ElastiCache Redis
module "redis" {
  source = "./modules/elasticache"
  
  name_prefix               = local.name_prefix
  vpc_id                   = module.vpc.vpc_id
  private_subnet_ids       = module.vpc.private_subnet_ids
  redis_security_group_id  = module.security_groups.redis_security_group_id
  
  node_type = var.redis_node_type
  
  tags = local.common_tags
}

# Kafka Service (Containerized - Free Tier Alternative to MSK)
module "kafka_service" {
  count  = var.use_msk ? 0 : 1
  source = "./modules/ecs-service"

  name_prefix     = "${local.name_prefix}-kafka"
  cluster_id      = module.ecs.cluster_id
  cluster_name    = module.ecs.cluster_name

  # Service configuration
  service_name    = "kafka"
  container_port  = 9092
  desired_count   = 1

  # Networking
  vpc_id              = module.vpc.vpc_id
  private_subnet_ids  = module.vpc.private_subnet_ids
  security_group_ids  = [module.security_groups.kafka_security_group_id]

  # No load balancer for Kafka (internal service)
  target_group_arn = null

  # Container configuration
  container_image = "confluentinc/cp-kafka:7.4.0"
  cpu            = 256
  memory         = 512

  # Environment variables for Kafka
  environment_variables = {
    KAFKA_BROKER_ID                 = "1"
    KAFKA_ZOOKEEPER_CONNECT        = "localhost:2181"
    KAFKA_ADVERTISED_LISTENERS     = "PLAINTEXT://localhost:9092"
    KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR = "1"
    KAFKA_TRANSACTION_STATE_LOG_MIN_ISR = "1"
    KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR = "1"
    KAFKA_AUTO_CREATE_TOPICS_ENABLE = "true"
  }

  tags = local.common_tags
}

# ECS Cluster
module "ecs" {
  source = "./modules/ecs"
  
  name_prefix = local.name_prefix
  
  tags = local.common_tags
}

# Application Load Balancer
module "alb" {
  source = "./modules/alb"
  
  name_prefix            = local.name_prefix
  vpc_id                = module.vpc.vpc_id
  public_subnet_ids     = module.vpc.public_subnet_ids
  alb_security_group_id = module.security_groups.alb_security_group_id
  
  certificate_arn = var.ssl_certificate_arn
  
  tags = local.common_tags
}

# Backend Service
module "backend_service" {
  source = "./modules/ecs-service"
  
  name_prefix     = "${local.name_prefix}-backend"
  cluster_id      = module.ecs.cluster_id
  cluster_name    = module.ecs.cluster_name
  
  # Service configuration
  service_name    = "backend"
  container_port  = 3001
  desired_count   = var.backend_desired_count
  
  # Networking
  vpc_id              = module.vpc.vpc_id
  private_subnet_ids  = module.vpc.private_subnet_ids
  security_group_ids  = [module.security_groups.backend_security_group_id]
  
  # Load balancer
  target_group_arn = module.alb.backend_target_group_arn
  
  # Container configuration
  container_image = var.backend_image
  cpu            = var.backend_cpu
  memory         = var.backend_memory
  
  # Environment variables
  environment_variables = {
    NODE_ENV = var.environment
    PORT     = "3001"
    
    # Database
    DB_HOST     = module.database.endpoint
    DB_PORT     = "5432"
    DB_NAME     = var.db_name
    DB_USERNAME = var.db_username
    
    # Redis
    REDIS_HOST = module.redis.endpoint
    REDIS_PORT = "6379"
    
    # Kafka (containerized or MSK)
    KAFKA_BROKERS = var.use_msk ? module.kafka.bootstrap_brokers : "localhost:9092"
    
    # Frontend URL
    FRONTEND_URL = "https://${var.domain_name}"
  }
  
  # Secrets
  secrets = {
    DB_PASSWORD = module.database.password_secret_arn
    JWT_SECRET  = aws_secretsmanager_secret.jwt_secret.arn
  }
  
  tags = local.common_tags
}

# Frontend Service
module "frontend_service" {
  source = "./modules/ecs-service"
  
  name_prefix     = "${local.name_prefix}-frontend"
  cluster_id      = module.ecs.cluster_id
  cluster_name    = module.ecs.cluster_name
  
  # Service configuration
  service_name    = "frontend"
  container_port  = 3000
  desired_count   = var.frontend_desired_count
  
  # Networking
  vpc_id              = module.vpc.vpc_id
  private_subnet_ids  = module.vpc.private_subnet_ids
  security_group_ids  = [module.security_groups.frontend_security_group_id]
  
  # Load balancer
  target_group_arn = module.alb.frontend_target_group_arn
  
  # Container configuration
  container_image = var.frontend_image
  cpu            = var.frontend_cpu
  memory         = var.frontend_memory
  
  # Environment variables
  environment_variables = {
    NODE_ENV = var.environment
    NEXT_PUBLIC_API_URL = "https://api.${var.domain_name}"
  }
  
  tags = local.common_tags
}

# Secrets Manager
resource "aws_secretsmanager_secret" "jwt_secret" {
  name        = "${local.name_prefix}-jwt-secret"
  description = "JWT secret for authentication"
  
  tags = local.common_tags
}

resource "aws_secretsmanager_secret_version" "jwt_secret" {
  secret_id     = aws_secretsmanager_secret.jwt_secret.id
  secret_string = var.jwt_secret
}

# CloudWatch Log Groups
resource "aws_cloudwatch_log_group" "backend" {
  name              = "/ecs/${local.name_prefix}-backend"
  retention_in_days = 30
  
  tags = local.common_tags
}

resource "aws_cloudwatch_log_group" "frontend" {
  name              = "/ecs/${local.name_prefix}-frontend"
  retention_in_days = 30
  
  tags = local.common_tags
}

# Route53 DNS
module "dns" {
  source = "./modules/route53"
  
  domain_name = var.domain_name
  alb_dns_name = module.alb.dns_name
  alb_zone_id  = module.alb.zone_id
  
  tags = local.common_tags
}
