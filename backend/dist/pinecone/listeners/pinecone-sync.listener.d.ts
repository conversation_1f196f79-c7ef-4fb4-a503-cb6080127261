import { PineconeService } from '../services/pinecone.service';
export declare class PineconeSyncListener {
    private readonly pineconeService;
    private readonly logger;
    constructor(pineconeService: PineconeService);
    handleEventCreated(payload: any): Promise<void>;
    handleEventUpdated(payload: any): Promise<void>;
    handleEventDeleted(payload: any): Promise<void>;
    handleUserRegistered(payload: any): Promise<void>;
    handleUserBehaviorTracked(payload: any): Promise<void>;
    handleBookingCreated(payload: any): Promise<void>;
    handleSearchPerformed(payload: any): Promise<void>;
    handleEventViewed(payload: any): Promise<void>;
    handleRecommendationClicked(payload: any): Promise<void>;
    handleReviewCreated(payload: any): Promise<void>;
    handleFraudSuspected(payload: any): Promise<void>;
    handlePricingOptimizationRequested(payload: any): Promise<void>;
    handleScheduledAnalytics(payload: any): Promise<void>;
}
