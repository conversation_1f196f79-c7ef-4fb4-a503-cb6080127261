import formatDistance from "./_lib/formatDistance/index.js";
import formatLong from "./_lib/formatLong/index.js";
import formatRelative from "./_lib/formatRelative/index.js";
import localize from "./_lib/localize/index.js";
import match from "./_lib/match/index.js";
/**
 * @type {Locale}
 * @category Locales
 * @summary Norwegian Bokmål locale.
 * @language Norwegian Bokmål
 * @iso-639-2 nob
 * <AUTHOR> [@Hanse]{@link https://github.com/Hanse}
 * <AUTHOR> [@mikolajgrzyb]{@link https://github.com/mikolajgrzyb}
 * <AUTHOR> [@dagstuan]{@link https://github.com/dagstuan}
 */
var locale = {
  code: 'nb',
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4
  }
};
export default locale;