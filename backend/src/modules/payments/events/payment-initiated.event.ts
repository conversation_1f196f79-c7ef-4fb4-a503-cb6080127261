import { DomainEvent } from '../../../common/events/base.event';
import { PaymentDetails } from '../commands/charge-payment.command';

export class PaymentInitiatedEvent extends DomainEvent {
  constructor(
    public readonly aggregateId: string,
    public readonly bookingId: string,
    public readonly paymentDetails: PaymentDetails,
    version: number = 1
  ) {
    super(aggregateId, version);
  }
}
