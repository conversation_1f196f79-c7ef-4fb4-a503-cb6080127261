import { Injectable } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { Observable } from 'rxjs';
import { BaseEvent } from '../events/base.event';
import { BaseCommand } from '../commands/base.command';

export interface SagaStep {
  stepId: string;
  command: BaseCommand;
  compensationCommand?: BaseCommand;
  retryCount?: number;
  maxRetries?: number;
}

export enum SagaStatus {
  STARTED = 'STARTED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  COMPENSATING = 'COMPENSATING',
  COMPENSATED = 'COMPENSATED',
  CANCELLED = 'CANCELLED'
}

@Injectable()
export abstract class BaseSaga {
  protected sagaId: string;
  protected status: SagaStatus;
  protected steps: SagaStep[] = [];
  protected currentStepIndex: number = 0;
  protected completedSteps: string[] = [];
  protected failedSteps: string[] = [];

  constructor(protected commandBus: CommandBus) {
    this.sagaId = this.generateSagaId();
    this.status = SagaStatus.STARTED;
  }

  abstract handle(event: BaseEvent): Observable<BaseCommand>;

  protected async executeStep(step: SagaStep): Promise<void> {
    try {
      console.log(`Executing saga step: ${step.stepId}`);
      await this.commandBus.execute(step.command);
      this.completedSteps.push(step.stepId);
      this.currentStepIndex++;
      
      if (this.currentStepIndex >= this.steps.length) {
        this.status = SagaStatus.COMPLETED;
        console.log(`Saga ${this.sagaId} completed successfully`);
      }
    } catch (error) {
      console.error(`Saga step ${step.stepId} failed:`, error);
      this.failedSteps.push(step.stepId);
      this.status = SagaStatus.FAILED;
      await this.compensate();
    }
  }

  protected async compensate(): Promise<void> {
    this.status = SagaStatus.COMPENSATING;
    console.log(`Starting compensation for saga ${this.sagaId}`);

    // Execute compensation commands in reverse order
    for (let i = this.completedSteps.length - 1; i >= 0; i--) {
      const stepId = this.completedSteps[i];
      const step = this.steps.find(s => s.stepId === stepId);
      
      if (step?.compensationCommand) {
        try {
          console.log(`Executing compensation for step: ${stepId}`);
          await this.commandBus.execute(step.compensationCommand);
        } catch (error) {
          console.error(`Compensation failed for step ${stepId}:`, error);
          // Log compensation failure but continue with other compensations
        }
      }
    }

    this.status = SagaStatus.COMPENSATED;
    console.log(`Saga ${this.sagaId} compensation completed`);
  }

  protected addStep(step: SagaStep): void {
    this.steps.push(step);
  }

  protected async executeNextStep(): Promise<void> {
    if (this.currentStepIndex < this.steps.length && this.status === SagaStatus.IN_PROGRESS) {
      const nextStep = this.steps[this.currentStepIndex];
      await this.executeStep(nextStep);
    }
  }

  protected startSaga(): void {
    this.status = SagaStatus.IN_PROGRESS;
    console.log(`Saga ${this.sagaId} started`);
  }

  private generateSagaId(): string {
    return `saga-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Getters
  get id(): string {
    return this.sagaId;
  }

  get currentStatus(): SagaStatus {
    return this.status;
  }

  get progress(): { completed: number; total: number; percentage: number } {
    const completed = this.completedSteps.length;
    const total = this.steps.length;
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
    
    return { completed, total, percentage };
  }
}
