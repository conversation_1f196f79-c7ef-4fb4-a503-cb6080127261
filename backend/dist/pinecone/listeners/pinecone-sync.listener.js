"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PineconeSyncListener_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PineconeSyncListener = void 0;
const common_1 = require("@nestjs/common");
const event_emitter_1 = require("@nestjs/event-emitter");
const pinecone_service_1 = require("../services/pinecone.service");
let PineconeSyncListener = PineconeSyncListener_1 = class PineconeSyncListener {
    constructor(pineconeService) {
        this.pineconeService = pineconeService;
        this.logger = new common_1.Logger(PineconeSyncListener_1.name);
    }
    async handleEventCreated(payload) {
        try {
            this.logger.log(`Syncing new event to Pinecone: ${payload.eventId}`);
            const eventEmbedding = {
                eventId: payload.eventId,
                title: payload.title,
                description: payload.description,
                category: payload.category,
                venue: payload.venue,
                artist: payload.artist || '',
                date: new Date(payload.date),
                price: payload.price,
                tags: payload.tags || [],
                imageUrl: payload.imageUrl,
                audioUrl: payload.audioUrl,
            };
            await this.pineconeService.upsertEvent(eventEmbedding);
            this.logger.log(`Event ${payload.eventId} synced to Pinecone successfully`);
        }
        catch (error) {
            this.logger.error(`Failed to sync event ${payload.eventId} to Pinecone`, error);
        }
    }
    async handleEventUpdated(payload) {
        try {
            this.logger.log(`Updating event in Pinecone: ${payload.eventId}`);
            const eventEmbedding = {
                eventId: payload.eventId,
                title: payload.title,
                description: payload.description,
                category: payload.category,
                venue: payload.venue,
                artist: payload.artist || '',
                date: new Date(payload.date),
                price: payload.price,
                tags: payload.tags || [],
                imageUrl: payload.imageUrl,
                audioUrl: payload.audioUrl,
            };
            await this.pineconeService.upsertEvent(eventEmbedding);
            this.logger.log(`Event ${payload.eventId} updated in Pinecone successfully`);
        }
        catch (error) {
            this.logger.error(`Failed to update event ${payload.eventId} in Pinecone`, error);
        }
    }
    async handleEventDeleted(payload) {
        try {
            this.logger.log(`Deleting event from Pinecone: ${payload.eventId}`);
            await this.pineconeService.deleteVector('events-index', payload.eventId);
            this.logger.log(`Event ${payload.eventId} deleted from Pinecone successfully`);
        }
        catch (error) {
            this.logger.error(`Failed to delete event ${payload.eventId} from Pinecone`, error);
        }
    }
    async handleUserRegistered(payload) {
        try {
            this.logger.log(`Creating user profile in Pinecone: ${payload.userId}`);
            const userEmbedding = {
                userId: payload.userId,
                preferences: payload.preferences || [],
                behaviorHistory: [],
                demographics: {
                    ageRange: payload.ageRange,
                    location: payload.location,
                    interests: payload.interests || [],
                    spendingPattern: 'low',
                },
                purchaseHistory: [],
            };
            await this.pineconeService.upsertUser(userEmbedding);
            this.logger.log(`User ${payload.userId} profile created in Pinecone successfully`);
        }
        catch (error) {
            this.logger.error(`Failed to create user ${payload.userId} profile in Pinecone`, error);
        }
    }
    async handleUserBehaviorTracked(payload) {
        try {
            this.logger.log(`Updating user behavior in Pinecone: ${payload.userId}`);
            this.logger.log(`User ${payload.userId} performed action: ${payload.action}`);
        }
        catch (error) {
            this.logger.error(`Failed to update user ${payload.userId} behavior in Pinecone`, error);
        }
    }
    async handleBookingCreated(payload) {
        try {
            this.logger.log(`Recording purchase in user profile: ${payload.userId}`);
            const behaviorPayload = {
                userId: payload.userId,
                action: 'purchase',
                eventId: payload.eventId,
                timestamp: new Date(),
                metadata: {
                    amount: payload.amount,
                    ticketCount: payload.ticketCount,
                },
            };
            await this.handleUserBehaviorTracked(behaviorPayload);
        }
        catch (error) {
            this.logger.error(`Failed to record purchase for user ${payload.userId}`, error);
        }
    }
    async handleSearchPerformed(payload) {
        try {
            this.logger.log(`Recording search behavior: ${payload.userId}`);
            const behaviorPayload = {
                userId: payload.userId,
                action: 'search',
                query: payload.query,
                timestamp: new Date(),
                metadata: {
                    resultsCount: payload.resultsCount,
                    filters: payload.filters,
                },
            };
            await this.handleUserBehaviorTracked(behaviorPayload);
        }
        catch (error) {
            this.logger.error(`Failed to record search for user ${payload.userId}`, error);
        }
    }
    async handleEventViewed(payload) {
        try {
            this.logger.log(`Recording event view: ${payload.userId} viewed ${payload.eventId}`);
            const behaviorPayload = {
                userId: payload.userId,
                action: 'view',
                eventId: payload.eventId,
                timestamp: new Date(),
                duration: payload.duration,
                metadata: {
                    source: payload.source,
                    referrer: payload.referrer,
                },
            };
            await this.handleUserBehaviorTracked(behaviorPayload);
        }
        catch (error) {
            this.logger.error(`Failed to record event view for user ${payload.userId}`, error);
        }
    }
    async handleRecommendationClicked(payload) {
        try {
            this.logger.log(`Recording recommendation click: ${payload.userId} clicked ${payload.eventId}`);
            const behaviorPayload = {
                userId: payload.userId,
                action: 'click',
                eventId: payload.eventId,
                timestamp: new Date(),
                metadata: {
                    recommendationScore: payload.score,
                    recommendationReason: payload.reason,
                    position: payload.position,
                },
            };
            await this.handleUserBehaviorTracked(behaviorPayload);
        }
        catch (error) {
            this.logger.error(`Failed to record recommendation click for user ${payload.userId}`, error);
        }
    }
    async handleReviewCreated(payload) {
        try {
            this.logger.log(`Processing new review for content analysis: ${payload.reviewId}`);
            const moderationResult = await this.pineconeService.moderateContent({
                content: payload.content,
                contentType: 'review',
                userId: payload.userId,
                eventId: payload.eventId,
            });
            if (!moderationResult.isAppropriate) {
                this.logger.warn(`Inappropriate review detected: ${payload.reviewId}`);
            }
        }
        catch (error) {
            this.logger.error(`Failed to process review ${payload.reviewId}`, error);
        }
    }
    async handleFraudSuspected(payload) {
        try {
            this.logger.log(`Analyzing suspected fraud: ${payload.userId}`);
            const fraudResult = await this.pineconeService.detectFraud({
                userId: payload.userId,
                eventId: payload.eventId,
                purchaseAmount: payload.amount,
                userBehavior: payload.behaviorHistory || [],
                deviceInfo: payload.deviceInfo,
            });
            if (fraudResult.riskLevel === 'high') {
                this.logger.warn(`High fraud risk detected for user ${payload.userId}: ${fraudResult.riskScore}`);
            }
        }
        catch (error) {
            this.logger.error(`Failed to analyze fraud for user ${payload.userId}`, error);
        }
    }
    async handlePricingOptimizationRequested(payload) {
        try {
            this.logger.log(`Optimizing pricing for event: ${payload.eventId}`);
            const pricingResult = await this.pineconeService.optimizePricing({
                eventId: payload.eventId,
                currentPrice: payload.currentPrice,
                marketConditions: payload.marketConditions,
                demandSignals: payload.demandSignals,
            });
            this.logger.log(`Pricing optimization completed for event ${payload.eventId}: $${pricingResult.recommendedPrice}`);
        }
        catch (error) {
            this.logger.error(`Failed to optimize pricing for event ${payload.eventId}`, error);
        }
    }
    async handleScheduledAnalytics(payload) {
        try {
            this.logger.log(`Running scheduled analytics: ${payload.type}`);
            const analyticsResult = await this.pineconeService.performAnalytics({
                type: payload.type,
                timeRange: payload.timeRange,
                filters: payload.filters,
                groupBy: payload.groupBy,
            });
            this.logger.log(`Analytics completed: ${payload.type} with confidence ${analyticsResult.confidence}`);
        }
        catch (error) {
            this.logger.error(`Failed to run analytics: ${payload.type}`, error);
        }
    }
};
exports.PineconeSyncListener = PineconeSyncListener;
__decorate([
    (0, event_emitter_1.OnEvent)('event.created'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PineconeSyncListener.prototype, "handleEventCreated", null);
__decorate([
    (0, event_emitter_1.OnEvent)('event.updated'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PineconeSyncListener.prototype, "handleEventUpdated", null);
__decorate([
    (0, event_emitter_1.OnEvent)('event.deleted'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PineconeSyncListener.prototype, "handleEventDeleted", null);
__decorate([
    (0, event_emitter_1.OnEvent)('user.registered'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PineconeSyncListener.prototype, "handleUserRegistered", null);
__decorate([
    (0, event_emitter_1.OnEvent)('user.behavior.tracked'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PineconeSyncListener.prototype, "handleUserBehaviorTracked", null);
__decorate([
    (0, event_emitter_1.OnEvent)('booking.created'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PineconeSyncListener.prototype, "handleBookingCreated", null);
__decorate([
    (0, event_emitter_1.OnEvent)('search.performed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PineconeSyncListener.prototype, "handleSearchPerformed", null);
__decorate([
    (0, event_emitter_1.OnEvent)('event.viewed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PineconeSyncListener.prototype, "handleEventViewed", null);
__decorate([
    (0, event_emitter_1.OnEvent)('recommendation.clicked'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PineconeSyncListener.prototype, "handleRecommendationClicked", null);
__decorate([
    (0, event_emitter_1.OnEvent)('review.created'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PineconeSyncListener.prototype, "handleReviewCreated", null);
__decorate([
    (0, event_emitter_1.OnEvent)('fraud.suspected'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PineconeSyncListener.prototype, "handleFraudSuspected", null);
__decorate([
    (0, event_emitter_1.OnEvent)('pricing.optimization.requested'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PineconeSyncListener.prototype, "handlePricingOptimizationRequested", null);
__decorate([
    (0, event_emitter_1.OnEvent)('analytics.scheduled'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PineconeSyncListener.prototype, "handleScheduledAnalytics", null);
exports.PineconeSyncListener = PineconeSyncListener = PineconeSyncListener_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [pinecone_service_1.PineconeService])
], PineconeSyncListener);
//# sourceMappingURL=pinecone-sync.listener.js.map