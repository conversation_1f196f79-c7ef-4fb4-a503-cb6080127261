# Default values for ticket-booking Helm chart
# This is a YAML-formatted file.

# Global configuration
global:
  imageRegistry: ""
  imagePullSecrets: []
  storageClass: "gp2"

# Application configuration
app:
  name: ticket-booking
  version: "1.0.0"

# Namespace
namespace:
  create: true
  name: ticket-booking

# Backend service configuration
backend:
  enabled: true
  name: backend
  image:
    repository: your-account.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-backend
    tag: latest
    pullPolicy: Always
  
  replicaCount: 3
  
  # Auto-scaling configuration
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  # Resource configuration
  resources:
    requests:
      memory: "512Mi"
      cpu: "250m"
    limits:
      memory: "1Gi"
      cpu: "500m"
  
  # Service configuration
  service:
    type: ClusterIP
    port: 3001
    targetPort: 3001
  
  # Health checks
  livenessProbe:
    httpGet:
      path: /api/v1/health
      port: 3001
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
  
  readinessProbe:
    httpGet:
      path: /api/v1/health
      port: 3001
    initialDelaySeconds: 5
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 3
  
  # Environment variables
  env:
    NODE_ENV: production
    PORT: "3001"
  
  # Pod distribution
  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
            - key: app.kubernetes.io/name
              operator: In
              values:
              - backend
          topologyKey: kubernetes.io/hostname

# Frontend service configuration
frontend:
  enabled: true
  name: frontend
  image:
    repository: your-account.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-frontend
    tag: latest
    pullPolicy: Always
  
  replicaCount: 2
  
  # Auto-scaling configuration
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 5
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  # Resource configuration
  resources:
    requests:
      memory: "256Mi"
      cpu: "100m"
    limits:
      memory: "512Mi"
      cpu: "250m"
  
  # Service configuration
  service:
    type: ClusterIP
    port: 3000
    targetPort: 3000
  
  # Health checks
  livenessProbe:
    httpGet:
      path: /
      port: 3000
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
  
  readinessProbe:
    httpGet:
      path: /
      port: 3000
    initialDelaySeconds: 5
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 3
  
  # Environment variables
  env:
    NODE_ENV: production
  
  # Pod distribution
  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
            - key: app.kubernetes.io/name
              operator: In
              values:
              - frontend
          topologyKey: kubernetes.io/hostname

# PostgreSQL configuration
postgresql:
  enabled: true
  auth:
    postgresPassword: postgres
    username: postgres
    password: postgres
    database: ticket_booking
  
  primary:
    persistence:
      enabled: true
      size: 10Gi
      storageClass: gp2
    
    resources:
      requests:
        memory: "256Mi"
        cpu: "250m"
      limits:
        memory: "512Mi"
        cpu: "500m"

# Redis configuration
redis:
  enabled: true
  auth:
    enabled: false
  
  master:
    persistence:
      enabled: false
    
    resources:
      requests:
        memory: "128Mi"
        cpu: "100m"
      limits:
        memory: "256Mi"
        cpu: "200m"

# Kafka configuration
kafka:
  enabled: true
  replicaCount: 1
  
  persistence:
    enabled: false
  
  zookeeper:
    enabled: true
    replicaCount: 1
    persistence:
      enabled: false
  
  resources:
    requests:
      memory: "512Mi"
      cpu: "250m"
    limits:
      memory: "1Gi"
      cpu: "500m"

# Ingress configuration
ingress:
  enabled: true
  className: "alb"
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/healthcheck-path: /api/v1/health
  
  hosts:
    - host: ticket-booking.example.com
      paths:
        - path: /api
          pathType: Prefix
          service: backend
        - path: /socket.io
          pathType: Prefix
          service: backend
        - path: /
          pathType: Prefix
          service: frontend
  
  tls: []

# Monitoring configuration
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    namespace: monitoring
  
  prometheusRule:
    enabled: true
    namespace: monitoring

# Security configuration
security:
  podSecurityPolicy:
    enabled: false
  
  networkPolicy:
    enabled: false
  
  securityContext:
    runAsNonRoot: true
    runAsUser: 1001
    fsGroup: 1001

# Node affinity for worker node distribution
nodeAffinity:
  enabled: true
  requiredDuringSchedulingIgnoredDuringExecution:
    nodeSelectorTerms:
    - matchExpressions:
      - key: kubernetes.io/arch
        operator: In
        values:
        - amd64
      - key: node.kubernetes.io/instance-type
        operator: In
        values:
        - t3.medium
        - t3.large
