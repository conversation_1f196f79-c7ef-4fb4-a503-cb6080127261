import { Is<PERSON>tring, <PERSON><PERSON><PERSON>al, <PERSON>N<PERSON>ber, IsArray, IsBoolean, Min, Max, IsObject, IsEnum, IsDate } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class SearchEventsDto {
  @ApiProperty({
    description: 'Search query text',
    example: 'rock concert in New York this weekend',
  })
  @IsString()
  query: string;

  @ApiPropertyOptional({
    description: 'Number of results to return',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  topK?: number = 10;

  @ApiPropertyOptional({
    description: 'Include metadata in results',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  includeMetadata?: boolean = true;

  @ApiPropertyOptional({
    description: 'Filter criteria',
    example: { category: 'music', price: { $lte: 100 } },
  })
  @IsOptional()
  @IsObject()
  filter?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Namespace to search in',
    example: 'production',
  })
  @IsOptional()
  @IsString()
  namespace?: string;
}

export class SimilarEventsDto {
  @ApiProperty({
    description: 'Event ID to find similar events for',
    example: 'event-123',
  })
  @IsString()
  eventId: string;

  @ApiPropertyOptional({
    description: 'Number of similar events to return',
    example: 5,
    minimum: 1,
    maximum: 50,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number = 5;

  @ApiPropertyOptional({
    description: 'Minimum similarity threshold',
    example: 0.7,
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  threshold?: number = 0.7;

  @ApiPropertyOptional({
    description: 'Include metadata in results',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  includeMetadata?: boolean = true;
}

export class RecommendationsDto {
  @ApiProperty({
    description: 'User ID to get recommendations for',
    example: 'user-456',
  })
  @IsString()
  userId: string;

  @ApiPropertyOptional({
    description: 'Number of recommendations to return',
    example: 10,
    minimum: 1,
    maximum: 50,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Filter by event categories',
    example: ['music', 'sports'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  categories?: string[];

  @ApiPropertyOptional({
    description: 'Price range filter',
    example: { min: 20, max: 200 },
  })
  @IsOptional()
  @IsObject()
  priceRange?: { min: number; max: number };

  @ApiPropertyOptional({
    description: 'Date range filter',
    example: { start: '2024-01-01', end: '2024-12-31' },
  })
  @IsOptional()
  @IsObject()
  dateRange?: { start: string; end: string };

  @ApiPropertyOptional({
    description: 'Location filter',
    example: 'New York, NY',
  })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiPropertyOptional({
    description: 'Event IDs to exclude from recommendations',
    example: ['event-123', 'event-456'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  excludeEventIds?: string[];
}

export class SearchResultDto {
  @ApiProperty({
    description: 'Result ID',
    example: 'event-123',
  })
  id: string;

  @ApiProperty({
    description: 'Similarity score',
    example: 0.95,
  })
  score: number;

  @ApiPropertyOptional({
    description: 'Result metadata',
  })
  metadata?: any;
}

export class RecommendationResultDto {
  @ApiProperty({
    description: 'Event ID',
    example: 'event-123',
  })
  eventId: string;

  @ApiProperty({
    description: 'Recommendation score',
    example: 0.92,
  })
  score: number;

  @ApiProperty({
    description: 'Reason for recommendation',
    example: 'Perfect match for your preferences',
  })
  reason: string;

  @ApiProperty({
    description: 'Confidence level',
    example: 0.88,
  })
  confidence: number;

  @ApiPropertyOptional({
    description: 'Event metadata',
  })
  metadata?: any;
}

export enum UserBehaviorAction {
  VIEW = 'view',
  SEARCH = 'search',
  CLICK = 'click',
  PURCHASE = 'purchase',
  SHARE = 'share',
}

export class UserBehaviorDto {
  @ApiProperty({
    description: 'User action timestamp',
    example: '2024-01-01T12:00:00Z',
  })
  @IsDate()
  @Type(() => Date)
  timestamp: Date;

  @ApiProperty({
    description: 'Type of user action',
    enum: UserBehaviorAction,
    example: UserBehaviorAction.VIEW,
  })
  @IsEnum(UserBehaviorAction)
  action: UserBehaviorAction;

  @ApiPropertyOptional({
    description: 'Event ID if action is related to an event',
    example: 'event-123',
  })
  @IsOptional()
  @IsString()
  eventId?: string;

  @ApiPropertyOptional({
    description: 'Search query if action is search',
    example: 'rock concert',
  })
  @IsOptional()
  @IsString()
  query?: string;

  @ApiPropertyOptional({
    description: 'Duration of action in milliseconds',
    example: 30000,
  })
  @IsOptional()
  @IsNumber()
  duration?: number;

  @ApiPropertyOptional({
    description: 'Additional metadata',
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class FraudDetectionDto {
  @ApiProperty({
    description: 'User ID',
    example: 'user-456',
  })
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'Event ID',
    example: 'event-123',
  })
  @IsString()
  eventId: string;

  @ApiProperty({
    description: 'Purchase amount',
    example: 150.00,
  })
  @IsNumber()
  purchaseAmount: number;

  @ApiProperty({
    description: 'User behavior history',
    type: [UserBehaviorDto],
  })
  @IsArray()
  @Type(() => UserBehaviorDto)
  userBehavior: UserBehaviorDto[];

  @ApiPropertyOptional({
    description: 'Device information',
    example: { userAgent: 'Mozilla/5.0...', ip: '***********' },
  })
  @IsOptional()
  @IsObject()
  deviceInfo?: Record<string, any>;
}

export enum ContentType {
  REVIEW = 'review',
  COMMENT = 'comment',
  DESCRIPTION = 'description',
}

export class ContentModerationDto {
  @ApiProperty({
    description: 'Content to moderate',
    example: 'This event was amazing!',
  })
  @IsString()
  content: string;

  @ApiProperty({
    description: 'Type of content',
    enum: ContentType,
    example: ContentType.REVIEW,
  })
  @IsEnum(ContentType)
  contentType: ContentType;

  @ApiProperty({
    description: 'User ID who created the content',
    example: 'user-456',
  })
  @IsString()
  userId: string;

  @ApiPropertyOptional({
    description: 'Event ID if content is related to an event',
    example: 'event-123',
  })
  @IsOptional()
  @IsString()
  eventId?: string;
}

export class FraudDetectionResultDto {
  @ApiProperty({
    description: 'Risk score (0-100)',
    example: 25,
  })
  riskScore: number;

  @ApiProperty({
    description: 'Risk level',
    example: 'LOW',
  })
  riskLevel: string;

  @ApiProperty({
    description: 'Detected risk factors',
    example: ['unusual_purchase_amount', 'new_device'],
  })
  riskFactors: string[];

  @ApiProperty({
    description: 'Recommended action',
    example: 'ALLOW',
  })
  recommendedAction: string;

  @ApiProperty({
    description: 'Confidence in the assessment',
    example: 0.85,
  })
  confidence: number;

  @ApiPropertyOptional({
    description: 'Additional details',
  })
  details?: any;
}

export class ContentModerationResultDto {
  @ApiProperty({
    description: 'Whether content is appropriate',
    example: true,
  })
  isAppropriate: boolean;

  @ApiProperty({
    description: 'Toxicity score (0-1)',
    example: 0.15,
  })
  toxicityScore: number;

  @ApiProperty({
    description: 'Content sentiment',
    example: 'POSITIVE',
  })
  sentiment: string;

  @ApiProperty({
    description: 'Detected categories',
    example: ['positive_feedback', 'event_review'],
  })
  categories: string[];

  @ApiProperty({
    description: 'Confidence in moderation decision',
    example: 0.92,
  })
  confidence: number;

  @ApiPropertyOptional({
    description: 'Suggested actions',
    example: ['approve'],
  })
  suggestedActions?: string[];

  @ApiPropertyOptional({
    description: 'Additional analysis details',
  })
  details?: any;
}
