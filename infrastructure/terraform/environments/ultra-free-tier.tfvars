# Ultra Free Tier Configuration
# This configuration removes NAT Gateway to stay completely within free tier
# WARNING: Private subnets won't have internet access

# Basic Configuration
aws_region   = "us-east-1"
environment  = "dev"
project_name = "ticket-booking"
domain_name  = "your-domain.com"

# Networking - Public subnets only (no NAT Gateway cost)
vpc_cidr = "10.0.0.0/16"

# Database - Free Tier
db_instance_class = "db.t3.micro"
db_name          = "ticket_booking"
db_username      = "postgres"

# Redis - Free Tier
redis_node_type = "cache.t2.micro"

# Kafka - Containerized (no MSK cost)
use_msk = false

# ECS Services - Minimal configuration
backend_desired_count  = 1
frontend_desired_count = 1

# Ultra-minimal resource allocation
backend_cpu    = 256
backend_memory = 512
frontend_cpu   = 256
frontend_memory = 512

# Container Images
backend_image  = "your-account.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-backend:latest"
frontend_image = "your-account.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-frontend:latest"

# No SSL (saves ACM costs)
ssl_certificate_arn = ""

# Secrets
jwt_secret = "your-super-secret-jwt-key-change-this-in-production"

# Cost Optimization Notes:
# =======================
# 1. No NAT Gateway = $0 (saves ~$45/month)
# 2. All services in public subnets with security groups
# 3. Single AZ deployment to minimize data transfer
# 4. Minimal resource allocation
# 5. No enhanced monitoring or encryption (not free)
#
# Expected Monthly Cost: $0-5 (within free tier)
# 
# Trade-offs:
# - Less secure (public subnets)
# - No private network isolation
# - Single point of failure
# - Good for development/testing only
