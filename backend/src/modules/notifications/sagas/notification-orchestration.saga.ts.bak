import { Injectable } from '@nestjs/common';
import { Saga, ICommand, ofType } from '@nestjs/cqrs';
import { Observable } from 'rxjs';
import { map, delay, mergeMap } from 'rxjs/operators';

import { BaseSaga } from '../../../common/sagas/base.saga';
import { SendNotificationCommand, NotificationType } from '../../bookings/commands/send-notification.command';

// Notification events
export class NotificationRequestedEvent {
  constructor(
    public readonly notificationId: string,
    public readonly recipient: string,
    public readonly type: NotificationType,
    public readonly subject: string,
    public readonly message: string,
    public readonly templateData?: any,
    public readonly priority: 'low' | 'normal' | 'high' | 'urgent' = 'normal',
    public readonly scheduledFor?: Date
  ) {}
}

export class NotificationSentEvent {
  constructor(
    public readonly notificationId: string,
    public readonly recipient: string,
    public readonly type: NotificationType,
    public readonly sentAt: Date,
    public readonly deliveryId?: string
  ) {}
}

export class NotificationFailedEvent {
  constructor(
    public readonly notificationId: string,
    public readonly recipient: string,
    public readonly type: NotificationType,
    public readonly error: string,
    public readonly retryCount: number
  ) {}
}

// Notification commands
export class SendEmailCommand {
  constructor(
    public readonly notificationId: string,
    public readonly to: string,
    public readonly subject: string,
    public readonly htmlContent: string,
    public readonly textContent: string,
    public readonly attachments?: any[]
  ) {}
}

export class SendSMSCommand {
  constructor(
    public readonly notificationId: string,
    public readonly to: string,
    public readonly message: string
  ) {}
}

export class SendPushNotificationCommand {
  constructor(
    public readonly notificationId: string,
    public readonly userId: string,
    public readonly title: string,
    public readonly body: string,
    public readonly data?: any
  ) {}
}

export class RenderTemplateCommand {
  constructor(
    public readonly templateName: string,
    public readonly templateData: any,
    public readonly outputFormat: 'html' | 'text' | 'pdf'
  ) {}
}

export class LogNotificationCommand {
  constructor(
    public readonly notificationId: string,
    public readonly status: 'sent' | 'failed' | 'pending',
    public readonly details: any
  ) {}
}

@Injectable()
export class NotificationOrchestrationSaga extends BaseSaga {

  @Saga()
  notificationRequested = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType(NotificationRequestedEvent),
      mergeMap((event: NotificationRequestedEvent) => {
        console.log(`📧 Notification Saga: Processing ${event.type} notification for ${event.recipient}`);
        
        const commands: ICommand[] = [];

        // Step 1: Log notification request
        this.addStep({
          stepId: 'log-notification-request',
          command: new LogNotificationCommand(
            event.notificationId,
            'pending',
            {
              recipient: event.recipient,
              type: event.type,
              subject: event.subject,
              priority: event.priority,
              requestedAt: new Date()
            }
          )
        });

        // Step 2: Handle different notification types
        switch (event.type) {
          case NotificationType.EMAIL:
            // Render email template
            this.addStep({
              stepId: 'render-email-template',
              command: new RenderTemplateCommand(
                this.getEmailTemplate(event.subject),
                event.templateData,
                'html'
              ),
              maxRetries: 2
            });

            // Send email
            this.addStep({
              stepId: 'send-email',
              command: new SendEmailCommand(
                event.notificationId,
                event.recipient,
                event.subject,
                event.message, // This would be replaced with rendered template
                event.message, // Text version
                event.templateData?.attachments
              ),
              maxRetries: 3
            });
            break;

          case NotificationType.SMS:
            this.addStep({
              stepId: 'send-sms',
              command: new SendSMSCommand(
                event.notificationId,
                event.recipient,
                event.message
              ),
              maxRetries: 3
            });
            break;

          case NotificationType.PUSH:
            this.addStep({
              stepId: 'send-push-notification',
              command: new SendPushNotificationCommand(
                event.notificationId,
                event.recipient,
                event.subject,
                event.message,
                event.templateData
              ),
              maxRetries: 3
            });
            break;
        }

        // Step 3: Log final status
        this.addStep({
          stepId: 'log-notification-completion',
          command: new LogNotificationCommand(
            event.notificationId,
            'sent',
            {
              completedAt: new Date(),
              type: event.type
            }
          )
        });

        this.startSaga();
        return new Observable(observer => {
          observer.next(this.steps[0].command);
          observer.complete();
        });
      })
    );
  };

  @Saga()
  notificationFailed = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType(NotificationFailedEvent),
      map((event: NotificationFailedEvent) => {
        console.log(`❌ Notification Saga: Failed to send ${event.type} to ${event.recipient}`);
        
        // Implement retry logic based on failure type and count
        if (event.retryCount < 3) {
          // Retry with exponential backoff
          const retryDelay = Math.pow(2, event.retryCount) * 1000; // 1s, 2s, 4s
          
          return new Observable(observer => {
            setTimeout(() => {
              observer.next(new SendNotificationCommand(
                event.recipient,
                event.type,
                'Retry: Notification',
                'Retrying failed notification',
                { retryCount: event.retryCount + 1 }
              ));
              observer.complete();
            }, retryDelay);
          });
        } else {
          // Max retries reached, log failure and potentially send to dead letter queue
          return new LogNotificationCommand(
            event.notificationId,
            'failed',
            {
              finalError: event.error,
              maxRetriesReached: true,
              failedAt: new Date()
            }
          );
        }
      })
    );
  };

  // Bulk notification workflow
  @Saga()
  bulkNotificationRequested = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType('BulkNotificationRequestedEvent'),
      mergeMap((event: any) => {
        console.log(`📬 Notification Saga: Processing bulk notification for ${event.recipients.length} recipients`);
        
        // Process in batches to avoid overwhelming the system
        const batchSize = 50;
        const batches = this.chunkArray(event.recipients, batchSize);
        
        return new Observable(observer => {
          batches.forEach((batch, index) => {
            setTimeout(() => {
              batch.forEach(recipient => {
                observer.next(new SendNotificationCommand(
                  recipient,
                  event.type,
                  event.subject,
                  event.message,
                  event.templateData
                ));
              });
              
              if (index === batches.length - 1) {
                observer.complete();
              }
            }, index * 1000); // 1 second delay between batches
          });
        });
      })
    );
  };

  // Scheduled notification workflow
  @Saga()
  scheduledNotificationDue = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType('ScheduledNotificationDueEvent'),
      map((event: any) => {
        console.log(`⏰ Notification Saga: Processing scheduled notification ${event.notificationId}`);
        
        return new SendNotificationCommand(
          event.recipient,
          event.type,
          event.subject,
          event.message,
          event.templateData
        );
      })
    );
  };

  // Reminder workflow (for booking expiration, event reminders, etc.)
  @Saga()
  reminderRequested = (events$: Observable<any>): Observable<ICommand> => {
    return events$.pipe(
      ofType('ReminderRequestedEvent'),
      map((event: any) => {
        console.log(`⏰ Notification Saga: Setting up reminder for ${event.reminderType}`);
        
        const reminderTimes = this.calculateReminderTimes(event.reminderType, event.targetDate);
        
        return new Observable(observer => {
          reminderTimes.forEach((reminderTime, index) => {
            const delay = reminderTime.getTime() - new Date().getTime();
            
            if (delay > 0) {
              setTimeout(() => {
                observer.next(new SendNotificationCommand(
                  event.recipient,
                  NotificationType.EMAIL,
                  event.subject,
                  event.message,
                  { ...event.templateData, reminderNumber: index + 1 }
                ));
              }, delay);
            }
          });
          
          observer.complete();
        });
      })
    );
  };

  // Helper methods
  private getEmailTemplate(subject: string): string {
    // Map subjects to template names
    const templateMap: Record<string, string> = {
      'Booking Confirmed': 'booking-confirmation',
      'Payment Successful': 'payment-success',
      'Event Cancelled': 'event-cancellation',
      'Seats Reserved': 'seat-reservation',
      'Booking Expired': 'booking-expiration'
    };
    
    return templateMap[subject] || 'default';
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  private calculateReminderTimes(reminderType: string, targetDate: Date): Date[] {
    const reminders: Date[] = [];
    const target = new Date(targetDate);
    
    switch (reminderType) {
      case 'booking-expiration':
        // 10 minutes, 5 minutes, 1 minute before expiration
        reminders.push(new Date(target.getTime() - 10 * 60 * 1000));
        reminders.push(new Date(target.getTime() - 5 * 60 * 1000));
        reminders.push(new Date(target.getTime() - 1 * 60 * 1000));
        break;
      case 'event-reminder':
        // 1 week, 1 day, 1 hour before event
        reminders.push(new Date(target.getTime() - 7 * 24 * 60 * 60 * 1000));
        reminders.push(new Date(target.getTime() - 24 * 60 * 60 * 1000));
        reminders.push(new Date(target.getTime() - 60 * 60 * 1000));
        break;
    }
    
    return reminders.filter(date => date > new Date());
  }

  // Override handle method for custom orchestration
  handle(event: any): Observable<ICommand> {
    switch (event.constructor.name) {
      case NotificationRequestedEvent.name:
        return this.notificationRequested(new Observable(observer => observer.next(event)));
      case NotificationFailedEvent.name:
        return this.notificationFailed(new Observable(observer => observer.next(event)));
      case 'BulkNotificationRequestedEvent':
        return this.bulkNotificationRequested(new Observable(observer => observer.next(event)));
      case 'ScheduledNotificationDueEvent':
        return this.scheduledNotificationDue(new Observable(observer => observer.next(event)));
      case 'ReminderRequestedEvent':
        return this.reminderRequested(new Observable(observer => observer.next(event)));
      default:
        return new Observable(observer => observer.complete());
    }
  }
}
