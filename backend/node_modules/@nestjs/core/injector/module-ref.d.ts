import { IntrospectionResult, Type } from '@nestjs/common';
import { AbstractInstanceResolver } from './abstract-instance-resolver';
import { NestContainer } from './container';
import { Injector } from './injector';
import { InstanceLinksHost } from './instance-links-host';
import { ContextId } from './instance-wrapper';
import { Module } from './module';
export interface ModuleRefGetOrResolveOpts {
    /**
     * If enabled, lookup will only be performed in the host module.
     * @default true
     */
    strict?: boolean;
    /**
     * If enabled, instead of returning a first instance registered under a given token,
     * a list of instances will be returned.
     * @default false
     */
    each?: boolean;
}
export declare abstract class ModuleRef extends AbstractInstanceResolver {
    protected readonly container: NestContainer;
    protected readonly injector: Injector;
    private _instanceLinksHost;
    protected get instanceLinksHost(): InstanceLinksHost;
    constructor(container: NestContainer);
    /**
     * Retrieves an instance of either injectable or controller, otherwise, throws exception.
     * @returns {TResult}
     */
    abstract get<TInput = any, TResult = TInput>(typeOrToken: Type<TInput> | Function | string | symbol): TResult;
    /**
     * Retrieves an instance of either injectable or controller, otherwise, throws exception.
     * @returns {TResult}
     */
    abstract get<TInput = any, TResult = TInput>(typeOrToken: Type<TInput> | Function | string | symbol, options: {
        /**
         * If enabled, lookup will only be performed in the host module.
         * @default true
         */
        strict?: boolean;
        /** This indicates that only the first instance registered will be returned. */
        each?: undefined | false;
    }): TResult;
    /**
     * Retrieves a list of instances of either injectables or controllers, otherwise, throws exception.
     * @returns {Array<TResult>}
     */
    abstract get<TInput = any, TResult = TInput>(typeOrToken: Type<TInput> | Function | string | symbol, options: {
        /**
         * If enabled, lookup will only be performed in the host module.
         * @default true
         */
        strict?: boolean;
        /** This indicates that a list of instances will be returned. */
        each: true;
    }): Array<TResult>;
    /**
     * Retrieves an instance (or a list of instances) of either injectable or controller, otherwise, throws exception.
     * @returns {TResult | Array<TResult>}
     */
    abstract get<TInput = any, TResult = TInput>(typeOrToken: Type<TInput> | Function | string | symbol, options?: ModuleRefGetOrResolveOpts): TResult | Array<TResult>;
    /**
     * Resolves transient or request-scoped instance of either injectable or controller, otherwise, throws exception.
     * @returns {Array<TResult>}
     */
    abstract resolve<TInput = any, TResult = TInput>(typeOrToken: Type<TInput> | Function | string | symbol): Promise<TResult>;
    /**
     * Resolves transient or request-scoped instance of either injectable or controller, otherwise, throws exception.
     * @returns {Array<TResult>}
     */
    abstract resolve<TInput = any, TResult = TInput>(typeOrToken: Type<TInput> | Function | string | symbol, contextId?: {
        id: number;
    }): Promise<TResult>;
    /**
     * Resolves transient or request-scoped instance of either injectable or controller, otherwise, throws exception.
     * @returns {Array<TResult>}
     */
    abstract resolve<TInput = any, TResult = TInput>(typeOrToken: Type<TInput> | Function | string | symbol, contextId?: {
        id: number;
    }, options?: {
        strict?: boolean;
        each?: undefined | false;
    }): Promise<TResult>;
    /**
     * Resolves transient or request-scoped instances of either injectables or controllers, otherwise, throws exception.
     * @returns {Array<TResult>}
     */
    abstract resolve<TInput = any, TResult = TInput>(typeOrToken: Type<TInput> | Function | string | symbol, contextId?: {
        id: number;
    }, options?: {
        strict?: boolean;
        each: true;
    }): Promise<Array<TResult>>;
    /**
     * Resolves transient or request-scoped instance (or a list of instances) of either injectable or controller, otherwise, throws exception.
     * @returns {Promise<TResult | Array<TResult>>}
     */
    abstract resolve<TInput = any, TResult = TInput>(typeOrToken: Type<TInput> | Function | string | symbol, contextId?: {
        id: number;
    }, options?: ModuleRefGetOrResolveOpts): Promise<TResult | Array<TResult>>;
    abstract create<T = any>(type: Type<T>, contextId?: ContextId): Promise<T>;
    introspect<T = any>(token: Type<T> | string | symbol): IntrospectionResult;
    registerRequestByContextId<T = any>(request: T, contextId: ContextId): void;
    protected instantiateClass<T = any>(type: Type<T>, moduleRef: Module, contextId?: ContextId): Promise<T>;
}
