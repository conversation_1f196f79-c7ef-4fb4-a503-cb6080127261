"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessPaymentCommand = void 0;
const base_command_1 = require("../../../common/commands/base.command");
class ProcessPaymentCommand extends base_command_1.Command {
    constructor(bookingId, paymentData) {
        super();
        this.bookingId = bookingId;
        this.paymentData = paymentData;
    }
}
exports.ProcessPaymentCommand = ProcessPaymentCommand;
//# sourceMappingURL=process-payment.command.js.map