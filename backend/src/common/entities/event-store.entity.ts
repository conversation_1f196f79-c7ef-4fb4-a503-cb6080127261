import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, Index } from 'typeorm';

@Entity('event_store')
@Index(['aggregateId', 'version'])
@Index(['eventType', 'timestamp'])
export class EventStoreEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'aggregate_id' })
  @Index()
  aggregateId: string;

  @Column({ name: 'event_id', unique: true })
  eventId: string;

  @Column({ name: 'event_type' })
  eventType: string;

  @Column({ name: 'event_data', type: 'jsonb' })
  eventData: any;

  @Column({ name: 'event_metadata', type: 'jsonb', nullable: true })
  eventMetadata: any;

  @Column({ type: 'integer' })
  version: number;

  @CreateDateColumn({ name: 'timestamp' })
  timestamp: Date;
}

@Entity('snapshots')
@Index(['aggregateId'])
export class SnapshotEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'aggregate_id', unique: true })
  aggregateId: string;

  @Column({ name: 'aggregate_type' })
  aggregateType: string;

  @Column({ type: 'jsonb' })
  data: any;

  @Column({ type: 'integer' })
  version: number;

  @CreateDateColumn()
  timestamp: Date;
}
