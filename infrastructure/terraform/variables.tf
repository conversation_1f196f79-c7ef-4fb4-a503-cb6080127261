# Variables for Terraform configuration

variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "us-east-1"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "ticket-booking"
}

variable "domain_name" {
  description = "Domain name for the application"
  type        = string
  default     = "ticketbooking.example.com"
}

# Networking
variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

# Database - Free Tier Eligible
variable "db_instance_class" {
  description = "RDS instance class (Free Tier: db.t3.micro)"
  type        = string
  default     = "db.t3.micro"
}

variable "db_name" {
  description = "Database name"
  type        = string
  default     = "ticket_booking"
}

variable "db_username" {
  description = "Database username"
  type        = string
  default     = "postgres"
}

# Redis - Free Tier Eligible
variable "redis_node_type" {
  description = "ElastiCache Redis node type (Free Tier: cache.t2.micro)"
  type        = string
  default     = "cache.t2.micro"
}

# Note: MSK is NOT free tier eligible, we'll use local Kafka in containers instead
variable "use_msk" {
  description = "Use MSK (not free tier) or containerized Kafka"
  type        = bool
  default     = false
}

# ECS Services - Free Tier Optimized
variable "backend_desired_count" {
  description = "Desired number of backend tasks (Free Tier: 1)"
  type        = number
  default     = 1
}

variable "frontend_desired_count" {
  description = "Desired number of frontend tasks (Free Tier: 1)"
  type        = number
  default     = 1
}

variable "backend_cpu" {
  description = "CPU units for backend service (Free Tier: 256)"
  type        = number
  default     = 256
}

variable "backend_memory" {
  description = "Memory for backend service (Free Tier: 512)"
  type        = number
  default     = 512
}

variable "frontend_cpu" {
  description = "CPU units for frontend service (Free Tier: 256)"
  type        = number
  default     = 256
}

variable "frontend_memory" {
  description = "Memory for frontend service (Free Tier: 512)"
  type        = number
  default     = 512
}

# Container Images
variable "backend_image" {
  description = "Backend container image"
  type        = string
  default     = "your-account.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-backend:latest"
}

variable "frontend_image" {
  description = "Frontend container image"
  type        = string
  default     = "your-account.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-frontend:latest"
}

# SSL Certificate
variable "ssl_certificate_arn" {
  description = "ARN of SSL certificate for HTTPS"
  type        = string
  default     = ""
}

# Secrets
variable "jwt_secret" {
  description = "JWT secret for authentication"
  type        = string
  sensitive   = true
  default     = "your-super-secret-jwt-key-change-this-in-production"
}
