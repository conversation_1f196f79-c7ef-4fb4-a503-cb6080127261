"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentInitiatedEvent = void 0;
const base_event_1 = require("../../../common/events/base.event");
class PaymentInitiatedEvent extends base_event_1.DomainEvent {
    constructor(aggregateId, bookingId, paymentDetails, version = 1) {
        super(aggregateId, version);
        this.aggregateId = aggregateId;
        this.bookingId = bookingId;
        this.paymentDetails = paymentDetails;
    }
}
exports.PaymentInitiatedEvent = PaymentInitiatedEvent;
//# sourceMappingURL=payment-initiated.event.js.map