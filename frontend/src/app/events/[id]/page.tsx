'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { 
  CalendarIcon, 
  MapPinIcon, 
  TicketIcon,
  UsersIcon,
  ClockIcon,
  ShareIcon,
  HeartIcon
} from '@heroicons/react/24/outline'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { eventsAPI, bookingsAPI } from '@/lib/api'
import { useAuthStore } from '@/lib/store'
import { formatDate, formatPrice, formatCategory } from '@/lib/utils'
import toast from 'react-hot-toast'

export default function EventDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const { isAuthenticated } = useAuthStore()
  const [event, setEvent] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [ticketCount, setTicketCount] = useState(1)
  const [isBooking, setIsBooking] = useState(false)

  useEffect(() => {
    if (params.id) {
      fetchEvent()
    }
  }, [params.id])

  const fetchEvent = async () => {
    try {
      const response = await eventsAPI.getById(params.id as string)
      setEvent(response.data)
    } catch (error) {
      console.error('Failed to fetch event:', error)
      toast.error('Event not found')
      router.push('/events')
    } finally {
      setIsLoading(false)
    }
  }

  const handleBooking = async () => {
    if (!isAuthenticated) {
      toast.error('Please sign in to book tickets')
      router.push('/login')
      return
    }

    if (ticketCount > event.availableSeats) {
      toast.error('Not enough seats available')
      return
    }

    setIsBooking(true)
    try {
      await bookingsAPI.create({
        eventId: event.id,
        ticketCount
      })
      
      toast.success(`Successfully booked ${ticketCount} ticket(s)!`)
      router.push('/my-bookings')
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Booking failed')
    } finally {
      setIsBooking(false)
    }
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: event.title,
        text: event.description,
        url: window.location.href,
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
      toast.success('Link copied to clipboard!')
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <div className="h-64 bg-gray-200 rounded mb-6"></div>
              <div className="h-32 bg-gray-200 rounded"></div>
            </div>
            <div className="h-96 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (!event) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-2xl font-bold mb-4">Event Not Found</h1>
        <Button asChild>
          <a href="/events">Browse Events</a>
        </Button>
      </div>
    )
  }

  const categoryVariant = event.category as 'music' | 'theater' | 'comedy' | 'sports'
  const isLowAvailability = event.availableSeats < event.totalSeats * 0.2
  const totalPrice = event.price * ticketCount

  return (
    <div className="container mx-auto px-4 py-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-2 mb-4">
            <Badge variant={categoryVariant}>
              {formatCategory(event.category)}
            </Badge>
            {isLowAvailability && (
              <Badge variant="destructive">Limited Availability</Badge>
            )}
          </div>
          
          <h1 className="text-3xl lg:text-4xl font-bold mb-4">{event.title}</h1>
          
          <div className="flex flex-wrap items-center gap-6 text-muted-foreground">
            <div className="flex items-center gap-2">
              <CalendarIcon className="h-5 w-5" />
              {formatDate(event.date)}
            </div>
            <div className="flex items-center gap-2">
              <MapPinIcon className="h-5 w-5" />
              {event.venue}
            </div>
            <div className="flex items-center gap-2">
              <UsersIcon className="h-5 w-5" />
              {event.availableSeats} of {event.totalSeats} available
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Event Image Placeholder */}
            <Card>
              <CardContent className="p-0">
                <div className="h-64 bg-gradient-to-br from-primary/20 to-purple-500/20 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <TicketIcon className="h-16 w-16 mx-auto text-primary mb-4" />
                    <p className="text-lg font-semibold">{event.title}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Description */}
            <Card>
              <CardHeader>
                <CardTitle>About This Event</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  {event.description}
                </p>
              </CardContent>
            </Card>

            {/* Event Details */}
            <Card>
              <CardHeader>
                <CardTitle>Event Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold mb-2">Date & Time</h4>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <ClockIcon className="h-4 w-4" />
                      {formatDate(event.date)}
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Venue</h4>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <MapPinIcon className="h-4 w-4" />
                      {event.venue}
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Category</h4>
                    <Badge variant={categoryVariant}>
                      {formatCategory(event.category)}
                    </Badge>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Price</h4>
                    <div className="text-2xl font-bold text-primary">
                      {formatPrice(event.price)}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Booking Sidebar */}
          <div className="space-y-6">
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Book Tickets</span>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="icon" onClick={handleShare}>
                      <ShareIcon className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <HeartIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Number of Tickets
                  </label>
                  <Input
                    type="number"
                    min="1"
                    max={Math.min(event.availableSeats, 10)}
                    value={ticketCount}
                    onChange={(e) => setTicketCount(parseInt(e.target.value) || 1)}
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Price per ticket:</span>
                    <span>{formatPrice(event.price)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Quantity:</span>
                    <span>{ticketCount}</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between font-bold text-lg">
                      <span>Total:</span>
                      <span className="text-primary">{formatPrice(totalPrice)}</span>
                    </div>
                  </div>
                </div>

                <Button 
                  className="w-full" 
                  size="lg"
                  onClick={handleBooking}
                  disabled={isBooking || event.availableSeats === 0}
                >
                  {isBooking ? 'Booking...' : 
                   event.availableSeats === 0 ? 'Sold Out' : 
                   'Book Now'}
                </Button>

                {!isAuthenticated && (
                  <p className="text-sm text-muted-foreground text-center">
                    <a href="/login" className="text-primary hover:underline">
                      Sign in
                    </a> to book tickets
                  </p>
                )}

                <div className="text-center">
                  <div className="text-sm text-muted-foreground mb-2">
                    Availability
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        isLowAvailability 
                          ? 'bg-red-500' 
                          : event.availableSeats / event.totalSeats > 0.5 
                            ? 'bg-green-500' 
                            : 'bg-yellow-500'
                      }`}
                      style={{ width: `${(event.availableSeats / event.totalSeats) * 100}%` }}
                    />
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">
                    {event.availableSeats} of {event.totalSeats} seats available
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
