import { DomainEvent } from '../../../common/events/base.event';

export class PaymentSucceededEvent extends DomainEvent {
  constructor(
    public readonly aggregateId: string,
    public readonly bookingId: string,
    public readonly transactionId: string,
    public readonly amount: number,
    public readonly currency: string,
    version: number = 1
  ) {
    super(aggregateId, version);
  }
}
