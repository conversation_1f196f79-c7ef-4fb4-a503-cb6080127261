import { ColumnType } from "../../driver/types/ColumnTypes";
import { VirtualColumnOptions } from "../options/VirtualColumnOptions";
/**
 * VirtualColumn decorator is used to mark a specific class property as a Virtual column.
 */
export declare function VirtualColumn(options: VirtualColumnOptions): PropertyDecorator;
/**
 * VirtualColumn decorator is used to mark a specific class property as a Virtual column.
 */
export declare function VirtualColumn(typeOrOptions: ColumnType, options: VirtualColumnOptions): PropertyDecorator;
