# 🚀 Kubernetes Scaling Architecture

## 🏗️ **Cluster Architecture: 1 Master + 3 Worker Nodes**

### **EKS Cluster Configuration**
```
┌─────────────────────────────────────────────────────────────┐
│                    EKS Control Plane                        │
│                   (1 Master - Managed)                      │
└─────────────────────────────────────────────────────────────┘
                              │
                    ┌─────────┼─────────┐
                    │                   │
        ┌───────────▼────────┐ ┌────────▼────────┐ ┌────────▼────────┐
        │   Worker Group 1   │ │   Worker Group 2 │ │   Worker Group 3 │
        │   (1-2 nodes)      │ │   (1-2 nodes)    │ │   (1-2 nodes)    │
        │   t3.medium        │ │   t3.medium      │ │   t3.medium      │
        │   SPOT instances   │ │   SPOT instances  │ │   SPOT instances  │
        └────────────────────┘ └─────────────────┘ └─────────────────┘
```

### **Pod Distribution Strategy**
- **Backend Pods**: Distributed across all 3 worker groups
- **Frontend Pods**: Distributed across worker groups
- **Database**: Pinned to specific worker group for data locality
- **Cache/Queue**: Distributed for high availability

## 📊 **Auto-Scaling Configuration**

### **Cluster Auto-Scaler**
```yaml
# Each worker group scales independently
Worker Group 1: 1-2 nodes (Backend heavy)
Worker Group 2: 1-2 nodes (Frontend heavy)  
Worker Group 3: 1-2 nodes (Infrastructure)
Total Capacity: 3-6 worker nodes
```

### **Horizontal Pod Auto-Scaler (HPA)**
```yaml
Backend Service:
  Min Replicas: 2
  Max Replicas: 10
  CPU Target: 70%
  Memory Target: 80%

Frontend Service:
  Min Replicas: 2
  Max Replicas: 5
  CPU Target: 70%
  Memory Target: 80%
```

### **Vertical Pod Auto-Scaler (VPA)**
```yaml
# Automatically adjusts resource requests/limits
Backend:
  CPU: 250m-500m (auto-adjusted)
  Memory: 512Mi-1Gi (auto-adjusted)

Frontend:
  CPU: 100m-250m (auto-adjusted)
  Memory: 256Mi-512Mi (auto-adjusted)
```

## 🚀 **Deployment Options**

### **Option 1: Terraform + kubectl (Recommended)**
```bash
# Deploy EKS infrastructure
cd infrastructure/kubernetes/terraform-eks
terraform apply

# Deploy applications
./scripts/deploy-kubernetes.sh
```

### **Option 2: Helm Chart Deployment**
```bash
# Install with Helm
helm install ticket-booking infrastructure/kubernetes/helm-charts/ticket-booking \
  --namespace ticket-booking \
  --create-namespace \
  --values infrastructure/kubernetes/helm-charts/ticket-booking/values.yaml
```

### **Option 3: Manual kubectl**
```bash
# Apply manifests manually
kubectl apply -f infrastructure/kubernetes/manifests/
```

## 📈 **Scaling Scenarios**

### **Traffic Spike Handling**
```bash
# Scenario: 10x traffic increase
1. HPA detects high CPU/memory usage
2. Scales backend pods: 2 → 8 replicas
3. Scales frontend pods: 2 → 4 replicas
4. Cluster autoscaler adds nodes if needed
5. Load balancer distributes traffic
```

### **Cost Optimization During Low Traffic**
```bash
# Scenario: Low traffic periods
1. HPA scales down pods: 8 → 2 replicas
2. Cluster autoscaler removes unused nodes
3. SPOT instances reduce costs by 70%
4. Maintains minimum availability
```

### **Database Scaling**
```bash
# PostgreSQL scaling options:
1. Vertical scaling: Increase pod resources
2. Read replicas: Add read-only instances
3. Connection pooling: PgBouncer sidecar
4. Sharding: Multiple database instances
```

## 🔧 **Scaling Commands**

### **Manual Scaling**
```bash
# Scale backend manually
kubectl scale deployment backend-deployment --replicas=5 -n ticket-booking

# Scale frontend manually
kubectl scale deployment frontend-deployment --replicas=3 -n ticket-booking

# Scale node groups
aws eks update-nodegroup-config \
  --cluster-name ticket-booking-dev-eks-cluster \
  --nodegroup-name worker-group-1 \
  --scaling-config minSize=2,maxSize=4,desiredSize=3
```

### **Auto-Scaling Configuration**
```bash
# Update HPA settings
kubectl patch hpa backend-hpa -n ticket-booking -p '{"spec":{"maxReplicas":15}}'

# Update resource requests (triggers VPA)
kubectl patch deployment backend-deployment -n ticket-booking -p \
  '{"spec":{"template":{"spec":{"containers":[{"name":"backend","resources":{"requests":{"cpu":"500m","memory":"1Gi"}}}]}}}}'
```

### **Monitoring Scaling**
```bash
# Check HPA status
kubectl get hpa -n ticket-booking

# Check node utilization
kubectl top nodes

# Check pod distribution
kubectl get pods -n ticket-booking -o wide

# Check cluster autoscaler logs
kubectl logs -f deployment/cluster-autoscaler -n kube-system
```

## 📊 **Performance Metrics**

### **Scaling Metrics**
```yaml
Response Time Targets:
  - API calls: < 200ms (95th percentile)
  - Page loads: < 2s (95th percentile)
  - Database queries: < 100ms (average)

Throughput Targets:
  - API requests: 1000 RPS per backend pod
  - Concurrent users: 500 per frontend pod
  - Database connections: 100 per backend pod

Availability Targets:
  - Service uptime: 99.9%
  - Pod restart time: < 30s
  - Node replacement time: < 5min
```

### **Resource Utilization**
```yaml
Optimal Utilization:
  - CPU: 60-70% (allows burst capacity)
  - Memory: 70-80% (prevents OOM kills)
  - Network: < 80% bandwidth
  - Storage: < 85% capacity
```

## 🛡️ **High Availability Features**

### **Pod Distribution**
```yaml
# Anti-affinity rules ensure pods spread across nodes
podAntiAffinity:
  preferredDuringSchedulingIgnoredDuringExecution:
  - weight: 100
    podAffinityTerm:
      labelSelector:
        matchExpressions:
        - key: app
          operator: In
          values: [backend]
      topologyKey: kubernetes.io/hostname
```

### **Node Failure Handling**
```yaml
# Automatic pod rescheduling
1. Node failure detected (30s)
2. Pods marked for eviction (5min)
3. New pods scheduled on healthy nodes
4. Load balancer updates endpoints
5. Traffic routes to healthy pods
```

### **Rolling Updates**
```yaml
# Zero-downtime deployments
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxUnavailable: 25%
    maxSurge: 25%
```

## 💰 **Cost Optimization**

### **SPOT Instance Strategy**
```yaml
# 70% cost savings with SPOT instances
capacity_type = "SPOT"
mixed_instances_policy:
  instances_distribution:
    on_demand_percentage: 20
    spot_allocation_strategy: "diversified"
```

### **Resource Right-Sizing**
```bash
# Use VPA recommendations
kubectl get vpa -n ticket-booking
kubectl describe vpa backend-vpa -n ticket-booking

# Implement recommendations
kubectl patch deployment backend-deployment -n ticket-booking \
  --patch "$(kubectl get vpa backend-vpa -n ticket-booking -o jsonpath='{.status.recommendation.containerRecommendations[0]}')"
```

## 🔍 **Monitoring & Observability**

### **Prometheus Metrics**
```yaml
# Custom metrics for scaling decisions
- ticket_booking_requests_per_second
- ticket_booking_active_users
- ticket_booking_queue_length
- ticket_booking_database_connections
```

### **Grafana Dashboards**
```yaml
# Scaling dashboards
1. Cluster Overview: Nodes, pods, resources
2. Application Performance: Response times, throughput
3. Auto-scaling: HPA/VPA/CA activity
4. Cost Analysis: Resource usage, SPOT savings
```

### **Alerting Rules**
```yaml
# Scaling alerts
- High CPU/Memory usage (> 80%)
- Pod restart rate (> 5/hour)
- Node failure detection
- Scaling events (scale up/down)
```

## 🎯 **Best Practices**

### **Scaling Strategy**
1. **Start Small**: Begin with minimum replicas
2. **Monitor Closely**: Watch metrics during scaling
3. **Test Thoroughly**: Load test scaling scenarios
4. **Plan Capacity**: Understand peak traffic patterns
5. **Cost Control**: Set resource limits and budgets

### **Performance Optimization**
1. **Resource Requests**: Set accurate CPU/memory requests
2. **Health Checks**: Configure proper liveness/readiness probes
3. **Connection Pooling**: Use database connection pools
4. **Caching**: Implement Redis caching effectively
5. **Load Testing**: Regular performance testing

### **Operational Excellence**
1. **Monitoring**: Comprehensive metrics and alerting
2. **Logging**: Centralized log aggregation
3. **Backup**: Regular database and configuration backups
4. **Security**: Network policies and pod security
5. **Documentation**: Keep scaling procedures updated

## 🚀 **Getting Started**

```bash
# 1. Deploy the cluster
./scripts/deploy-kubernetes.sh

# 2. Monitor scaling
kubectl get hpa -n ticket-booking -w

# 3. Test scaling
kubectl run -i --tty load-test --image=busybox --restart=Never -- \
  wget -q -O- http://backend-service.ticket-booking.svc.cluster.local:3001/api/v1/health

# 4. Check results
kubectl top pods -n ticket-booking
```

Your Kubernetes cluster is now ready for enterprise-scale traffic with automatic scaling across 1 master and 3 worker nodes! 🎉
