import { ICommand } from '@nestjs/cqrs';
import { Observable } from 'rxjs';
import { BaseSaga } from '../../../common/sagas/base.saga';
import { NotificationType } from '../../bookings/commands/send-notification.command';
export declare class NotificationRequestedEvent {
    readonly notificationId: string;
    readonly recipient: string;
    readonly type: NotificationType;
    readonly subject: string;
    readonly message: string;
    readonly templateData?: any;
    readonly priority: 'low' | 'normal' | 'high' | 'urgent';
    readonly scheduledFor?: Date;
    constructor(notificationId: string, recipient: string, type: NotificationType, subject: string, message: string, templateData?: any, priority?: 'low' | 'normal' | 'high' | 'urgent', scheduledFor?: Date);
}
export declare class NotificationSentEvent {
    readonly notificationId: string;
    readonly recipient: string;
    readonly type: NotificationType;
    readonly sentAt: Date;
    readonly deliveryId?: string;
    constructor(notificationId: string, recipient: string, type: NotificationType, sentAt: Date, deliveryId?: string);
}
export declare class NotificationFailedEvent {
    readonly notificationId: string;
    readonly recipient: string;
    readonly type: NotificationType;
    readonly error: string;
    readonly retryCount: number;
    constructor(notificationId: string, recipient: string, type: NotificationType, error: string, retryCount: number);
}
export declare class SendEmailCommand {
    readonly notificationId: string;
    readonly to: string;
    readonly subject: string;
    readonly htmlContent: string;
    readonly textContent: string;
    readonly attachments?: any[];
    constructor(notificationId: string, to: string, subject: string, htmlContent: string, textContent: string, attachments?: any[]);
}
export declare class SendSMSCommand {
    readonly notificationId: string;
    readonly to: string;
    readonly message: string;
    constructor(notificationId: string, to: string, message: string);
}
export declare class SendPushNotificationCommand {
    readonly notificationId: string;
    readonly userId: string;
    readonly title: string;
    readonly body: string;
    readonly data?: any;
    constructor(notificationId: string, userId: string, title: string, body: string, data?: any);
}
export declare class RenderTemplateCommand {
    readonly templateName: string;
    readonly templateData: any;
    readonly outputFormat: 'html' | 'text' | 'pdf';
    constructor(templateName: string, templateData: any, outputFormat: 'html' | 'text' | 'pdf');
}
export declare class LogNotificationCommand {
    readonly notificationId: string;
    readonly status: 'sent' | 'failed' | 'pending';
    readonly details: any;
    constructor(notificationId: string, status: 'sent' | 'failed' | 'pending', details: any);
}
export declare class NotificationOrchestrationSaga extends BaseSaga {
    notificationRequested: (events$: Observable<any>) => Observable<ICommand>;
    notificationFailed: (events$: Observable<any>) => Observable<ICommand>;
    bulkNotificationRequested: (events$: Observable<any>) => Observable<ICommand>;
    scheduledNotificationDue: (events$: Observable<any>) => Observable<ICommand>;
    reminderRequested: (events$: Observable<any>) => Observable<ICommand>;
    private getEmailTemplate;
    private chunkArray;
    private calculateReminderTimes;
    handle(event: any): Observable<ICommand>;
}
