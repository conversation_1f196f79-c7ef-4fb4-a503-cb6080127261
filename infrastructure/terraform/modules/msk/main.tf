# MSK (Managed Streaming for Kafka) Module

# MSK Configuration
resource "aws_msk_configuration" "main" {
  kafka_versions = ["3.4.0"]
  name           = "${var.name_prefix}-msk-config"

  server_properties = <<PROPERTIES
auto.create.topics.enable=true
default.replication.factor=2
min.insync.replicas=2
num.partitions=3
log.retention.hours=168
log.retention.bytes=1073741824
log.segment.bytes=1073741824
PROPERTIES

  description = "MSK configuration for ${var.name_prefix}"
}

# CloudWatch Log Group for MSK
resource "aws_cloudwatch_log_group" "msk" {
  name              = "/aws/msk/${var.name_prefix}"
  retention_in_days = 7

  tags = var.tags
}

# MSK Cluster
resource "aws_msk_cluster" "main" {
  cluster_name           = "${var.name_prefix}-kafka"
  kafka_version          = "3.4.0"
  number_of_broker_nodes = 3

  broker_node_group_info {
    instance_type   = var.instance_type
    client_subnets  = var.private_subnet_ids
    security_groups = [var.kafka_security_group_id]
    
    storage_info {
      ebs_storage_info {
        volume_size = 100
      }
    }
  }

  configuration_info {
    arn      = aws_msk_configuration.main.arn
    revision = aws_msk_configuration.main.latest_revision
  }

  encryption_info {
    encryption_at_rest_kms_key_id = aws_kms_key.msk.arn
    encryption_in_transit {
      client_broker = "TLS"
      in_cluster    = true
    }
  }

  client_authentication {
    sasl {
      scram = true
    }
    tls {
      certificate_authority_arns = []
    }
  }

  logging_info {
    broker_logs {
      cloudwatch_logs {
        enabled   = true
        log_group = aws_cloudwatch_log_group.msk.name
      }
      firehose {
        enabled = false
      }
      s3 {
        enabled = false
      }
    }
  }

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-kafka"
  })
}

# KMS Key for MSK encryption
resource "aws_kms_key" "msk" {
  description             = "KMS key for MSK encryption"
  deletion_window_in_days = 7

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-msk-kms-key"
  })
}

resource "aws_kms_alias" "msk" {
  name          = "alias/${var.name_prefix}-msk"
  target_key_id = aws_kms_key.msk.key_id
}

# MSK SCRAM Secret
resource "random_password" "kafka_user_password" {
  length  = 16
  special = true
}

resource "aws_secretsmanager_secret" "kafka_user" {
  name        = "AmazonMSK_${var.name_prefix}_kafka_user"
  description = "Kafka user credentials for ${var.name_prefix}"
  kms_key_id  = aws_kms_key.msk.arn

  tags = var.tags
}

resource "aws_secretsmanager_secret_version" "kafka_user" {
  secret_id = aws_secretsmanager_secret.kafka_user.id
  secret_string = jsonencode({
    username = "kafka-user"
    password = random_password.kafka_user_password.result
  })
}

# Associate the secret with MSK cluster
resource "aws_msk_scram_secret_association" "main" {
  cluster_arn     = aws_msk_cluster.main.arn
  secret_arn_list = [aws_secretsmanager_secret.kafka_user.arn]

  depends_on = [aws_secretsmanager_secret_version.kafka_user]
}

# CloudWatch Alarms for MSK
resource "aws_cloudwatch_metric_alarm" "kafka_cpu" {
  alarm_name          = "${var.name_prefix}-kafka-cpu-utilization"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "CpuUser"
  namespace           = "AWS/Kafka"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "This metric monitors kafka cpu utilization"
  alarm_actions       = []

  dimensions = {
    "Cluster Name" = aws_msk_cluster.main.cluster_name
  }

  tags = var.tags
}

resource "aws_cloudwatch_metric_alarm" "kafka_disk" {
  alarm_name          = "${var.name_prefix}-kafka-disk-utilization"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "KafkaDataLogsDiskUsed"
  namespace           = "AWS/Kafka"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "This metric monitors kafka disk utilization"
  alarm_actions       = []

  dimensions = {
    "Cluster Name" = aws_msk_cluster.main.cluster_name
  }

  tags = var.tags
}
