# Variables for EKS Kubernetes Deployment

variable "aws_region" {
  description = "AWS region for EKS cluster"
  type        = string
  default     = "us-east-1"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "ticket-booking"
}

# Networking
variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "single_nat_gateway" {
  description = "Use single NAT gateway for cost optimization"
  type        = bool
  default     = true
}

# EKS Configuration
variable "kubernetes_version" {
  description = "Kubernetes version for EKS cluster"
  type        = string
  default     = "1.28"
}

variable "worker_instance_type" {
  description = "EC2 instance type for worker nodes"
  type        = string
  default     = "t3.medium"
}

variable "capacity_type" {
  description = "Type of capacity associated with the EKS Node Group. Valid values: ON_DEMAND, SPOT"
  type        = string
  default     = "SPOT" # Cost optimization
}

# Node Group Configuration
variable "min_nodes_per_group" {
  description = "Minimum number of nodes per node group"
  type        = number
  default     = 1
}

variable "max_nodes_per_group" {
  description = "Maximum number of nodes per node group"
  type        = number
  default     = 2
}

variable "desired_nodes_per_group" {
  description = "Desired number of nodes per node group"
  type        = number
  default     = 1
}

# Application Configuration
variable "enable_monitoring" {
  description = "Enable monitoring stack (Prometheus, Grafana)"
  type        = bool
  default     = true
}

variable "enable_logging" {
  description = "Enable centralized logging (ELK stack)"
  type        = bool
  default     = true
}

variable "enable_ingress_nginx" {
  description = "Enable NGINX Ingress Controller"
  type        = bool
  default     = true
}

# Database Configuration (RDS)
variable "db_instance_class" {
  description = "RDS instance class"
  type        = string
  default     = "db.t3.micro"
}

variable "db_allocated_storage" {
  description = "RDS allocated storage in GB"
  type        = number
  default     = 20
}

# Redis Configuration
variable "redis_node_type" {
  description = "ElastiCache Redis node type"
  type        = string
  default     = "cache.t3.micro"
}

# Container Images
variable "backend_image" {
  description = "Backend container image"
  type        = string
  default     = "your-account.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-backend:latest"
}

variable "frontend_image" {
  description = "Frontend container image"
  type        = string
  default     = "your-account.dkr.ecr.us-east-1.amazonaws.com/ticket-booking-frontend:latest"
}

variable "kafka_image" {
  description = "Kafka container image"
  type        = string
  default     = "confluentinc/cp-kafka:7.4.0"
}

# Scaling Configuration
variable "backend_min_replicas" {
  description = "Minimum number of backend replicas"
  type        = number
  default     = 2
}

variable "backend_max_replicas" {
  description = "Maximum number of backend replicas"
  type        = number
  default     = 10
}

variable "frontend_min_replicas" {
  description = "Minimum number of frontend replicas"
  type        = number
  default     = 2
}

variable "frontend_max_replicas" {
  description = "Maximum number of frontend replicas"
  type        = number
  default     = 5
}

# Resource Limits
variable "backend_cpu_request" {
  description = "Backend CPU request"
  type        = string
  default     = "250m"
}

variable "backend_memory_request" {
  description = "Backend memory request"
  type        = string
  default     = "512Mi"
}

variable "backend_cpu_limit" {
  description = "Backend CPU limit"
  type        = string
  default     = "500m"
}

variable "backend_memory_limit" {
  description = "Backend memory limit"
  type        = string
  default     = "1Gi"
}

variable "frontend_cpu_request" {
  description = "Frontend CPU request"
  type        = string
  default     = "100m"
}

variable "frontend_memory_request" {
  description = "Frontend memory request"
  type        = string
  default     = "256Mi"
}

variable "frontend_cpu_limit" {
  description = "Frontend CPU limit"
  type        = string
  default     = "250m"
}

variable "frontend_memory_limit" {
  description = "Frontend memory limit"
  type        = string
  default     = "512Mi"
}
