import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

export enum EventStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  CANCELLED = 'CANCELLED',
  COMPLETED = 'COMPLETED'
}

export enum EventCategory {
  CONCERT = 'CONCERT',
  SPORTS = 'SPORTS',
  THEATER = 'THEATER',
  CONFERENCE = 'CONFERENCE',
  FESTIVAL = 'FESTIVAL',
  OTHER = 'OTHER'
}

@Entity('events')
@Index(['status', 'startDate'])
@Index(['category', 'startDate'])
@Index(['location', 'startDate'])
export class EventEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 255 })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'enum', enum: EventCategory, default: EventCategory.OTHER })
  category: EventCategory;

  @Column({ name: 'start_date', type: 'timestamp' })
  startDate: Date;

  @Column({ name: 'end_date', type: 'timestamp' })
  endDate: Date;

  @Column({ length: 255 })
  location: string;

  @Column({ length: 255, nullable: true })
  venue: string;

  @Column({ type: 'text', nullable: true })
  address: string;

  @Column({ type: 'decimal', precision: 10, scale: 6, nullable: true })
  latitude: number;

  @Column({ type: 'decimal', precision: 10, scale: 6, nullable: true })
  longitude: number;

  @Column({ name: 'total_seats', type: 'integer' })
  totalSeats: number;

  @Column({ name: 'available_seats', type: 'integer' })
  availableSeats: number;

  @Column({ name: 'base_price', type: 'decimal', precision: 10, scale: 2 })
  basePrice: number;

  @Column({ type: 'enum', enum: EventStatus, default: EventStatus.DRAFT })
  status: EventStatus;

  @Column({ name: 'organizer_id', nullable: true })
  organizerId: string;

  @Column({ name: 'external_id', nullable: true, unique: true })
  externalId: string; // For external API integration

  @Column({ name: 'external_source', nullable: true })
  externalSource: string; // e.g., 'ticketmaster', 'eventbrite'

  @Column({ type: 'jsonb', nullable: true })
  metadata: any; // Additional event metadata

  @Column({ type: 'text', array: true, nullable: true })
  tags: string[];

  @Column({ name: 'image_url', nullable: true })
  imageUrl: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
