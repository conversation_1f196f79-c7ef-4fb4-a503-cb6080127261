import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ThrottlerModule } from '@nestjs/throttler';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { CqrsModule } from '@nestjs/cqrs';

import { DatabaseConfig } from './config/database.config';
import { RedisConfig } from './config/redis.config';
import { KafkaConfig } from './config/kafka.config';
import { EventStoreEntity, SnapshotEntity } from './common/entities/event-store.entity';
import { EventStoreService } from './common/services/event-store.service';
import { PineconeModule } from './pinecone/pinecone.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // Database
    TypeOrmModule.forRootAsync({
      useClass: DatabaseConfig,
    }),

    // Event Store entities
    TypeOrmModule.forFeature([EventStoreEntity, SnapshotEntity]),

    // Rate Limiting
    ThrottlerModule.forRoot([
      {
        ttl: 60000, // 1 minute
        limit: 100, // 100 requests per minute
      },
    ]),

    // Event Emitter for domain events
    EventEmitterModule.forRoot(),

    // CQRS
    CqrsModule,

    // AI & Vector Search
    PineconeModule,

    // Feature modules will be added here
  ],
  controllers: [AppController],
  providers: [
    AppService,
    DatabaseConfig,
    RedisConfig,
    KafkaConfig,
    EventStoreService,
  ],
})
export class AppModule {}
