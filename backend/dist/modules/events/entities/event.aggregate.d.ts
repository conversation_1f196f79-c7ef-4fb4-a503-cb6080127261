import { AggregateRoot } from '../../../common/entities/aggregate-root';
import { BaseEvent } from '../../../common/events/base.event';
export declare enum EventStatus {
    DRAFT = "DRAFT",
    PUBLISHED = "PUBLISHED",
    CANCELLED = "CANCELLED",
    COMPLETED = "COMPLETED"
}
export declare enum EventCategory {
    CONCERT = "CONCERT",
    SPORTS = "SPORTS",
    THEATER = "THEATER",
    CONFERENCE = "CONFERENCE",
    FESTIVAL = "FESTIVAL",
    OTHER = "OTHER"
}
export interface EventData {
    title: string;
    description?: string;
    category: EventCategory;
    startDate: Date;
    endDate: Date;
    location: string;
    venue?: string;
    address?: string;
    latitude?: number;
    longitude?: number;
    totalSeats: number;
    basePrice: number;
    organizerId?: string;
    tags?: string[];
    imageUrl?: string;
}
export declare class EventAggregate extends AggregateRoot {
    private _title;
    private _description?;
    private _category;
    private _startDate;
    private _endDate;
    private _location;
    private _venue?;
    private _address?;
    private _latitude?;
    private _longitude?;
    private _totalSeats;
    private _availableSeats;
    private _basePrice;
    private _status;
    private _organizerId?;
    private _tags?;
    private _imageUrl?;
    private _reservedSeats;
    constructor(id?: string);
    static create(eventData: EventData): EventAggregate;
    updateEvent(eventData: Partial<EventData>): void;
    publish(): void;
    cancel(): void;
    reserveSeats(seatIds: string[], customerId: string): void;
    releaseSeats(seatIds: string[]): void;
    protected applyEvent(event: BaseEvent): void;
    private onEventCreated;
    private onEventUpdated;
    private onEventPublished;
    private onEventCancelled;
    private onSeatReserved;
    private onSeatReleased;
    get title(): string;
    get description(): string | undefined;
    get category(): EventCategory;
    get startDate(): Date;
    get endDate(): Date;
    get location(): string;
    get venue(): string | undefined;
    get address(): string | undefined;
    get latitude(): number | undefined;
    get longitude(): number | undefined;
    get totalSeats(): number;
    get availableSeats(): number;
    get basePrice(): number;
    get status(): EventStatus;
    get organizerId(): string | undefined;
    get tags(): string[] | undefined;
    get imageUrl(): string | undefined;
    get reservedSeats(): string[];
}
