"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KafkaConfig = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const kafkajs_1 = require("kafkajs");
let KafkaConfig = class KafkaConfig {
    constructor(configService) {
        this.configService = configService;
        this.kafka = new kafkajs_1.Kafka({
            clientId: this.configService.get('KAFKA_CLIENT_ID', 'ticket-booking-backend'),
            brokers: this.configService.get('KAFKA_BROKERS', 'localhost:9092').split(','),
            retry: {
                initialRetryTime: 100,
                retries: 8,
            },
        });
        this.producer = this.kafka.producer({
            maxInFlightRequests: 1,
            idempotent: true,
            transactionTimeout: 30000,
        });
        this.consumer = this.kafka.consumer({
            groupId: this.configService.get('KAFKA_GROUP_ID', 'ticket-booking-group'),
        });
    }
    async onModuleInit() {
        await this.producer.connect();
        await this.consumer.connect();
        console.log('✅ Kafka connected successfully');
    }
    async onModuleDestroy() {
        await this.producer.disconnect();
        await this.consumer.disconnect();
        console.log('🔌 Kafka disconnected');
    }
    async publishEvent(topic, message) {
        try {
            await this.producer.send({
                topic,
                messages: [
                    {
                        key: message.aggregateId || message.id,
                        value: JSON.stringify(message),
                        timestamp: Date.now().toString(),
                    },
                ],
            });
        }
        catch (error) {
            console.error('Failed to publish event to Kafka:', error);
            throw error;
        }
    }
    async subscribe(topic, callback) {
        await this.consumer.subscribe({ topic, fromBeginning: false });
        await this.consumer.run({
            eachMessage: async ({ topic, partition, message }) => {
                try {
                    await callback(message);
                }
                catch (error) {
                    console.error(`Error processing message from topic ${topic}:`, error);
                }
            },
        });
    }
    async createTopics(topics) {
        const admin = this.kafka.admin();
        await admin.connect();
        try {
            await admin.createTopics({
                topics: topics.map(topic => ({
                    topic,
                    numPartitions: 3,
                    replicationFactor: 1,
                })),
            });
            console.log(`✅ Kafka topics created: ${topics.join(', ')}`);
        }
        catch (error) {
            console.error('Failed to create Kafka topics:', error);
        }
        finally {
            await admin.disconnect();
        }
    }
};
exports.KafkaConfig = KafkaConfig;
exports.KafkaConfig = KafkaConfig = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], KafkaConfig);
//# sourceMappingURL=kafka.config.js.map