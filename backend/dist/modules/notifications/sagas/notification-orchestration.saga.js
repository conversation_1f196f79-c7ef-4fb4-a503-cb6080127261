"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationOrchestrationSaga = exports.LogNotificationCommand = exports.RenderTemplateCommand = exports.SendPushNotificationCommand = exports.SendSMSCommand = exports.SendEmailCommand = exports.NotificationFailedEvent = exports.NotificationSentEvent = exports.NotificationRequestedEvent = void 0;
const common_1 = require("@nestjs/common");
const cqrs_1 = require("@nestjs/cqrs");
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
const base_saga_1 = require("../../../common/sagas/base.saga");
const send_notification_command_1 = require("../../bookings/commands/send-notification.command");
class NotificationRequestedEvent {
    constructor(notificationId, recipient, type, subject, message, templateData, priority = 'normal', scheduledFor) {
        this.notificationId = notificationId;
        this.recipient = recipient;
        this.type = type;
        this.subject = subject;
        this.message = message;
        this.templateData = templateData;
        this.priority = priority;
        this.scheduledFor = scheduledFor;
    }
}
exports.NotificationRequestedEvent = NotificationRequestedEvent;
class NotificationSentEvent {
    constructor(notificationId, recipient, type, sentAt, deliveryId) {
        this.notificationId = notificationId;
        this.recipient = recipient;
        this.type = type;
        this.sentAt = sentAt;
        this.deliveryId = deliveryId;
    }
}
exports.NotificationSentEvent = NotificationSentEvent;
class NotificationFailedEvent {
    constructor(notificationId, recipient, type, error, retryCount) {
        this.notificationId = notificationId;
        this.recipient = recipient;
        this.type = type;
        this.error = error;
        this.retryCount = retryCount;
    }
}
exports.NotificationFailedEvent = NotificationFailedEvent;
class SendEmailCommand {
    constructor(notificationId, to, subject, htmlContent, textContent, attachments) {
        this.notificationId = notificationId;
        this.to = to;
        this.subject = subject;
        this.htmlContent = htmlContent;
        this.textContent = textContent;
        this.attachments = attachments;
    }
}
exports.SendEmailCommand = SendEmailCommand;
class SendSMSCommand {
    constructor(notificationId, to, message) {
        this.notificationId = notificationId;
        this.to = to;
        this.message = message;
    }
}
exports.SendSMSCommand = SendSMSCommand;
class SendPushNotificationCommand {
    constructor(notificationId, userId, title, body, data) {
        this.notificationId = notificationId;
        this.userId = userId;
        this.title = title;
        this.body = body;
        this.data = data;
    }
}
exports.SendPushNotificationCommand = SendPushNotificationCommand;
class RenderTemplateCommand {
    constructor(templateName, templateData, outputFormat) {
        this.templateName = templateName;
        this.templateData = templateData;
        this.outputFormat = outputFormat;
    }
}
exports.RenderTemplateCommand = RenderTemplateCommand;
class LogNotificationCommand {
    constructor(notificationId, status, details) {
        this.notificationId = notificationId;
        this.status = status;
        this.details = details;
    }
}
exports.LogNotificationCommand = LogNotificationCommand;
let NotificationOrchestrationSaga = class NotificationOrchestrationSaga extends base_saga_1.BaseSaga {
    constructor() {
        super(...arguments);
        this.notificationRequested = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)(NotificationRequestedEvent), (0, operators_1.mergeMap)((event) => {
                console.log(`📧 Notification Saga: Processing ${event.type} notification for ${event.recipient}`);
                const commands = [];
                this.addStep({
                    stepId: 'log-notification-request',
                    command: new LogNotificationCommand(event.notificationId, 'pending', {
                        recipient: event.recipient,
                        type: event.type,
                        subject: event.subject,
                        priority: event.priority,
                        requestedAt: new Date()
                    })
                });
                switch (event.type) {
                    case send_notification_command_1.NotificationType.EMAIL:
                        this.addStep({
                            stepId: 'render-email-template',
                            command: new RenderTemplateCommand(this.getEmailTemplate(event.subject), event.templateData, 'html'),
                            maxRetries: 2
                        });
                        this.addStep({
                            stepId: 'send-email',
                            command: new SendEmailCommand(event.notificationId, event.recipient, event.subject, event.message, event.message, event.templateData?.attachments),
                            maxRetries: 3
                        });
                        break;
                    case send_notification_command_1.NotificationType.SMS:
                        this.addStep({
                            stepId: 'send-sms',
                            command: new SendSMSCommand(event.notificationId, event.recipient, event.message),
                            maxRetries: 3
                        });
                        break;
                    case send_notification_command_1.NotificationType.PUSH:
                        this.addStep({
                            stepId: 'send-push-notification',
                            command: new SendPushNotificationCommand(event.notificationId, event.recipient, event.subject, event.message, event.templateData),
                            maxRetries: 3
                        });
                        break;
                }
                this.addStep({
                    stepId: 'log-notification-completion',
                    command: new LogNotificationCommand(event.notificationId, 'sent', {
                        completedAt: new Date(),
                        type: event.type
                    })
                });
                this.startSaga();
                return new rxjs_1.Observable(observer => {
                    observer.next(this.steps[0].command);
                    observer.complete();
                });
            }));
        };
        this.notificationFailed = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)(NotificationFailedEvent), (0, operators_1.map)((event) => {
                console.log(`❌ Notification Saga: Failed to send ${event.type} to ${event.recipient}`);
                if (event.retryCount < 3) {
                    const retryDelay = Math.pow(2, event.retryCount) * 1000;
                    return new rxjs_1.Observable(observer => {
                        setTimeout(() => {
                            observer.next(new send_notification_command_1.SendNotificationCommand(event.recipient, event.type, 'Retry: Notification', 'Retrying failed notification', { retryCount: event.retryCount + 1 }));
                            observer.complete();
                        }, retryDelay);
                    });
                }
                else {
                    return new LogNotificationCommand(event.notificationId, 'failed', {
                        finalError: event.error,
                        maxRetriesReached: true,
                        failedAt: new Date()
                    });
                }
            }));
        };
        this.bulkNotificationRequested = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)('BulkNotificationRequestedEvent'), (0, operators_1.mergeMap)((event) => {
                console.log(`📬 Notification Saga: Processing bulk notification for ${event.recipients.length} recipients`);
                const batchSize = 50;
                const batches = this.chunkArray(event.recipients, batchSize);
                return new rxjs_1.Observable(observer => {
                    batches.forEach((batch, index) => {
                        setTimeout(() => {
                            batch.forEach(recipient => {
                                observer.next(new send_notification_command_1.SendNotificationCommand(recipient, event.type, event.subject, event.message, event.templateData));
                            });
                            if (index === batches.length - 1) {
                                observer.complete();
                            }
                        }, index * 1000);
                    });
                });
            }));
        };
        this.scheduledNotificationDue = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)('ScheduledNotificationDueEvent'), (0, operators_1.map)((event) => {
                console.log(`⏰ Notification Saga: Processing scheduled notification ${event.notificationId}`);
                return new send_notification_command_1.SendNotificationCommand(event.recipient, event.type, event.subject, event.message, event.templateData);
            }));
        };
        this.reminderRequested = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)('ReminderRequestedEvent'), (0, operators_1.map)((event) => {
                console.log(`⏰ Notification Saga: Setting up reminder for ${event.reminderType}`);
                const reminderTimes = this.calculateReminderTimes(event.reminderType, event.targetDate);
                return new rxjs_1.Observable(observer => {
                    reminderTimes.forEach((reminderTime, index) => {
                        const delay = reminderTime.getTime() - new Date().getTime();
                        if (delay > 0) {
                            setTimeout(() => {
                                observer.next(new send_notification_command_1.SendNotificationCommand(event.recipient, send_notification_command_1.NotificationType.EMAIL, event.subject, event.message, { ...event.templateData, reminderNumber: index + 1 }));
                            }, delay);
                        }
                    });
                    observer.complete();
                });
            }));
        };
    }
    getEmailTemplate(subject) {
        const templateMap = {
            'Booking Confirmed': 'booking-confirmation',
            'Payment Successful': 'payment-success',
            'Event Cancelled': 'event-cancellation',
            'Seats Reserved': 'seat-reservation',
            'Booking Expired': 'booking-expiration'
        };
        return templateMap[subject] || 'default';
    }
    chunkArray(array, size) {
        const chunks = [];
        for (let i = 0; i < array.length; i += size) {
            chunks.push(array.slice(i, i + size));
        }
        return chunks;
    }
    calculateReminderTimes(reminderType, targetDate) {
        const reminders = [];
        const target = new Date(targetDate);
        switch (reminderType) {
            case 'booking-expiration':
                reminders.push(new Date(target.getTime() - 10 * 60 * 1000));
                reminders.push(new Date(target.getTime() - 5 * 60 * 1000));
                reminders.push(new Date(target.getTime() - 1 * 60 * 1000));
                break;
            case 'event-reminder':
                reminders.push(new Date(target.getTime() - 7 * 24 * 60 * 60 * 1000));
                reminders.push(new Date(target.getTime() - 24 * 60 * 60 * 1000));
                reminders.push(new Date(target.getTime() - 60 * 60 * 1000));
                break;
        }
        return reminders.filter(date => date > new Date());
    }
    handle(event) {
        switch (event.constructor.name) {
            case NotificationRequestedEvent.name:
                return this.notificationRequested(new rxjs_1.Observable(observer => observer.next(event)));
            case NotificationFailedEvent.name:
                return this.notificationFailed(new rxjs_1.Observable(observer => observer.next(event)));
            case 'BulkNotificationRequestedEvent':
                return this.bulkNotificationRequested(new rxjs_1.Observable(observer => observer.next(event)));
            case 'ScheduledNotificationDueEvent':
                return this.scheduledNotificationDue(new rxjs_1.Observable(observer => observer.next(event)));
            case 'ReminderRequestedEvent':
                return this.reminderRequested(new rxjs_1.Observable(observer => observer.next(event)));
            default:
                return new rxjs_1.Observable(observer => observer.complete());
        }
    }
};
exports.NotificationOrchestrationSaga = NotificationOrchestrationSaga;
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], NotificationOrchestrationSaga.prototype, "notificationRequested", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], NotificationOrchestrationSaga.prototype, "notificationFailed", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], NotificationOrchestrationSaga.prototype, "bulkNotificationRequested", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], NotificationOrchestrationSaga.prototype, "scheduledNotificationDue", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], NotificationOrchestrationSaga.prototype, "reminderRequested", void 0);
exports.NotificationOrchestrationSaga = NotificationOrchestrationSaga = __decorate([
    (0, common_1.Injectable)()
], NotificationOrchestrationSaga);
//# sourceMappingURL=notification-orchestration.saga.js.map