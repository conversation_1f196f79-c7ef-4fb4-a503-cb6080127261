import { CommandBus } from '@nestjs/cqrs';
import { Observable } from 'rxjs';
import { BaseEvent } from '../events/base.event';
import { BaseCommand } from '../commands/base.command';
export interface SagaStep {
    stepId: string;
    command: BaseCommand;
    compensationCommand?: BaseCommand;
    retryCount?: number;
    maxRetries?: number;
}
export declare enum SagaStatus {
    STARTED = "STARTED",
    IN_PROGRESS = "IN_PROGRESS",
    COMPLETED = "COMPLETED",
    FAILED = "FAILED",
    COMPENSATING = "COMPENSATING",
    COMPENSATED = "COMPENSATED",
    CANCELLED = "CANCELLED"
}
export declare abstract class BaseSaga {
    protected commandBus: CommandBus;
    protected sagaId: string;
    protected status: SagaStatus;
    protected steps: SagaStep[];
    protected currentStepIndex: number;
    protected completedSteps: string[];
    protected failedSteps: string[];
    constructor(commandBus: CommandBus);
    abstract handle(event: BaseEvent): Observable<BaseCommand>;
    protected executeStep(step: SagaStep): Promise<void>;
    protected compensate(): Promise<void>;
    protected addStep(step: SagaStep): void;
    protected executeNextStep(): Promise<void>;
    protected startSaga(): void;
    private generateSagaId;
    get id(): string;
    get currentStatus(): SagaStatus;
    get progress(): {
        completed: number;
        total: number;
        percentage: number;
    };
}
