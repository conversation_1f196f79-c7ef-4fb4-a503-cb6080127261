"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentFailedEvent = void 0;
const base_event_1 = require("../../../common/events/base.event");
class PaymentFailedEvent extends base_event_1.DomainEvent {
    constructor(aggregateId, bookingId, errorCode, errorMessage, version = 1) {
        super(aggregateId, version);
        this.aggregateId = aggregateId;
        this.bookingId = bookingId;
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }
}
exports.PaymentFailedEvent = PaymentFailedEvent;
//# sourceMappingURL=payment-failed.event.js.map