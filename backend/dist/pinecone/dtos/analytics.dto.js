"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsResultDto = exports.PricingOptimizationResultDto = exports.ContentModerationResultDto = exports.FraudDetectionResultDto = exports.DemandSignalDto = exports.MarketConditionsDto = exports.PricingOptimizationDto = exports.ContentModerationDto = exports.UserBehaviorDto = exports.FraudDetectionDto = exports.AnalyticsQueryDto = exports.AnalyticsType = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
var AnalyticsType;
(function (AnalyticsType) {
    AnalyticsType["USER_CLUSTERING"] = "user_clustering";
    AnalyticsType["EVENT_PERFORMANCE"] = "event_performance";
    AnalyticsType["TREND_ANALYSIS"] = "trend_analysis";
    AnalyticsType["MARKET_INSIGHTS"] = "market_insights";
})(AnalyticsType || (exports.AnalyticsType = AnalyticsType = {}));
class AnalyticsQueryDto {
}
exports.AnalyticsQueryDto = AnalyticsQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of analytics to perform',
        enum: AnalyticsType,
        example: AnalyticsType.USER_CLUSTERING,
    }),
    (0, class_validator_1.IsEnum)(AnalyticsType),
    __metadata("design:type", String)
], AnalyticsQueryDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Start date for analysis',
        example: '2024-01-01T00:00:00Z',
    }),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], AnalyticsQueryDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'End date for analysis',
        example: '2024-12-31T23:59:59Z',
    }),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], AnalyticsQueryDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional filters for analysis',
        example: { category: 'music', venue: 'Madison Square Garden' },
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], AnalyticsQueryDto.prototype, "filters", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Group results by specific fields',
        example: ['category', 'venue'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AnalyticsQueryDto.prototype, "groupBy", void 0);
class FraudDetectionDto {
}
exports.FraudDetectionDto = FraudDetectionDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID to analyze',
        example: 'user-123',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FraudDetectionDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Event ID being purchased',
        example: 'event-456',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FraudDetectionDto.prototype, "eventId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Purchase amount',
        example: 150.00,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], FraudDetectionDto.prototype, "purchaseAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User behavior data',
        example: [
            {
                action: 'view',
                eventId: 'event-456',
                timestamp: '2024-01-15T10:30:00Z',
                duration: 45000,
            },
        ],
    }),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], FraudDetectionDto.prototype, "userBehavior", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Device information',
        example: { userAgent: 'Mozilla/5.0...', ip: '***********' },
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], FraudDetectionDto.prototype, "deviceInfo", void 0);
class UserBehaviorDto {
}
exports.UserBehaviorDto = UserBehaviorDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User action type',
        example: 'view',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserBehaviorDto.prototype, "action", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Event ID (if applicable)',
        example: 'event-456',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserBehaviorDto.prototype, "eventId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search query (if applicable)',
        example: 'rock concerts NYC',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UserBehaviorDto.prototype, "query", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of action',
        example: '2024-01-15T10:30:00Z',
    }),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], UserBehaviorDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Duration of action in milliseconds',
        example: 45000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UserBehaviorDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional metadata',
        example: { page: 'event-details', referrer: 'search' },
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], UserBehaviorDto.prototype, "metadata", void 0);
class ContentModerationDto {
}
exports.ContentModerationDto = ContentModerationDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Content to moderate',
        example: 'This event was absolutely amazing! Great performance.',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContentModerationDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of content',
        example: 'review',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContentModerationDto.prototype, "contentType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'User ID who created the content',
        example: 'user-789',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContentModerationDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Event ID the content relates to',
        example: 'event-456',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ContentModerationDto.prototype, "eventId", void 0);
class PricingOptimizationDto {
}
exports.PricingOptimizationDto = PricingOptimizationDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Event ID to optimize pricing for',
        example: 'event-789',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PricingOptimizationDto.prototype, "eventId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current ticket price',
        example: 75.00,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], PricingOptimizationDto.prototype, "currentPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Market conditions data',
    }),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", MarketConditionsDto)
], PricingOptimizationDto.prototype, "marketConditions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Demand signals data',
    }),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], PricingOptimizationDto.prototype, "demandSignals", void 0);
class MarketConditionsDto {
}
exports.MarketConditionsDto = MarketConditionsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Competitor prices',
        example: [65.00, 80.00, 70.00, 85.00],
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsNumber)({}, { each: true }),
    __metadata("design:type", Array)
], MarketConditionsDto.prototype, "competitorPrices", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Seasonality factor',
        example: 1.2,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], MarketConditionsDto.prototype, "seasonality", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Economic indicators',
        example: { inflation: 3.2, unemployment: 4.1, gdp_growth: 2.8 },
    }),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], MarketConditionsDto.prototype, "economicIndicators", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Weather forecast',
        example: 'sunny',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], MarketConditionsDto.prototype, "weatherForecast", void 0);
class DemandSignalDto {
}
exports.DemandSignalDto = DemandSignalDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of signal',
        example: '2024-01-15T12:00:00Z',
    }),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], DemandSignalDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Search volume',
        example: 150,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DemandSignalDto.prototype, "searchVolume", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'View count',
        example: 1200,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DemandSignalDto.prototype, "viewCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Wishlist additions',
        example: 45,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DemandSignalDto.prototype, "wishlistAdds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Social media mentions',
        example: 78,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], DemandSignalDto.prototype, "socialMentions", void 0);
class FraudDetectionResultDto {
}
exports.FraudDetectionResultDto = FraudDetectionResultDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Risk score (0-100)',
        example: 25,
    }),
    __metadata("design:type", Number)
], FraudDetectionResultDto.prototype, "riskScore", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Risk level',
        example: 'low',
    }),
    __metadata("design:type", String)
], FraudDetectionResultDto.prototype, "riskLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Reasons for risk assessment',
        example: ['Standard processing', 'No suspicious patterns detected'],
    }),
    __metadata("design:type", Array)
], FraudDetectionResultDto.prototype, "reasons", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recommended actions',
        example: ['Standard processing', 'Log for analysis'],
    }),
    __metadata("design:type", Array)
], FraudDetectionResultDto.prototype, "recommendations", void 0);
class ContentModerationResultDto {
}
exports.ContentModerationResultDto = ContentModerationResultDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether content is appropriate',
        example: true,
    }),
    __metadata("design:type", Boolean)
], ContentModerationResultDto.prototype, "isAppropriate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Confidence in moderation decision',
        example: 0.92,
    }),
    __metadata("design:type", Number)
], ContentModerationResultDto.prototype, "confidence", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Content categories detected',
        example: ['positive', 'entertainment'],
    }),
    __metadata("design:type", Array)
], ContentModerationResultDto.prototype, "categories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sentiment analysis result',
        example: 'positive',
    }),
    __metadata("design:type", String)
], ContentModerationResultDto.prototype, "sentiment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Toxicity score (0-100)',
        example: 5,
    }),
    __metadata("design:type", Number)
], ContentModerationResultDto.prototype, "toxicityScore", void 0);
class PricingOptimizationResultDto {
}
exports.PricingOptimizationResultDto = PricingOptimizationResultDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recommended price',
        example: 82.50,
    }),
    __metadata("design:type", Number)
], PricingOptimizationResultDto.prototype, "recommendedPrice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Price range recommendation',
        example: { min: 70.00, max: 95.00 },
    }),
    __metadata("design:type", Object)
], PricingOptimizationResultDto.prototype, "priceRange", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Confidence in recommendation',
        example: 0.78,
    }),
    __metadata("design:type", Number)
], PricingOptimizationResultDto.prototype, "confidence", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Reasoning for price recommendation',
        example: ['Based on similar market conditions', 'High demand detected'],
    }),
    __metadata("design:type", Array)
], PricingOptimizationResultDto.prototype, "reasoning", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Expected demand at recommended price',
        example: 450,
    }),
    __metadata("design:type", Number)
], PricingOptimizationResultDto.prototype, "expectedDemand", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Revenue projection',
        example: 37125.00,
    }),
    __metadata("design:type", Number)
], PricingOptimizationResultDto.prototype, "revenueProjection", void 0);
class AnalyticsResultDto {
}
exports.AnalyticsResultDto = AnalyticsResultDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Analytics type',
        example: 'user_clustering',
    }),
    __metadata("design:type", String)
], AnalyticsResultDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Analysis data',
        example: [{ segment: 'music_lovers', count: 1250, characteristics: ['frequent_concert_goer', 'high_spender'] }],
    }),
    __metadata("design:type", Array)
], AnalyticsResultDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Key insights from analysis',
        example: ['3 distinct user segments identified', 'Music lovers segment shows highest engagement'],
    }),
    __metadata("design:type", Array)
], AnalyticsResultDto.prototype, "insights", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Actionable recommendations',
        example: ['Target music lovers with premium events', 'Create loyalty program for high spenders'],
    }),
    __metadata("design:type", Array)
], AnalyticsResultDto.prototype, "recommendations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Confidence in analysis',
        example: 0.85,
    }),
    __metadata("design:type", Number)
], AnalyticsResultDto.prototype, "confidence", void 0);
//# sourceMappingURL=analytics.dto.js.map