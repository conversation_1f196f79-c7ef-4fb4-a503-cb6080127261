# Online Ticket Booking System

A comprehensive ticket booking platform built with modern microservices architecture.

## Architecture Overview

This system implements:
- **Frontend**: Next.js 14 with TypeScript and Tailwind CSS
- **Backend**: NestJS with CQRS, Event Sourcing, and Saga Pattern
- **Caching**: Redis for high-performance data caching
- **Message Queue**: Apache Kafka for reliable event processing
- **Database**: PostgreSQL with TypeORM
- **Authentication**: JWT with optional Okta integration
- **Rate Limiting**: Redis-based API throttling
- **Real-time**: WebSocket connections for live updates

## Project Structure

```
├── frontend/                 # Next.js frontend application
├── backend/                  # NestJS backend services
├── infrastructure/           # Docker, Kubernetes, and deployment configs
├── shared/                   # Shared types and utilities
├── docs/                     # Documentation and API specs
└── docker-compose.yml        # Local development environment
```

## Features

- **Event Discovery**: Browse and search events with real-time data
- **Seat Selection**: Interactive seat maps with live availability
- **Secure Booking**: Multi-step booking process with payment integration
- **Real-time Updates**: Live seat availability and booking notifications
- **User Management**: Authentication with role-based access control
- **Admin Dashboard**: Event management and analytics
- **Mobile Responsive**: Optimized for all device types

## Technology Stack

### Frontend
- Next.js 14 (App Router)
- TypeScript
- Tailwind CSS
- React Query for state management
- Socket.io for real-time features

### Backend
- NestJS with TypeScript
- CQRS with Event Sourcing
- Saga Pattern for complex workflows
- TypeORM with PostgreSQL
- Redis for caching and rate limiting
- Apache Kafka for message queuing
- JWT authentication
- Swagger API documentation

### Infrastructure
- Docker & Docker Compose
- Redis
- Apache Kafka
- PostgreSQL
- Optional: Kubernetes for production

## 🚀 **Deploy to AWS Free Tier (5 Minutes)**

### **Prerequisites**
```bash
# Install required tools (if not already installed)
# AWS CLI, Terraform, Docker, Node.js

# Configure AWS credentials
aws configure
```

### **Option 1: One-Command Deployment**
```bash
# Validate everything is ready
./scripts/validate-deployment.sh

# Deploy to AWS Free Tier
./scripts/deploy-free-tier.sh
```

### **Option 2: Manual Deployment**
```bash
# Follow the detailed guide
open DEPLOY-NOW.md
```

### **💰 Cost Options**

#### **Ultra Free Tier** (Recommended for Development)
- **Cost**: $0-5/month
- **Features**: All core functionality
- **Trade-off**: Public subnets (still secure with security groups)

#### **Standard Free Tier**
- **Cost**: ~$45/month (NAT Gateway)
- **Features**: Full production architecture
- **Benefits**: Private subnets, enhanced security

## Getting Started (Local Development)

### Prerequisites
- Node.js 18+
- Docker and Docker Compose
- PostgreSQL
- Redis
- Apache Kafka

### Quick Start

1. Clone the repository
2. Install dependencies: `npm install`
3. Start infrastructure: `docker-compose up -d`
4. Run backend: `cd backend && npm run start:dev`
5. Run frontend: `cd frontend && npm run dev`

## External APIs Integration

The system integrates with:
- **Ticketmaster API**: For event data and venue information
- **Eventbrite API**: Additional event sources
- **Payment Gateways**: Stripe/PayPal for secure payments
- **Notification Services**: Email and SMS notifications

## Development

See individual README files in each service directory for detailed setup instructions.

## License

MIT License
