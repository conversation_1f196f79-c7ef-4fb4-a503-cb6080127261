"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PineconeController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../../auth/guards/roles.guard");
const roles_decorator_1 = require("../../auth/decorators/roles.decorator");
const user_role_enum_1 = require("../../auth/enums/user-role.enum");
const pinecone_service_1 = require("../services/pinecone.service");
const search_dto_1 = require("../dtos/search.dto");
const analytics_dto_1 = require("../dtos/analytics.dto");
let PineconeController = class PineconeController {
    constructor(pineconeService) {
        this.pineconeService = pineconeService;
    }
    async searchEvents(searchDto) {
        return this.pineconeService.searchSimilarEvents(searchDto.query, {
            topK: searchDto.topK,
            includeMetadata: searchDto.includeMetadata,
            filter: searchDto.filter,
            namespace: searchDto.namespace,
        });
    }
    async findSimilarEvents(similarDto) {
        return this.pineconeService.findSimilarEvents({
            eventId: similarDto.eventId,
            limit: similarDto.limit,
            threshold: similarDto.threshold,
            includeMetadata: similarDto.includeMetadata,
        });
    }
    async getRecommendations(recommendationsDto) {
        const request = {
            userId: recommendationsDto.userId,
            limit: recommendationsDto.limit,
            categories: recommendationsDto.categories,
            priceRange: recommendationsDto.priceRange,
            dateRange: recommendationsDto.dateRange ? {
                start: new Date(recommendationsDto.dateRange.start),
                end: new Date(recommendationsDto.dateRange.end),
            } : undefined,
            location: recommendationsDto.location,
            excludeEventIds: recommendationsDto.excludeEventIds,
        };
        return this.pineconeService.getPersonalizedRecommendations(request);
    }
    async performAnalytics(analyticsDto) {
        const query = {
            type: analyticsDto.type,
            timeRange: {
                start: new Date(analyticsDto.startDate),
                end: new Date(analyticsDto.endDate),
            },
            filters: analyticsDto.filters,
            groupBy: analyticsDto.groupBy,
        };
        return this.pineconeService.performAnalytics(query);
    }
    async detectFraud(fraudDto) {
        const request = {
            userId: fraudDto.userId,
            eventId: fraudDto.eventId,
            purchaseAmount: fraudDto.purchaseAmount,
            userBehavior: fraudDto.userBehavior.map(behavior => ({
                ...behavior,
                timestamp: new Date(behavior.timestamp),
            })),
            deviceInfo: fraudDto.deviceInfo,
        };
        return this.pineconeService.detectFraud(request);
    }
    async moderateContent(moderationDto) {
        return this.pineconeService.moderateContent({
            content: moderationDto.content,
            contentType: moderationDto.contentType,
            userId: moderationDto.userId,
            eventId: moderationDto.eventId,
        });
    }
    async optimizePricing(pricingDto) {
        const request = {
            eventId: pricingDto.eventId,
            currentPrice: pricingDto.currentPrice,
            marketConditions: {
                competitorPrices: pricingDto.marketConditions.competitorPrices,
                seasonality: pricingDto.marketConditions.seasonality,
                economicIndicators: pricingDto.marketConditions.economicIndicators,
                weatherForecast: pricingDto.marketConditions.weatherForecast,
            },
            demandSignals: pricingDto.demandSignals.map(signal => ({
                ...signal,
                timestamp: new Date(signal.timestamp),
            })),
        };
        return this.pineconeService.optimizePricing(request);
    }
    async healthCheck() {
        try {
            const eventsStats = await this.pineconeService.getIndexStats('events-index');
            const usersStats = await this.pineconeService.getIndexStats('users-index');
            const contentStats = await this.pineconeService.getIndexStats('content-index');
            return {
                status: 'healthy',
                services: {
                    pinecone: 'connected',
                    openai: 'connected',
                    indexes: {
                        events: eventsStats,
                        users: usersStats,
                        content: contentStats,
                    },
                },
            };
        }
        catch (error) {
            return {
                status: 'unhealthy',
                services: {
                    pinecone: 'error',
                    openai: 'unknown',
                    error: error.message,
                },
            };
        }
    }
    async getIndexStats(indexName) {
        return this.pineconeService.getIndexStats(indexName);
    }
    async generateTextEmbedding(text) {
        const embedding = await this.pineconeService.generateTextEmbedding(text);
        return {
            embedding,
            dimensions: embedding.length,
        };
    }
    async batchUpsertEvents(events) {
        const results = { processed: 0, errors: [] };
        for (const event of events) {
            try {
                await this.pineconeService.upsertEvent(event);
                results.processed++;
            }
            catch (error) {
                results.errors.push({
                    eventId: event.eventId,
                    error: error.message,
                });
            }
        }
        return results;
    }
    async batchUpsertUsers(users) {
        const results = { processed: 0, errors: [] };
        for (const user of users) {
            try {
                await this.pineconeService.upsertUser(user);
                results.processed++;
            }
            catch (error) {
                results.errors.push({
                    userId: user.userId,
                    error: error.message,
                });
            }
        }
        return results;
    }
};
exports.PineconeController = PineconeController;
__decorate([
    (0, common_1.Post)('search/events'),
    (0, swagger_1.ApiOperation)({
        summary: 'Search events using natural language',
        description: 'Find events using semantic search with natural language queries',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Search results returned successfully',
        type: [search_dto_1.SearchResultDto],
    }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [search_dto_1.SearchEventsDto]),
    __metadata("design:returntype", Promise)
], PineconeController.prototype, "searchEvents", null);
__decorate([
    (0, common_1.Post)('search/similar'),
    (0, swagger_1.ApiOperation)({
        summary: 'Find similar events',
        description: 'Find events similar to a specific event using vector similarity',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Similar events found successfully',
        type: [search_dto_1.SearchResultDto],
    }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [search_dto_1.SimilarEventsDto]),
    __metadata("design:returntype", Promise)
], PineconeController.prototype, "findSimilarEvents", null);
__decorate([
    (0, common_1.Post)('recommendations'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get personalized recommendations',
        description: 'Get AI-powered personalized event recommendations for a user',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Recommendations generated successfully',
        type: [search_dto_1.RecommendationResultDto],
    }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [search_dto_1.RecommendationsDto]),
    __metadata("design:returntype", Promise)
], PineconeController.prototype, "getRecommendations", null);
__decorate([
    (0, common_1.Post)('analytics'),
    (0, swagger_1.ApiOperation)({
        summary: 'Perform AI analytics',
        description: 'Run advanced analytics using vector analysis',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Analytics completed successfully',
        type: analytics_dto_1.AnalyticsResultDto,
    }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.ADMIN, user_role_enum_1.UserRole.ANALYST),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_dto_1.AnalyticsQueryDto]),
    __metadata("design:returntype", Promise)
], PineconeController.prototype, "performAnalytics", null);
__decorate([
    (0, common_1.Post)('fraud-detection'),
    (0, swagger_1.ApiOperation)({
        summary: 'Detect fraudulent activity',
        description: 'Analyze user behavior patterns to detect potential fraud',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Fraud analysis completed successfully',
        type: search_dto_1.FraudDetectionResultDto,
    }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.ADMIN, user_role_enum_1.UserRole.SECURITY),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [search_dto_1.FraudDetectionDto]),
    __metadata("design:returntype", Promise)
], PineconeController.prototype, "detectFraud", null);
__decorate([
    (0, common_1.Post)('content-moderation'),
    (0, swagger_1.ApiOperation)({
        summary: 'Moderate user content',
        description: 'Analyze and moderate user-generated content using AI',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Content moderation completed successfully',
        type: search_dto_1.ContentModerationResultDto,
    }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.ADMIN, user_role_enum_1.UserRole.MODERATOR),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [search_dto_1.ContentModerationDto]),
    __metadata("design:returntype", Promise)
], PineconeController.prototype, "moderateContent", null);
__decorate([
    (0, common_1.Post)('pricing-optimization'),
    (0, swagger_1.ApiOperation)({
        summary: 'Optimize event pricing',
        description: 'Get AI-powered pricing recommendations based on market conditions',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Pricing optimization completed successfully',
        type: analytics_dto_1.PricingOptimizationResultDto,
    }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.ADMIN, user_role_enum_1.UserRole.PRICING_MANAGER),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_dto_1.PricingOptimizationDto]),
    __metadata("design:returntype", Promise)
], PineconeController.prototype, "optimizePricing", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({
        summary: 'Check AI service health',
        description: 'Check the health status of Pinecone and AI services',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Health check completed successfully',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PineconeController.prototype, "healthCheck", null);
__decorate([
    (0, common_1.Get)('stats/:indexName'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get index statistics',
        description: 'Get detailed statistics for a specific Pinecone index',
    }),
    (0, swagger_1.ApiParam)({
        name: 'indexName',
        description: 'Name of the Pinecone index',
        example: 'events-index',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Index statistics retrieved successfully',
    }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.ADMIN),
    __param(0, (0, common_1.Param)('indexName')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PineconeController.prototype, "getIndexStats", null);
__decorate([
    (0, common_1.Post)('embeddings/text'),
    (0, swagger_1.ApiOperation)({
        summary: 'Generate text embedding',
        description: 'Generate vector embedding for text using OpenAI',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Text embedding generated successfully',
    }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.ADMIN, user_role_enum_1.UserRole.DEVELOPER),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)('text')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PineconeController.prototype, "generateTextEmbedding", null);
__decorate([
    (0, common_1.Post)('batch/events/upsert'),
    (0, swagger_1.ApiOperation)({
        summary: 'Batch upsert events',
        description: 'Upsert multiple events to Pinecone in batch',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Batch upsert completed successfully',
    }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.ADMIN, user_role_enum_1.UserRole.DATA_MANAGER),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)('events')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], PineconeController.prototype, "batchUpsertEvents", null);
__decorate([
    (0, common_1.Post)('batch/users/upsert'),
    (0, swagger_1.ApiOperation)({
        summary: 'Batch upsert users',
        description: 'Upsert multiple user profiles to Pinecone in batch',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Batch upsert completed successfully',
    }),
    (0, common_1.UseGuards)(roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(user_role_enum_1.UserRole.ADMIN, user_role_enum_1.UserRole.DATA_MANAGER),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)('users')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], PineconeController.prototype, "batchUpsertUsers", null);
exports.PineconeController = PineconeController = __decorate([
    (0, swagger_1.ApiTags)('AI & Vector Search'),
    (0, common_1.Controller)('api/v1/ai'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [pinecone_service_1.PineconeService])
], PineconeController);
//# sourceMappingURL=pinecone.controller.js.map