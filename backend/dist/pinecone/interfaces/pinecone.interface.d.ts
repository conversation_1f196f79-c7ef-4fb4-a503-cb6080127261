export interface PineconeConfig {
    apiKey: string;
    environment: string;
    indexName: string;
}
export interface VectorMetadata {
    id: string;
    eventId?: string;
    userId?: string;
    type: 'event' | 'user' | 'content' | 'artist';
    title?: string;
    description?: string;
    category?: string;
    venue?: string;
    date?: string;
    price?: number;
    tags?: string[];
    createdAt: Date;
    updatedAt: Date;
}
export interface SearchQuery {
    vector?: number[];
    text?: string;
    topK?: number;
    includeMetadata?: boolean;
    filter?: Record<string, any>;
    namespace?: string;
}
export interface SearchResult {
    id: string;
    score: number;
    metadata?: VectorMetadata;
}
export interface UpsertRequest {
    id: string;
    values: number[];
    metadata?: VectorMetadata;
    namespace?: string;
}
export interface EventEmbedding {
    eventId: string;
    title: string;
    description: string;
    category: string;
    venue: string;
    artist: string;
    date: Date;
    price: number;
    tags: string[];
    imageUrl?: string;
    audioUrl?: string;
}
export interface UserEmbedding {
    userId: string;
    preferences: string[];
    behaviorHistory: UserBehavior[];
    demographics: UserDemographics;
    purchaseHistory: PurchaseHistory[];
}
export interface UserBehavior {
    action: 'view' | 'search' | 'click' | 'purchase' | 'share';
    eventId?: string;
    query?: string;
    timestamp: Date;
    duration?: number;
    metadata?: Record<string, any>;
}
export interface UserDemographics {
    ageRange?: string;
    location?: string;
    interests?: string[];
    spendingPattern?: 'low' | 'medium' | 'high';
}
export interface PurchaseHistory {
    eventId: string;
    purchaseDate: Date;
    amount: number;
    category: string;
    venue: string;
    satisfaction?: number;
}
export interface RecommendationRequest {
    userId: string;
    limit?: number;
    categories?: string[];
    priceRange?: {
        min: number;
        max: number;
    };
    dateRange?: {
        start: Date;
        end: Date;
    };
    location?: string;
    excludeEventIds?: string[];
}
export interface RecommendationResult {
    eventId: string;
    score: number;
    reason: string;
    confidence: number;
    metadata: VectorMetadata;
}
export interface SimilarityRequest {
    eventId: string;
    limit?: number;
    threshold?: number;
    includeMetadata?: boolean;
}
export interface FraudDetectionRequest {
    userId: string;
    eventId: string;
    purchaseAmount: number;
    userBehavior: UserBehavior[];
    deviceInfo?: Record<string, any>;
}
export interface FraudDetectionResult {
    riskScore: number;
    riskLevel: string;
    riskFactors: string[];
    recommendedAction: string;
    confidence: number;
    details?: any;
}
export interface ContentModerationRequest {
    content: string;
    contentType: 'review' | 'comment' | 'description';
    userId?: string;
    eventId?: string;
}
export interface ContentModerationResult {
    isAppropriate: boolean;
    confidence: number;
    categories: string[];
    sentiment: 'positive' | 'negative' | 'neutral';
    toxicityScore: number;
}
export interface AnalyticsQuery {
    type: 'user_clustering' | 'event_performance' | 'trend_analysis' | 'market_insights';
    timeRange: {
        start: Date;
        end: Date;
    };
    filters?: Record<string, any>;
    groupBy?: string[];
}
export interface AnalyticsResult {
    type: string;
    data: any[];
    insights: string[];
    recommendations: string[];
    confidence: number;
}
export interface PricingOptimizationRequest {
    eventId: string;
    currentPrice: number;
    marketConditions: MarketConditions;
    demandSignals: DemandSignal[];
}
export interface MarketConditions {
    competitorPrices: number[];
    seasonality: number;
    economicIndicators: Record<string, number>;
    weatherForecast?: string;
}
export interface DemandSignal {
    timestamp: Date;
    searchVolume: number;
    viewCount: number;
    wishlistAdds: number;
    socialMentions: number;
}
export interface PricingOptimizationResult {
    recommendedPrice: number;
    priceRange: {
        min: number;
        max: number;
    };
    confidence: number;
    reasoning: string[];
    expectedDemand: number;
    revenueProjection: number;
}
