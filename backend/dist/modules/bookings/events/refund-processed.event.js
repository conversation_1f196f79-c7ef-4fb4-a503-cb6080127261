"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RefundProcessedEvent = void 0;
const base_event_1 = require("../../../common/events/base.event");
class RefundProcessedEvent extends base_event_1.DomainEvent {
    constructor(aggregateId, refundAmount, refundId, refundedAt, version = 1) {
        super(aggregateId, version);
        this.aggregateId = aggregateId;
        this.refundAmount = refundAmount;
        this.refundId = refundId;
        this.refundedAt = refundedAt;
    }
}
exports.RefundProcessedEvent = RefundProcessedEvent;
//# sourceMappingURL=refund-processed.event.js.map