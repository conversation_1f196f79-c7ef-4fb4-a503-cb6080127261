apiVersion: v1
kind: Secret
metadata:
  name: backend-secrets
  namespace: ticket-booking
type: Opaque
data:
  # Base64 encoded secrets - replace with actual values
  DB_PASSWORD: cG9zdGdyZXM=  # postgres
  JWT_SECRET: eW91ci1zdXBlci1zZWNyZXQtand0LWtleS1jaGFuZ2UtdGhpcy1pbi1wcm9kdWN0aW9u  # your-super-secret-jwt-key-change-this-in-production
  REDIS_PASSWORD: ""  # empty for no auth
  STRIPE_SECRET_KEY: c2tfdGVzdF8xMjM0NTY3ODkw  # sk_test_1234567890
  PAYPAL_CLIENT_SECRET: cGF5cGFsX3NlY3JldF8xMjM0NTY3ODkw  # paypal_secret_1234567890
  EMAIL_PASSWORD: ZW1haWxfcGFzc3dvcmRfMTIzNDU2Nzg5MA==  # email_password_1234567890
  SMS_API_KEY: c21zX2FwaV9rZXlfMTIzNDU2Nzg5MA==  # sms_api_key_1234567890
---
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secrets
  namespace: ticket-booking
type: Opaque
data:
  POSTGRES_PASSWORD: cG9zdGdyZXM=  # postgres
---
apiVersion: v1
kind: Secret
metadata:
  name: redis-secrets
  namespace: ticket-booking
type: Opaque
data:
  REDIS_PASSWORD: ""  # empty for no auth in development
---
# External Secrets for production (optional)
# apiVersion: external-secrets.io/v1beta1
# kind: SecretStore
# metadata:
#   name: aws-secrets-manager
#   namespace: ticket-booking
# spec:
#   provider:
#     aws:
#       service: SecretsManager
#       region: us-east-1
#       auth:
#         jwt:
#           serviceAccountRef:
#             name: external-secrets-sa
# ---
# apiVersion: external-secrets.io/v1beta1
# kind: ExternalSecret
# metadata:
#   name: backend-external-secrets
#   namespace: ticket-booking
# spec:
#   refreshInterval: 1h
#   secretStoreRef:
#     name: aws-secrets-manager
#     kind: SecretStore
#   target:
#     name: backend-secrets
#     creationPolicy: Owner
#   data:
#   - secretKey: DB_PASSWORD
#     remoteRef:
#       key: ticket-booking/database
#       property: password
#   - secretKey: JWT_SECRET
#     remoteRef:
#       key: ticket-booking/jwt
#       property: secret
