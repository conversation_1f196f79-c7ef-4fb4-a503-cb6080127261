"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventManagementSaga = exports.GenerateEventReportCommand = exports.UpdateSearchIndexCommand = exports.SyncToExternalAPICommand = void 0;
const common_1 = require("@nestjs/common");
const cqrs_1 = require("@nestjs/cqrs");
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
const base_saga_1 = require("../../../common/sagas/base.saga");
const event_created_event_1 = require("../events/event-created.event");
const event_published_event_1 = require("../events/event-published.event");
const event_cancelled_event_1 = require("../events/event-cancelled.event");
const seat_reserved_event_1 = require("../events/seat-reserved.event");
const send_notification_command_1 = require("../../bookings/commands/send-notification.command");
class SyncToExternalAPICommand {
    constructor(eventId, apiProvider, action) {
        this.eventId = eventId;
        this.apiProvider = apiProvider;
        this.action = action;
    }
}
exports.SyncToExternalAPICommand = SyncToExternalAPICommand;
class UpdateSearchIndexCommand {
    constructor(eventId, action) {
        this.eventId = eventId;
        this.action = action;
    }
}
exports.UpdateSearchIndexCommand = UpdateSearchIndexCommand;
class GenerateEventReportCommand {
    constructor(eventId, reportType) {
        this.eventId = eventId;
        this.reportType = reportType;
    }
}
exports.GenerateEventReportCommand = GenerateEventReportCommand;
let EventManagementSaga = class EventManagementSaga extends base_saga_1.BaseSaga {
    constructor() {
        super(...arguments);
        this.eventCreated = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)(event_created_event_1.EventCreatedEvent), (0, operators_1.map)((event) => {
                console.log(`🎪 Event Saga: Event created ${event.aggregateId}`);
                this.addStep({
                    stepId: 'sync-to-external-apis',
                    command: new SyncToExternalAPICommand(event.aggregateId, 'ticketmaster', 'create'),
                    maxRetries: 3
                });
                this.addStep({
                    stepId: 'update-search-index',
                    command: new UpdateSearchIndexCommand(event.aggregateId, 'index'),
                    maxRetries: 2
                });
                this.addStep({
                    stepId: 'notify-organizer-creation',
                    command: new send_notification_command_1.SendNotificationCommand(event.eventData.organizerId || 'system', send_notification_command_1.NotificationType.EMAIL, 'Event Created Successfully', 'Your event has been created and is ready for review.', {
                        eventId: event.aggregateId,
                        eventTitle: event.eventData.title,
                        eventDate: event.eventData.startDate
                    })
                });
                this.startSaga();
                return this.steps[0].command;
            }));
        };
        this.eventPublished = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)(event_published_event_1.EventPublishedEvent), (0, operators_1.map)((event) => {
                console.log(`📢 Event Saga: Event published ${event.aggregateId}`);
                this.addStep({
                    stepId: 'sync-published-status',
                    command: new SyncToExternalAPICommand(event.aggregateId, 'all', 'update'),
                    maxRetries: 3
                });
                this.addStep({
                    stepId: 'update-search-published',
                    command: new UpdateSearchIndexCommand(event.aggregateId, 'update'),
                    maxRetries: 2
                });
                this.addStep({
                    stepId: 'send-marketing-notifications',
                    command: new send_notification_command_1.SendNotificationCommand('marketing-team', send_notification_command_1.NotificationType.EMAIL, 'New Event Published', 'A new event is now available for booking.', {
                        eventId: event.aggregateId,
                        publishedAt: event.occurredOn
                    })
                });
                this.addStep({
                    stepId: 'notify-subscribers',
                    command: new send_notification_command_1.SendNotificationCommand('subscribers', send_notification_command_1.NotificationType.PUSH, 'New Event Available', 'A new event matching your interests is now available!', {
                        eventId: event.aggregateId
                    })
                });
                return this.steps[0].command;
            }));
        };
        this.eventCancelled = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)(event_cancelled_event_1.EventCancelledEvent), (0, operators_1.map)((event) => {
                console.log(`❌ Event Saga: Event cancelled ${event.aggregateId}`);
                this.addStep({
                    stepId: 'remove-from-external-apis',
                    command: new SyncToExternalAPICommand(event.aggregateId, 'all', 'delete'),
                    maxRetries: 3
                });
                this.addStep({
                    stepId: 'remove-from-search',
                    command: new UpdateSearchIndexCommand(event.aggregateId, 'delete'),
                    maxRetries: 2
                });
                this.addStep({
                    stepId: 'notify-ticket-holders',
                    command: new send_notification_command_1.SendNotificationCommand('all-ticket-holders', send_notification_command_1.NotificationType.EMAIL, 'Event Cancelled - Refund Information', 'We regret to inform you that this event has been cancelled. Refunds will be processed automatically.', {
                        eventId: event.aggregateId,
                        cancelledAt: event.occurredOn
                    })
                });
                this.addStep({
                    stepId: 'generate-cancellation-report',
                    command: new GenerateEventReportCommand(event.aggregateId, 'sales')
                });
                return this.steps[0].command;
            }));
        };
        this.seatReserved = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)(seat_reserved_event_1.SeatReservedEvent), (0, operators_1.delay)(500), (0, operators_1.map)((event) => {
                console.log(`🪑 Event Saga: Seats reserved for event ${event.aggregateId}`);
                return new SyncToExternalAPICommand(event.aggregateId, 'all', 'update');
            }));
        };
        this.eventCompleted = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)('EventCompletedEvent'), (0, operators_1.map)((event) => {
                console.log(`🏁 Event Saga: Event completed ${event.eventId}`);
                this.addStep({
                    stepId: 'generate-attendance-report',
                    command: new GenerateEventReportCommand(event.eventId, 'attendance')
                });
                this.addStep({
                    stepId: 'generate-analytics-report',
                    command: new GenerateEventReportCommand(event.eventId, 'analytics')
                });
                this.addStep({
                    stepId: 'send-feedback-request',
                    command: new send_notification_command_1.SendNotificationCommand('attendees', send_notification_command_1.NotificationType.EMAIL, 'How was your experience?', 'We would love to hear your feedback about the event.', {
                        eventId: event.eventId,
                        feedbackUrl: `https://feedback.example.com/${event.eventId}`
                    })
                });
                this.addStep({
                    stepId: 'archive-event-data',
                    command: new SyncToExternalAPICommand(event.eventId, 'archive', 'create')
                });
                return this.steps[0].command;
            }));
        };
    }
    handle(event) {
        switch (event.constructor.name) {
            case event_created_event_1.EventCreatedEvent.name:
                return this.eventCreated(new rxjs_1.Observable(observer => observer.next(event)));
            case event_published_event_1.EventPublishedEvent.name:
                return this.eventPublished(new rxjs_1.Observable(observer => observer.next(event)));
            case event_cancelled_event_1.EventCancelledEvent.name:
                return this.eventCancelled(new rxjs_1.Observable(observer => observer.next(event)));
            case seat_reserved_event_1.SeatReservedEvent.name:
                return this.seatReserved(new rxjs_1.Observable(observer => observer.next(event)));
            case 'EventCompletedEvent':
                return this.eventCompleted(new rxjs_1.Observable(observer => observer.next(event)));
            default:
                return new rxjs_1.Observable(observer => observer.complete());
        }
    }
};
exports.EventManagementSaga = EventManagementSaga;
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], EventManagementSaga.prototype, "eventCreated", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], EventManagementSaga.prototype, "eventPublished", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], EventManagementSaga.prototype, "eventCancelled", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], EventManagementSaga.prototype, "seatReserved", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], EventManagementSaga.prototype, "eventCompleted", void 0);
exports.EventManagementSaga = EventManagementSaga = __decorate([
    (0, common_1.Injectable)()
], EventManagementSaga);
//# sourceMappingURL=event-management.saga.js.map