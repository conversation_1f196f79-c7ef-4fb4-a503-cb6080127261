import { Command } from '../../../common/commands/base.command';
export declare enum NotificationType {
    EMAIL = "EMAIL",
    SMS = "SMS",
    PUSH = "PUSH"
}
export declare class SendNotificationCommand extends Command {
    readonly recipient: string;
    readonly type: NotificationType;
    readonly subject: string;
    readonly message: string;
    readonly templateData?: any;
    constructor(recipient: string, type: NotificationType, subject: string, message: string, templateData?: any);
}
