apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ticket-booking-ingress
  namespace: ticket-booking
  annotations:
    # AWS Load Balancer Controller annotations
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/healthcheck-path: /api/v1/health
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '30'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '5'
    alb.ingress.kubernetes.io/healthy-threshold-count: '2'
    alb.ingress.kubernetes.io/unhealthy-threshold-count: '3'
    # Certificate ARN (update with your certificate)
    # alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:************:certificate/your-cert-id
    # Load balancer attributes
    alb.ingress.kubernetes.io/load-balancer-attributes: |
      idle_timeout.timeout_seconds=60,
      routing.http2.enabled=true,
      access_logs.s3.enabled=true,
      access_logs.s3.bucket=ticket-booking-alb-logs,
      access_logs.s3.prefix=alb-logs
    # Tags
    alb.ingress.kubernetes.io/tags: |
      Environment=dev,
      Project=ticket-booking,
      ManagedBy=kubernetes
spec:
  rules:
  - host: ticket-booking.example.com  # Update with your domain
    http:
      paths:
      # Backend API routes
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 3001
      - path: /socket.io
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 3001
      # Frontend routes (catch-all)
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-service
            port:
              number: 3000
  # Default backend for unmatched routes
  defaultBackend:
    service:
      name: frontend-service
      port:
        number: 3000
---
# Alternative: NGINX Ingress (if preferred over ALB)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ticket-booking-nginx-ingress
  namespace: ticket-booking
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "60"
    # Rate limiting
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    # CORS
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
spec:
  tls:
  - hosts:
    - ticket-booking.example.com
    secretName: ticket-booking-tls
  rules:
  - host: ticket-booking.example.com
    http:
      paths:
      # Backend API routes
      - path: /api(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 3001
      - path: /socket.io(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: backend-service
            port:
              number: 3001
      # Frontend routes
      - path: /(.*)
        pathType: Prefix
        backend:
          service:
            name: frontend-service
            port:
              number: 3000
---
# TLS Certificate (if using cert-manager)
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: ticket-booking-tls
  namespace: ticket-booking
spec:
  secretName: ticket-booking-tls
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - ticket-booking.example.com
---
# ClusterIssuer for Let's Encrypt (if using cert-manager)
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>  # Update with your email
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
