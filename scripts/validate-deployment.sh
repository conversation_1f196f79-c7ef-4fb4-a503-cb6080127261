#!/bin/bash

# Deployment Validation Script
# Checks if everything is ready for AWS deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Validating Deployment Readiness${NC}"
echo -e "${BLUE}===================================${NC}"

# Track validation status
validation_passed=true

# Function to check if command exists
check_command() {
    if command -v $1 &> /dev/null; then
        echo -e "${GREEN}✅ $1 is installed${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 is not installed${NC}"
        validation_passed=false
        return 1
    fi
}

# Function to check file exists
check_file() {
    if [ -f "$1" ]; then
        echo -e "${GREEN}✅ $1 exists${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 is missing${NC}"
        validation_passed=false
        return 1
    fi
}

# Function to check directory exists
check_directory() {
    if [ -d "$1" ]; then
        echo -e "${GREEN}✅ $1 directory exists${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 directory is missing${NC}"
        validation_passed=false
        return 1
    fi
}

echo -e "\n${BLUE}📋 Checking Prerequisites${NC}"
echo -e "${BLUE}=========================${NC}"

# Check required commands
check_command "aws"
check_command "terraform"
check_command "docker"
check_command "node"
check_command "npm"

echo -e "\n${BLUE}🔐 Checking AWS Configuration${NC}"
echo -e "${BLUE}=============================${NC}"

# Check AWS credentials
if aws sts get-caller-identity &> /dev/null; then
    AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
    AWS_REGION=$(aws configure get region)
    echo -e "${GREEN}✅ AWS credentials configured${NC}"
    echo -e "${BLUE}   Account ID: ${AWS_ACCOUNT_ID}${NC}"
    echo -e "${BLUE}   Region: ${AWS_REGION:-us-east-1}${NC}"
else
    echo -e "${RED}❌ AWS credentials not configured${NC}"
    echo -e "${YELLOW}   Run: aws configure${NC}"
    validation_passed=false
fi

echo -e "\n${BLUE}📁 Checking Project Structure${NC}"
echo -e "${BLUE}=============================${NC}"

# Check main directories
check_directory "backend"
check_directory "frontend"
check_directory "infrastructure"
check_directory "infrastructure/terraform"
check_directory "infrastructure/terraform/modules"
check_directory "scripts"

echo -e "\n${BLUE}📄 Checking Configuration Files${NC}"
echo -e "${BLUE}===============================${NC}"

# Check Terraform files
check_file "infrastructure/terraform/main.tf"
check_file "infrastructure/terraform/variables.tf"
check_file "infrastructure/terraform/outputs.tf"
check_file "infrastructure/terraform/environments/free-tier.tfvars"
check_file "infrastructure/terraform/environments/ultra-free-tier.tfvars"

# Check Docker files
check_file "backend/Dockerfile"
check_file "frontend/Dockerfile"
check_file "backend/package.json"
check_file "frontend/package.json"

# Check deployment scripts
check_file "scripts/deploy-free-tier.sh"
check_file "scripts/monitor-costs.sh"

echo -e "\n${BLUE}🏗️ Checking Terraform Modules${NC}"
echo -e "${BLUE}=============================${NC}"

# Check Terraform modules
check_directory "infrastructure/terraform/modules/vpc"
check_directory "infrastructure/terraform/modules/security"
check_directory "infrastructure/terraform/modules/rds"
check_directory "infrastructure/terraform/modules/elasticache"
check_directory "infrastructure/terraform/modules/ecs"
check_directory "infrastructure/terraform/modules/alb"

# Check module files
check_file "infrastructure/terraform/modules/vpc/main.tf"
check_file "infrastructure/terraform/modules/security/main.tf"
check_file "infrastructure/terraform/modules/rds/main.tf"
check_file "infrastructure/terraform/modules/elasticache/main.tf"

echo -e "\n${BLUE}🐳 Checking Docker Setup${NC}"
echo -e "${BLUE}========================${NC}"

# Check if Docker is running
if docker info &> /dev/null; then
    echo -e "${GREEN}✅ Docker is running${NC}"
else
    echo -e "${RED}❌ Docker is not running${NC}"
    echo -e "${YELLOW}   Run: sudo systemctl start docker${NC}"
    validation_passed=false
fi

# Check Docker permissions
if docker ps &> /dev/null; then
    echo -e "${GREEN}✅ Docker permissions OK${NC}"
else
    echo -e "${YELLOW}⚠️  Docker permission issue${NC}"
    echo -e "${YELLOW}   Run: sudo usermod -aG docker \$USER && newgrp docker${NC}"
fi

echo -e "\n${BLUE}📦 Checking Node.js Projects${NC}"
echo -e "${BLUE}============================${NC}"

# Check backend dependencies
if [ -f "backend/package.json" ]; then
    cd backend
    if [ -d "node_modules" ]; then
        echo -e "${GREEN}✅ Backend dependencies installed${NC}"
    else
        echo -e "${YELLOW}⚠️  Backend dependencies not installed${NC}"
        echo -e "${YELLOW}   Run: cd backend && npm install${NC}"
    fi
    cd ..
fi

# Check frontend dependencies
if [ -f "frontend/package.json" ]; then
    cd frontend
    if [ -d "node_modules" ]; then
        echo -e "${GREEN}✅ Frontend dependencies installed${NC}"
    else
        echo -e "${YELLOW}⚠️  Frontend dependencies not installed${NC}"
        echo -e "${YELLOW}   Run: cd frontend && npm install${NC}"
    fi
    cd ..
fi

echo -e "\n${BLUE}💾 Checking Available Space${NC}"
echo -e "${BLUE}===========================${NC}"

# Check disk space
available_space=$(df -BG . | awk 'NR==2 {print $4}' | sed 's/G//')
if [ "$available_space" -gt 5 ]; then
    echo -e "${GREEN}✅ Sufficient disk space: ${available_space}GB available${NC}"
else
    echo -e "${RED}❌ Low disk space: ${available_space}GB available${NC}"
    echo -e "${YELLOW}   Need at least 5GB for Docker builds${NC}"
    validation_passed=false
fi

echo -e "\n${BLUE}🔍 Checking AWS Free Tier Eligibility${NC}"
echo -e "${BLUE}====================================${NC}"

if aws sts get-caller-identity &> /dev/null; then
    # Check if account is new enough for free tier
    account_age=$(aws support describe-cases --query 'cases[0].timeCreated' --output text 2>/dev/null || echo "unknown")
    echo -e "${BLUE}   Account status: Active${NC}"
    echo -e "${YELLOW}   ⚠️  Verify free tier eligibility in AWS billing dashboard${NC}"
    echo -e "${YELLOW}   ⚠️  Free tier is available for 12 months from account creation${NC}"
fi

echo -e "\n${BLUE}📋 Validation Summary${NC}"
echo -e "${BLUE}=====================${NC}"

if [ "$validation_passed" = true ]; then
    echo -e "${GREEN}🎉 All validations passed! Ready to deploy.${NC}"
    echo -e "\n${BLUE}Next steps:${NC}"
    echo -e "${GREEN}1. Run: ./scripts/deploy-free-tier.sh${NC}"
    echo -e "${GREEN}2. Or follow manual steps in DEPLOY-NOW.md${NC}"
    echo -e "${GREEN}3. Monitor costs with: ./scripts/monitor-costs.sh${NC}"
    exit 0
else
    echo -e "${RED}❌ Some validations failed. Please fix the issues above.${NC}"
    echo -e "\n${YELLOW}Common fixes:${NC}"
    echo -e "${YELLOW}• Install missing tools: aws, terraform, docker${NC}"
    echo -e "${YELLOW}• Configure AWS: aws configure${NC}"
    echo -e "${YELLOW}• Start Docker: sudo systemctl start docker${NC}"
    echo -e "${YELLOW}• Install dependencies: npm install${NC}"
    exit 1
fi
