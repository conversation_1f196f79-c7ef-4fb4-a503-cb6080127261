"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentProcessingSaga = void 0;
const common_1 = require("@nestjs/common");
const cqrs_1 = require("@nestjs/cqrs");
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
const base_saga_1 = require("../../../common/sagas/base.saga");
const payment_initiated_event_1 = require("../events/payment-initiated.event");
const payment_succeeded_event_1 = require("../events/payment-succeeded.event");
const payment_failed_event_1 = require("../events/payment-failed.event");
const charge_payment_command_1 = require("../commands/charge-payment.command");
const refund_payment_command_1 = require("../commands/refund-payment.command");
const process_payment_command_1 = require("../../bookings/commands/process-payment.command");
const cancel_booking_command_1 = require("../../bookings/commands/cancel-booking.command");
const send_notification_command_1 = require("../../bookings/commands/send-notification.command");
let PaymentProcessingSaga = class PaymentProcessingSaga extends base_saga_1.BaseSaga {
    constructor() {
        super(...arguments);
        this.paymentInitiated = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)(payment_initiated_event_1.PaymentInitiatedEvent), (0, operators_1.map)((event) => {
                console.log(`💳 Payment Saga: Payment initiated for ${event.aggregateId}`);
                this.addStep({
                    stepId: 'charge-payment',
                    command: new charge_payment_command_1.ChargePaymentCommand(event.aggregateId, event.paymentDetails),
                    compensationCommand: new cancel_booking_command_1.CancelBookingCommand(event.bookingId, 'Payment processing failed'),
                    maxRetries: 3
                });
                this.startSaga();
                return this.steps[0].command;
            }), (0, operators_1.catchError)((error) => {
                console.error('Payment initiation failed:', error);
                return new rxjs_1.Observable(observer => observer.next(new cancel_booking_command_1.CancelBookingCommand('unknown', 'Payment initiation error')));
            }));
        };
        this.paymentSucceeded = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)(payment_succeeded_event_1.PaymentSucceededEvent), (0, operators_1.map)((event) => {
                console.log(`✅ Payment Saga: Payment succeeded for ${event.aggregateId}`);
                this.addStep({
                    stepId: 'update-booking-payment',
                    command: new process_payment_command_1.ProcessPaymentCommand(event.bookingId, {
                        paymentId: event.aggregateId,
                        paymentMethod: 'stripe',
                        amount: event.amount,
                        currency: event.currency,
                        transactionId: event.transactionId
                    }),
                    compensationCommand: new refund_payment_command_1.RefundPaymentCommand(event.aggregateId, event.amount, 'Booking update failed'),
                    maxRetries: 3
                });
                this.addStep({
                    stepId: 'send-payment-success-notification',
                    command: new send_notification_command_1.SendNotificationCommand(event.bookingId, send_notification_command_1.NotificationType.EMAIL, 'Payment Successful', 'Your payment has been processed successfully.', {
                        paymentId: event.aggregateId,
                        transactionId: event.transactionId,
                        amount: event.amount,
                        currency: event.currency
                    })
                });
                return this.steps[this.steps.length - 2].command;
            }));
        };
        this.paymentFailed = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)(payment_failed_event_1.PaymentFailedEvent), (0, operators_1.delay)(1000), (0, operators_1.map)((event) => {
                console.log(`❌ Payment Saga: Payment failed for ${event.aggregateId}`);
                this.addStep({
                    stepId: 'cancel-booking-payment-failed',
                    command: new cancel_booking_command_1.CancelBookingCommand(event.bookingId, `Payment failed: ${event.errorMessage}`),
                    maxRetries: 3
                });
                this.addStep({
                    stepId: 'send-payment-failure-notification',
                    command: new send_notification_command_1.SendNotificationCommand(event.bookingId, send_notification_command_1.NotificationType.EMAIL, 'Payment Failed', 'Your payment could not be processed. Please try again.', {
                        paymentId: event.aggregateId,
                        errorCode: event.errorCode,
                        errorMessage: event.errorMessage,
                        bookingId: event.bookingId
                    })
                });
                return this.steps[0].command;
            }));
        };
        this.refundInitiated = (events$) => {
            return events$.pipe((0, cqrs_1.ofType)('RefundInitiatedEvent'), (0, operators_1.map)((event) => {
                console.log(`💰 Payment Saga: Refund initiated for ${event.paymentId}`);
                this.addStep({
                    stepId: 'process-refund',
                    command: new refund_payment_command_1.RefundPaymentCommand(event.paymentId, event.refundAmount, event.reason),
                    maxRetries: 3
                });
                this.addStep({
                    stepId: 'send-refund-notification',
                    command: new send_notification_command_1.SendNotificationCommand(event.bookingId, send_notification_command_1.NotificationType.EMAIL, 'Refund Processed', 'Your refund has been processed and will appear in your account within 3-5 business days.', {
                        paymentId: event.paymentId,
                        refundAmount: event.refundAmount,
                        reason: event.reason
                    })
                });
                return this.steps[0].command;
            }));
        };
    }
    handle(event) {
        switch (event.constructor.name) {
            case payment_initiated_event_1.PaymentInitiatedEvent.name:
                return this.paymentInitiated(new rxjs_1.Observable(observer => observer.next(event)));
            case payment_succeeded_event_1.PaymentSucceededEvent.name:
                return this.paymentSucceeded(new rxjs_1.Observable(observer => observer.next(event)));
            case payment_failed_event_1.PaymentFailedEvent.name:
                return this.paymentFailed(new rxjs_1.Observable(observer => observer.next(event)));
            case 'RefundInitiatedEvent':
                return this.refundInitiated(new rxjs_1.Observable(observer => observer.next(event)));
            default:
                return new rxjs_1.Observable(observer => observer.complete());
        }
    }
};
exports.PaymentProcessingSaga = PaymentProcessingSaga;
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], PaymentProcessingSaga.prototype, "paymentInitiated", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], PaymentProcessingSaga.prototype, "paymentSucceeded", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], PaymentProcessingSaga.prototype, "paymentFailed", void 0);
__decorate([
    (0, cqrs_1.Saga)(),
    __metadata("design:type", Object)
], PaymentProcessingSaga.prototype, "refundInitiated", void 0);
exports.PaymentProcessingSaga = PaymentProcessingSaga = __decorate([
    (0, common_1.Injectable)()
], PaymentProcessingSaga);
//# sourceMappingURL=payment-processing.saga.js.map