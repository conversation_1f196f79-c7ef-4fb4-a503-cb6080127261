"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentProcessedEvent = void 0;
const base_event_1 = require("../../../common/events/base.event");
class PaymentProcessedEvent extends base_event_1.DomainEvent {
    constructor(aggregateId, paymentData, version = 1) {
        super(aggregateId, version);
        this.aggregateId = aggregateId;
        this.paymentData = paymentData;
    }
}
exports.PaymentProcessedEvent = PaymentProcessedEvent;
//# sourceMappingURL=payment-processed.event.js.map