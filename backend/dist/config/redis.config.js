"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisConfig = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const ioredis_1 = require("ioredis");
let RedisConfig = class RedisConfig {
    constructor(configService) {
        this.configService = configService;
        this.redisClient = new ioredis_1.default({
            host: this.configService.get('REDIS_HOST', 'localhost'),
            port: this.configService.get('REDIS_PORT', 6379),
            password: this.configService.get('REDIS_PASSWORD'),
            db: this.configService.get('REDIS_DB', 0),
            maxRetriesPerRequest: 3,
            lazyConnect: true,
        });
        this.redisClient.on('connect', () => {
            console.log('✅ Redis connected successfully');
        });
        this.redisClient.on('error', (error) => {
            console.error('❌ Redis connection error:', error);
        });
    }
    getClient() {
        return this.redisClient;
    }
    async set(key, value, ttl) {
        if (ttl) {
            await this.redisClient.setex(key, ttl, value);
        }
        else {
            await this.redisClient.set(key, value);
        }
    }
    async get(key) {
        return await this.redisClient.get(key);
    }
    async del(key) {
        await this.redisClient.del(key);
    }
    async exists(key) {
        const result = await this.redisClient.exists(key);
        return result === 1;
    }
    async setHash(key, field, value) {
        await this.redisClient.hset(key, field, value);
    }
    async getHash(key, field) {
        return await this.redisClient.hget(key, field);
    }
    async getAllHash(key) {
        return await this.redisClient.hgetall(key);
    }
};
exports.RedisConfig = RedisConfig;
exports.RedisConfig = RedisConfig = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], RedisConfig);
//# sourceMappingURL=redis.config.js.map