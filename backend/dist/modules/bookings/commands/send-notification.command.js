"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SendNotificationCommand = exports.NotificationType = void 0;
const base_command_1 = require("../../../common/commands/base.command");
var NotificationType;
(function (NotificationType) {
    NotificationType["EMAIL"] = "EMAIL";
    NotificationType["SMS"] = "SMS";
    NotificationType["PUSH"] = "PUSH";
})(NotificationType || (exports.NotificationType = NotificationType = {}));
class SendNotificationCommand extends base_command_1.Command {
    constructor(recipient, type, subject, message, templateData) {
        super();
        this.recipient = recipient;
        this.type = type;
        this.subject = subject;
        this.message = message;
        this.templateData = templateData;
    }
}
exports.SendNotificationCommand = SendNotificationCommand;
//# sourceMappingURL=send-notification.command.js.map