"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CancelBookingCommand = void 0;
const base_command_1 = require("../../../common/commands/base.command");
class CancelBookingCommand extends base_command_1.Command {
    constructor(bookingId, reason) {
        super();
        this.bookingId = bookingId;
        this.reason = reason;
    }
}
exports.CancelBookingCommand = CancelBookingCommand;
//# sourceMappingURL=cancel-booking.command.js.map