import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON><PERSON>, IsBoolean, <PERSON>, <PERSON>, IsObject } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class SearchEventsDto {
  @ApiProperty({
    description: 'Search query text',
    example: 'rock concert in New York this weekend',
  })
  @IsString()
  query: string;

  @ApiPropertyOptional({
    description: 'Number of results to return',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  topK?: number = 10;

  @ApiPropertyOptional({
    description: 'Include metadata in results',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  includeMetadata?: boolean = true;

  @ApiPropertyOptional({
    description: 'Filter criteria',
    example: { category: 'music', price: { $lte: 100 } },
  })
  @IsOptional()
  @IsObject()
  filter?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Namespace to search in',
    example: 'production',
  })
  @IsOptional()
  @IsString()
  namespace?: string;
}

export class SimilarEventsDto {
  @ApiProperty({
    description: 'Event ID to find similar events for',
    example: 'event-123',
  })
  @IsString()
  eventId: string;

  @ApiPropertyOptional({
    description: 'Number of similar events to return',
    example: 5,
    minimum: 1,
    maximum: 50,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number = 5;

  @ApiPropertyOptional({
    description: 'Minimum similarity threshold',
    example: 0.7,
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  threshold?: number = 0.7;

  @ApiPropertyOptional({
    description: 'Include metadata in results',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  includeMetadata?: boolean = true;
}

export class RecommendationsDto {
  @ApiProperty({
    description: 'User ID to get recommendations for',
    example: 'user-456',
  })
  @IsString()
  userId: string;

  @ApiPropertyOptional({
    description: 'Number of recommendations to return',
    example: 10,
    minimum: 1,
    maximum: 50,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Filter by event categories',
    example: ['music', 'sports'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  categories?: string[];

  @ApiPropertyOptional({
    description: 'Price range filter',
    example: { min: 20, max: 200 },
  })
  @IsOptional()
  @IsObject()
  priceRange?: { min: number; max: number };

  @ApiPropertyOptional({
    description: 'Date range filter',
    example: { start: '2024-01-01', end: '2024-12-31' },
  })
  @IsOptional()
  @IsObject()
  dateRange?: { start: string; end: string };

  @ApiPropertyOptional({
    description: 'Location filter',
    example: 'New York, NY',
  })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiPropertyOptional({
    description: 'Event IDs to exclude from recommendations',
    example: ['event-123', 'event-456'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  excludeEventIds?: string[];
}

export class SearchResultDto {
  @ApiProperty({
    description: 'Result ID',
    example: 'event-123',
  })
  id: string;

  @ApiProperty({
    description: 'Similarity score',
    example: 0.95,
  })
  score: number;

  @ApiPropertyOptional({
    description: 'Result metadata',
  })
  metadata?: any;
}

export class RecommendationResultDto {
  @ApiProperty({
    description: 'Event ID',
    example: 'event-123',
  })
  eventId: string;

  @ApiProperty({
    description: 'Recommendation score',
    example: 0.92,
  })
  score: number;

  @ApiProperty({
    description: 'Reason for recommendation',
    example: 'Perfect match for your preferences',
  })
  reason: string;

  @ApiProperty({
    description: 'Confidence level',
    example: 0.88,
  })
  confidence: number;

  @ApiPropertyOptional({
    description: 'Event metadata',
  })
  metadata?: any;
}
