"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IntegrationEvent = exports.DomainEvent = exports.BaseEvent = void 0;
class BaseEvent {
    constructor(aggregateId, version = 1) {
        this.aggregateId = aggregateId;
        this.eventId = this.generateEventId();
        this.eventType = this.constructor.name;
        this.occurredOn = new Date();
        this.version = version;
    }
    generateEventId() {
        return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
}
exports.BaseEvent = BaseEvent;
class DomainEvent extends BaseEvent {
    constructor(aggregateId, version = 1) {
        super(aggregateId, version);
    }
}
exports.DomainEvent = DomainEvent;
class IntegrationEvent extends BaseEvent {
    constructor(aggregateId, version = 1) {
        super(aggregateId, version);
    }
}
exports.IntegrationEvent = IntegrationEvent;
//# sourceMappingURL=base.event.js.map