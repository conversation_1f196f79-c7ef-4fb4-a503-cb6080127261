import { AggregateRoot } from '../../../common/entities/aggregate-root';
import { BaseEvent } from '../../../common/events/base.event';
import { BookingInitiatedEvent } from '../events/booking-initiated.event';
import { SeatsReservedEvent } from '../events/seats-reserved.event';
import { PaymentProcessedEvent } from '../events/payment-processed.event';
import { BookingConfirmedEvent } from '../events/booking-confirmed.event';
import { BookingCancelledEvent } from '../events/booking-cancelled.event';
import { BookingExpiredEvent } from '../events/booking-expired.event';
import { RefundProcessedEvent } from '../events/refund-processed.event';

export enum BookingStatus {
  INITIATED = 'INITIATED',
  SEATS_RESERVED = 'SEATS_RESERVED',
  PAYMENT_PENDING = 'PAYMENT_PENDING',
  PAYMENT_PROCESSING = 'PAYMENT_PROCESSING',
  CONFIRMED = 'CONFIRMED',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED',
  REFUNDED = 'REFUNDED'
}

export interface BookingData {
  eventId: string;
  customerId: string;
  seatIds: string[];
  totalAmount: number;
  currency: string;
  customerEmail: string;
  customerPhone?: string;
}

export interface PaymentData {
  paymentId: string;
  paymentMethod: string;
  amount: number;
  currency: string;
  transactionId?: string;
}

export class BookingAggregate extends AggregateRoot {
  private _eventId: string;
  private _customerId: string;
  private _seatIds: string[] = [];
  private _totalAmount: number;
  private _currency: string;
  private _customerEmail: string;
  private _customerPhone?: string;
  private _status: BookingStatus;
  private _paymentData?: PaymentData;
  private _reservationExpiresAt?: Date;
  private _confirmedAt?: Date;
  private _cancelledAt?: Date;
  private _refundAmount?: number;

  constructor(id?: string) {
    super(id);
    this._status = BookingStatus.INITIATED;
  }

  static initiate(bookingData: BookingData): BookingAggregate {
    const booking = new BookingAggregate();
    booking.apply(new BookingInitiatedEvent(
      booking.id,
      bookingData,
      booking.version + 1
    ));
    return booking;
  }

  reserveSeats(expirationMinutes: number = 15): void {
    if (this._status !== BookingStatus.INITIATED) {
      throw new Error('Can only reserve seats for initiated bookings');
    }

    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + expirationMinutes);

    this.apply(new SeatsReservedEvent(
      this.id,
      this._eventId,
      this._seatIds,
      expiresAt,
      this.version + 1
    ));
  }

  processPayment(paymentData: PaymentData): void {
    if (this._status !== BookingStatus.SEATS_RESERVED && this._status !== BookingStatus.PAYMENT_PENDING) {
      throw new Error('Can only process payment for bookings with reserved seats');
    }

    if (this._reservationExpiresAt && new Date() > this._reservationExpiresAt) {
      throw new Error('Seat reservation has expired');
    }

    this.apply(new PaymentProcessedEvent(
      this.id,
      paymentData,
      this.version + 1
    ));
  }

  confirm(): void {
    if (this._status !== BookingStatus.PAYMENT_PROCESSING) {
      throw new Error('Can only confirm bookings with processed payment');
    }

    this.apply(new BookingConfirmedEvent(
      this.id,
      new Date(),
      this.version + 1
    ));
  }

  cancel(reason?: string): void {
    if (this._status === BookingStatus.CANCELLED || 
        this._status === BookingStatus.EXPIRED || 
        this._status === BookingStatus.REFUNDED) {
      throw new Error('Booking is already cancelled, expired, or refunded');
    }

    this.apply(new BookingCancelledEvent(
      this.id,
      reason,
      new Date(),
      this.version + 1
    ));
  }

  expire(): void {
    if (this._status !== BookingStatus.SEATS_RESERVED && this._status !== BookingStatus.PAYMENT_PENDING) {
      throw new Error('Can only expire bookings with reserved seats');
    }

    this.apply(new BookingExpiredEvent(
      this.id,
      new Date(),
      this.version + 1
    ));
  }

  processRefund(refundAmount: number, refundId: string): void {
    if (this._status !== BookingStatus.CONFIRMED) {
      throw new Error('Can only refund confirmed bookings');
    }

    this.apply(new RefundProcessedEvent(
      this.id,
      refundAmount,
      refundId,
      new Date(),
      this.version + 1
    ));
  }

  protected applyEvent(event: BaseEvent): void {
    switch (event.constructor.name) {
      case BookingInitiatedEvent.name:
        this.onBookingInitiated(event as BookingInitiatedEvent);
        break;
      case SeatsReservedEvent.name:
        this.onSeatsReserved(event as SeatsReservedEvent);
        break;
      case PaymentProcessedEvent.name:
        this.onPaymentProcessed(event as PaymentProcessedEvent);
        break;
      case BookingConfirmedEvent.name:
        this.onBookingConfirmed(event as BookingConfirmedEvent);
        break;
      case BookingCancelledEvent.name:
        this.onBookingCancelled(event as BookingCancelledEvent);
        break;
      case BookingExpiredEvent.name:
        this.onBookingExpired(event as BookingExpiredEvent);
        break;
      case RefundProcessedEvent.name:
        this.onRefundProcessed(event as RefundProcessedEvent);
        break;
    }
  }

  private onBookingInitiated(event: BookingInitiatedEvent): void {
    this._eventId = event.bookingData.eventId;
    this._customerId = event.bookingData.customerId;
    this._seatIds = event.bookingData.seatIds;
    this._totalAmount = event.bookingData.totalAmount;
    this._currency = event.bookingData.currency;
    this._customerEmail = event.bookingData.customerEmail;
    this._customerPhone = event.bookingData.customerPhone;
    this._status = BookingStatus.INITIATED;
  }

  private onSeatsReserved(event: SeatsReservedEvent): void {
    this._status = BookingStatus.SEATS_RESERVED;
    this._reservationExpiresAt = event.expiresAt;
  }

  private onPaymentProcessed(event: PaymentProcessedEvent): void {
    this._status = BookingStatus.PAYMENT_PROCESSING;
    this._paymentData = event.paymentData;
  }

  private onBookingConfirmed(event: BookingConfirmedEvent): void {
    this._status = BookingStatus.CONFIRMED;
    this._confirmedAt = event.confirmedAt;
  }

  private onBookingCancelled(event: BookingCancelledEvent): void {
    this._status = BookingStatus.CANCELLED;
    this._cancelledAt = event.cancelledAt;
  }

  private onBookingExpired(event: BookingExpiredEvent): void {
    this._status = BookingStatus.EXPIRED;
  }

  private onRefundProcessed(event: RefundProcessedEvent): void {
    this._status = BookingStatus.REFUNDED;
    this._refundAmount = event.refundAmount;
  }

  // Getters
  get eventId(): string { return this._eventId; }
  get customerId(): string { return this._customerId; }
  get seatIds(): string[] { return [...this._seatIds]; }
  get totalAmount(): number { return this._totalAmount; }
  get currency(): string { return this._currency; }
  get customerEmail(): string { return this._customerEmail; }
  get customerPhone(): string | undefined { return this._customerPhone; }
  get status(): BookingStatus { return this._status; }
  get paymentData(): PaymentData | undefined { return this._paymentData; }
  get reservationExpiresAt(): Date | undefined { return this._reservationExpiresAt; }
  get confirmedAt(): Date | undefined { return this._confirmedAt; }
  get cancelledAt(): Date | undefined { return this._cancelledAt; }
  get refundAmount(): number | undefined { return this._refundAmount; }
  get isExpired(): boolean {
    return this._reservationExpiresAt ? new Date() > this._reservationExpiresAt : false;
  }
}
