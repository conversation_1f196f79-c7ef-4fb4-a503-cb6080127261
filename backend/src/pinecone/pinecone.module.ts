import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PineconeService } from './services/pinecone.service';
import { PineconeController } from './controllers/pinecone.controller';
import { EventsModule } from '../events/events.module';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [
    ConfigModule,
    EventsModule,
    UsersModule,
  ],
  controllers: [PineconeController],
  providers: [PineconeService],
  exports: [PineconeService],
})
export class PineconeModule {}
