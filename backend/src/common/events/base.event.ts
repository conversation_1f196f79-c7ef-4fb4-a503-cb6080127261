import { IEvent } from '@nestjs/cqrs';

export abstract class BaseEvent implements IEvent {
  public readonly aggregateId: string;
  public readonly eventId: string;
  public readonly eventType: string;
  public readonly occurredOn: Date;
  public readonly version: number;

  constructor(aggregateId: string, version: number = 1) {
    this.aggregateId = aggregateId;
    this.eventId = this.generateEventId();
    this.eventType = this.constructor.name;
    this.occurredOn = new Date();
    this.version = version;
  }

  private generateEventId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

export abstract class DomainEvent extends BaseEvent {
  constructor(aggregateId: string, version: number = 1) {
    super(aggregateId, version);
  }
}

export abstract class IntegrationEvent extends BaseEvent {
  constructor(aggregateId: string, version: number = 1) {
    super(aggregateId, version);
  }
}
